import mysql.connector
from datetime import datetime
from passlib.context import CryptContext
import os

# Database connection details
db_config = {
    "user": "root",
    "password": "123",
    "host": "127.0.0.1",
    "database": "m1",
}

# Connect to the database
conn = mysql.connector.connect(**db_config)
cursor = conn.cursor()

# Query to fetch all patients
fetch_patients_query = "SELECT id, first_name, last_name, phone_number FROM patient"
cursor.execute(fetch_patients_query)
patients = cursor.fetchall()

# Query to check if an email exists
check_email_query = "SELECT COUNT(*) FROM users WHERE email = %s"

# Query to insert a new user
insert_user_query = """
INSERT INTO users (first_name, last_name, email, password, mobile_number, is_active, created_at, updated_at)
VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
"""

# Query to get hospital_file_no from case table
fetch_hospital_file_no_query = """
SELECT hospital_file_no FROM `case` WHERE patient_id = %s
"""

# Initialize CryptContext
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


# Function to generate a unique email
def generate_unique_email(first_name, last_name, suffix=""):
    base_email = f"{first_name.lower()}.{last_name.lower()}{suffix}@arogyadoot.com"
    email = base_email
    counter = 1
    cursor.execute(check_email_query, (email,))
    while cursor.fetchone()[0] > 0:
        email = (
            f"{first_name.lower()}.{last_name.lower()}{suffix}{counter}@arogyadoot.com"
        )
        cursor.execute(check_email_query, (email,))
        counter += 1
    return email


# Iterate through each patient and create a corresponding user for each hospital_file_no
for patient in patients:
    patient_id, first_name, last_name, phone_number = patient

    # Fetch hospital_file_no for the patient
    cursor.execute(fetch_hospital_file_no_query, (patient_id,))
    hospital_file_no_results = cursor.fetchall()

    for idx, hospital_file_no_result in enumerate(hospital_file_no_results):
        hospital_file_no = hospital_file_no_result[0]
        email_suffix = f".{idx}" if len(hospital_file_no_results) > 1 else ""
        email = generate_unique_email(first_name, last_name, email_suffix)
        password = (
            hospital_file_no if hospital_file_no else "default_password"
        )  # Use hospital_file_no as password if available
        hashed_password = pwd_context.hash(password)  # Hash the password
        is_active = True
        created_at = datetime.utcnow()
        updated_at = datetime.utcnow()

        # Insert the new user into the users table
        cursor.execute(
            insert_user_query,
            (
                first_name,
                last_name,
                email,
                hashed_password,
                phone_number,
                is_active,
                created_at,
                updated_at,
            ),
        )

# Commit the transaction
conn.commit()

# Close the cursor and connection
cursor.close()
conn.close()
