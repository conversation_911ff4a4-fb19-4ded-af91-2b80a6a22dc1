import os
from jinja2 import Environment, FileSystemLoader


class HTMLParser:
    """A class to parse HTML templates."""

    def __init__(self, template_path: str):
        """Initialize the HTMLParser object."""
        if os.path.exists(template_path):
            self.file_loader = FileSystemLoader(template_path)
            self.env = Environment(loader=self.file_loader)
        else:
            raise FileNotFoundError("Template path does not exist.")

    def get_html_content(self, file_name: str, **kwargs):
        """Render the HTML template."""
        template = self.env.get_template(file_name)
        return template.render(**kwargs)
