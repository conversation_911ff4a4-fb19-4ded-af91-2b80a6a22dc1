from typing import Any, <PERSON>, <PERSON><PERSON>, Union

from fastapi import HTTPException
from fastapi.exceptions import RequestValidationError


class CustomRequestValidationException:
    def __init__(self):
        self.details = []

    def add_validation_error(
        self,
        loc: Union[List[str], Tuple[str, ...]],
        msg: str,
        error_type: str = "value_error",
        input_value: Any = None,
    ):
        """
        Add a validation error detail in FastAPI format.

        Args:
            loc: Location of the error (e.g. ["body", "email"] or ["path", "user_id"])
            msg: Error message
            error_type: Type of error (e.g. "value_error", "type_error")
            input_value: The input value that caused the error
        """
        # Ensure loc is a tuple as expected by FastAPI
        if isinstance(loc, list):
            loc = tuple(loc)

        error = {
            "type": error_type,
            "loc": loc,
            "msg": msg,
        }
        # Only add input if provided (optional)
        if input_value is not None:
            error["input"] = input_value

        self.details.append(error)
        return self

    def raise_validation_exception(self):
        """
        Raise a RequestValidationError with the collected error details.
        """
        if self.details:
            raise RequestValidationError(errors=self.details)


class CustomExceptionDetailsBase:
    def __init__(self):
        self.details = {"detail": []}

    def add_detail(self, type, loc, msg, input):
        """Add error details to the list."""
        self.details["detail"].append(
            {"type": type, "loc": loc, "msg": msg, "input": input}
        )

    def raise_custom_exception(self):
        """Raise a CustomException if there are error details."""
        if self.details:
            raise CustomException(self.details)

    def raise_not_found_exception(self):
        """Raise a CustomException if there are error details."""
        if self.details:
            raise NotFoundException(self.details)


class BaseException(HTTPException):
    """Base exception class for custom exceptions."""

    def __init__(self, status_code: int, detail: str):
        super().__init__(status_code=status_code, detail=detail)

    @property
    def response(self):
        return {"message": self.detail}


class InternalServerErrorException(BaseException):
    """Exception class for internal server errors."""

    def __init__(self):
        super().__init__(status_code=500, detail="Internal server error")


class UnexpectedErrorException(BaseException):
    """Exception class for unexpected errors."""

    def __init__(self):
        super().__init__(status_code=500, detail="An unexpected error occurred")


class CustomException(Exception):
    """Custom exception class for custom exceptions."""

    def __init__(self, details):
        self.details = details

    def __str__(self):
        return str({"detail": self.details})


class NotAuthorizedException(Exception):
    def __init__(self, details):
        self.details = details

    def __str__(self):
        return str({"detail": self.details})


class NotFoundException(Exception):
    def __init__(self, details):
        # self.name = name
        self.details = details

    def __str__(self):
        return str({"detail": self.details})
