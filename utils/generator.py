"""
This module provides a class for generating error objects.
"""

from typing import List, Optional

from utils.schema import <PERSON><PERSON><PERSON>, ErrorDetail


class ErrorGenerator:
    @staticmethod
    def generate_error_detail(type: str, loc: List[str], msg: str, input: Optional[str] = None, ctx: Optional[dict] = None, url: Optional[str] = None):
        """
        Generate an error detail object.

        Args:
            type (str): The type of the error.
            loc (List[str]): The location of the error.
            msg (str): The error message.
            input (Optional[str], optional): The input that caused the error. Defaults to None.
            ctx (Optional[dict], optional): Additional context for the error. Defaults to None.
            url (Optional[str], optional): The URL associated with the error. Defaults to None.

        Returns:
            Error: The generated error object.
        """
        error_detail = ErrorDetail(type=type, loc=loc, msg=msg, input=input, ctx=ctx, url=url)
        error = Error(detail=[error_detail])
        return error



