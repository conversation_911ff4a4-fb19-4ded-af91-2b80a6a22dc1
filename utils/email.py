"""This module contains the Email class to send emails."""

import os
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import smtplib

from utils.logger import setup_logger


class Email:
    def __init__(self, recipient_email: str, subject: str, html_content: str = None):
        """Initialize the Email object."""
        self.msg = MIMEMultipart()
        self.sender = os.environ.get("SENDER_EMAIL")
        self.password = os.environ.get("SENDER_PASSWORD")
        self.msg["From"] = self.sender
        self.msg["To"] = recipient_email
        self.msg["Subject"] = subject
        self.html_content = html_content
        self.logger = setup_logger(name=__name__, log_file="email.log")

    def attach_html(self, html_content: str):
        """Attach HTML content to the email."""
        self.msg.attach(MIMEText(html_content, "html"))
        self.logger.info("HTML content attached to the email.")

    def send(self):
        """Send the email."""
        try:
            if self.html_content:
                self.attach_html(self.html_content)
            server = smtplib.SMTP(os.environ.get("SMTP_SERVER"), 587)
            server.starttls()
            server.login(self.sender, self.password)
            server.sendmail(self.msg["From"], self.msg["To"], self.msg.as_string())
            server.quit()
            self.logger.info("Email sent successfully to %s with subject: %s", 
                            self.msg["To"], self.msg["Subject"])
            return True
        except Exception as e:
            self.logger.error("Failed to send email to %s with subject: %s. Error: %s", 
                             self.msg["To"], self.msg["Subject"], str(e), exc_info=True)
            return False
