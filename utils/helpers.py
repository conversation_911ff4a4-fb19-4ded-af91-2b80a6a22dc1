import traceback

from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError

from app.services.user import AuthService
from utils.exception import (
    CustomException,
    InternalServerErrorException,
    NotAuthorizedException,
    NotFoundException,
)
from utils.logger import Logger, setup_logger


class ExceptionHandler(Logger):
    def __init__(self):
        super().__init__()
        self.logger = setup_logger(name=__name__, log_file="error.log")

    def handle_exception(self, e: Exception):
        """
        Handle exceptions and return appropriate JSONResponse.

        Args:
            e (Exception): The exception to handle.

        Returns:
            JSONResponse: The JSONResponse object with appropriate status code and message.
        """
        self.logger.error("Error: %s", str(e))
        self.error(str(e))
        self.logger.error("Traceback: %s", traceback.format_exc())
        
        if isinstance(e, InternalServerErrorException):
            self.logger.error("Internal Server Error: %s", str(e))
            return JSONResponse(status_code=e.status_code, content=e.response)
        elif isinstance(e, NotAuthorizedException):
            self.logger.error("Not Authorized: %s", str(e))
            return JSONResponse(status_code=401, content=e.details)
        elif isinstance(e, ValidationError):
            self.logger.error("Validation Error: %s", str(e))
            raise RequestValidationError(e.errors())

        elif isinstance(e, RequestValidationError):
            self.logger.error("Request Validation Error: %s", str(e))
            raise

        # Add more elif conditions here for other types of exceptions
        elif isinstance(e, CustomException):
            self.logger.error("Custom Exception: %s", str(e))
            return JSONResponse(status_code=422, content=e.details)
        elif isinstance(e, NotFoundException):
            self.logger.error("Not Found: %s", str(e))
            return JSONResponse(
                status_code=404,
                content=(
                    {"message": f"{e.details} not found"}
                    if isinstance(e.details, str)
                    else e.details
                ),
            )
        else:
            self.logger.error("Unexpected error: %s", str(e), exc_info=True)
            return JSONResponse(
                status_code=500, content={"message": "An unexpected error occurred"}
            )


class AddUserFields:
    """Helper class to add user fields to a dictionary."""

    @staticmethod
    def add_create_and_updated_by(dict_: dict, user_id: int):
        """Add created_by and updated_by fields to a dictionary."""
        dict_["created_by"] = user_id
        dict_["updated_by"] = user_id
        return dict_

    @staticmethod
    def add_updated_by(dict_: dict, user_id: int):
        """Add updated_by field to a dictionary."""
        dict_["updated_by"] = user_id
        return dict_


class ExtractUserAndAddToDict:
    """Helper class to extract user ID from token and add it to a dictionary."""

    @staticmethod
    def extract_user_and_add_to_dict(token: str, dict_: dict):
        """Extract user ID from token and add it to a dictionary."""
        user = AuthService().get_user_from_token(token)
        return AddUserFields.add_create_and_updated_by(dict_, user.id)

    @staticmethod
    def extract_user_and_add_to_dict_updated(token: str, dict_: dict):
        """Extract user ID from token and add it to a dictionary."""
        user = AuthService().get_user_from_token(token)
        return AddUserFields.add_updated_by(dict_, user.id)
