"""
This module provides a function to generate a response dictionary with optional data,
message, and status code.
"""


def generate_response(data=None, message=None, status_code=None,custom_response=None,total_count=None):
    """
    Generate a response dictionary with optional data, message, and status code.

    Args:
        data (any, optional): The data to include in the response. Defaults to None.
        message (str, optional): The message to include in the response. Defaults to None.
        status_code (int, optional): The status code to include in the response. Defaults to None.

    Returns:
        dict: The generated response dictionary.
    """
    if custom_response is not None:
        return custom_response
    response = {}
    if status_code is not None:
        response["status_code"] = status_code
    if message is not None:
        response["message"] = message
    if data is not None:
        response["data"] = data
    if total_count is not None:
        response["total_count"] = total_count
    return response
