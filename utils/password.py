""" Helper class to generate a random password. """

import os
import secrets
import string

from itsdangerous import URLSafeTimedSerializer, SignatureExpired


class PasswordGenerator:
    """Helper class to generate a random password."""

    def __init__(self, length: int = 10) -> None:
        self.length = length

    def random_password(self) -> str:
        """Generate a random password."""
        alphabet = string.ascii_letters + string.digits
        password = "".join(secrets.choice(alphabet) for _ in range(self.length))
        return "AD" + password


class TokenManager:
    """Helper class to create and load tokens."""

    def __init__(self):
        """Initialize the TokenManager class."""
        self.serializer = URLSafeTimedSerializer(os.getenv("SECRET_KEY"))
        self.salt = os.getenv("SALT")

    def create_token(self, email):
        """Create a token for the given email."""
        return self.serializer.dumps(email, salt=self.salt)

    def load_token(self, token):
        """Load the token and return the email if it is valid."""
        try:
            token_expiry = int(os.getenv("PASSWORD_RESET_TOKEN_EXPIRY"))
            email = self.serializer.loads(token, salt=self.salt, max_age=token_expiry)
            return email
        except SignatureExpired:
            return None
