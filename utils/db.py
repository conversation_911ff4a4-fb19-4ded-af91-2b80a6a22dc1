"""
This module provides a BaseService class for common database operations.
"""

import logging
from typing import Any, Dict, List, Optional, Type

from sqlalchemy import Column, and_, asc, desc, or_
from sqlalchemy.orm import Session

from app.database.database import Base, transaction


class BaseService:
    """Base service class for database operations."""

    def __init__(self):
        pass

    @staticmethod
    def init_query(db: Session, model):
        """Initialize a query for a model."""
        return db.query(model)

    @staticmethod
    def get_by_attribute(
        db: Session, model: Type, attribute: Column, value: Any
    ) -> Optional[Type]:
        """Get a single instance of a model by attribute value."""
        try:
            result = db.query(model).filter(attribute == value).first()
            return result
        except Exception as e:
            raise

    def get_first_record(self, db: Session, model: Type, **conditions: Dict[str, Any]):
        """Retrieve the first record from the database."""
        try:
            # Build the query
            query = db.query(model).filter(
                and_(
                    *[getattr(model, key) == value for key, value in conditions.items()]
                )
            )
            
            # Execute the query and return the first result
            result = query.first()
            return result
        except Exception as e:
            raise

    def check_record_exists(self, db: Session, model: Type, **conditions: Dict[str, Any]) -> bool:
        """
        Check if a record exists with the given conditions.
        
        Parameters:
        - db: SQLAlchemy Session
        - model: The model class to query
        - **conditions: Key-value pairs representing the conditions to check
        
        Returns:
        - True if a record exists matching all conditions, False otherwise
        """
        try:
            query = db.query(model).filter_by(**conditions)
            exists = db.query(query.exists()).scalar()
            return exists
        except Exception as e:
            raise

    @staticmethod
    def get_by_attributes(db: Session, model: Type, **kwargs) -> Optional[Type]:
        """Get a single instance of a model by multiple attributes."""
        return db.query(model).filter_by(**kwargs).first()

    @staticmethod
    def get_patients_attributes(db: Session, model: Type, **kwargs) -> Optional[Type]:
        """Get a single instance of a model by multiple attributes."""
        return db.query(model).filter_by(**kwargs).all()

    @staticmethod
    def get_all_by_attributes(
        db: Session,
        model: Type,
        load_attrs: Optional[List[str]] = None,
        in_conditions: Optional[Dict[str, List[Any]]] = None,
        skip: Optional[int] = None,
        limit: Optional[int] = None,
        **kwargs,
    ) -> Optional[List[Type]]:
        """Get instances of a model by multiple attributes, optionally loading specific attributes only."""
        query = db.query(model)

        if load_attrs:
            query = query.with_entities(*[getattr(model, attr) for attr in load_attrs])

        if in_conditions:
            for attr, values in in_conditions.items():
                query = query.filter(getattr(model, attr).in_(values))

        query = query.filter_by(**kwargs)
        total_count = query.count()
        if skip is not None and limit is not None:
            offset = (skip - 1) * limit
            query = query.offset(offset).limit(limit)

        return query.all(), total_count

    @staticmethod
    def get_all(db: Session, model: Type) -> List[Type]:
        """Get all instances of a model."""
        return db.query(model).all()

    @staticmethod
    def create(db: Session, model: Type, **kwargs) -> Type:
        """Create a new instance of a model."""
        try:
            instance = model(**kwargs)
            db.add(instance)
            db.commit()
            db.refresh(instance)
            return instance
        except Exception as e:
            db.rollback()
            raise

    @staticmethod
    def transaction_create(db: Session, model: Type, **kwargs) -> Type:
        instance = model(**kwargs)
        db.add(instance)
        db.flush()  # Ensure the instance is persisted to the database
        db.refresh(instance)
        return instance

    @staticmethod
    def bulk_create(db: Session, model: Type, instances: List[Dict]):
        """Generic method to bulk create instances."""
        objects = [model(**instance) for instance in instances]
        db.bulk_save_objects(objects)
        db.commit()

    @staticmethod
    def update(db: Session, model: Type, instance_id: Any, **kwargs) -> Type:
        """Update an instance of a model."""
        try:
            instance = db.query(model).filter(model.id == instance_id).first()
            if not instance:
                return None
                
            for key, value in kwargs.items():
                setattr(instance, key, value)
                
            db.commit()
            db.refresh(instance)
            return instance
        except Exception as e:
            db.rollback()
            raise

    @staticmethod
    def transaction_update(
        db: Session, model: Type, instance_id: Any, **kwargs
    ) -> Type:
        instance = db.query(model).filter(model.id == instance_id).first()
        for attr, value in kwargs.items():
            setattr(instance, attr, value)
        db.flush()  # Ensure the instance is persisted to the database
        db.refresh(instance)
        return instance

    @staticmethod
    def delete(db: Session, model: Type, instance_id: Any) -> bool:
        """Delete an instance of a model."""
        try:
            instance = db.query(model).filter(model.id == instance_id).first()
            if not instance:
                return False
                
            db.delete(instance)
            db.commit()
            return True
        except Exception as e:
            db.rollback()
            raise

    @staticmethod
    def filter_by_attribute(
        db: Session, model: Type, attribute: str, value: str
    ) -> List[Type]:
        """Filter records based on a specific attribute."""
        return (
            db.query(model).filter(getattr(model, attribute).like(f"%{value}%")).all()
        )

    @staticmethod
    def filter_like(db: Session, model, field: str, value: str):
        """Filter records based on a specific attribute."""
        query = db.query(model).filter(field.like(f"%{value}%"))
        total_count = query.count()  # Get total count before fetching results
        return query, total_count  # Return Query object and total count

    @staticmethod
    def filter_value_like(db: Session, model, field: str, value: str):
        """Filter records based on a specific attribute."""
        return db.query(model).filter(field.like(f"%{value}%"))

    @staticmethod
    def filter_equal(db: Session, model, field: str, value: str):
        """Filter records based on a specific attribute equals to value."""
        query = db.query(model).filter(field == value)
        total_count = query.count()  # Get total count before fetching results
        return query, total_count  # Return Query object and total count

    @staticmethod
    def filter_by_multiple_attributes(db: Session, model, **kwargs):
        filters = [
            getattr(model, attribute).contains(value)
            for attribute, value in kwargs.items()
        ]
        return db.query(model).filter(or_(*filters))

    @staticmethod
    def join_filter_query(db: Session, query, model, field, value):
        query = query.join(model).filter(field == value)
        return query

    @staticmethod
    def order_query(query, model, order_by):
        if order_by is not None:
            direction = desc if order_by.startswith("-") else asc
            attribute = order_by.lstrip("-")
            if hasattr(model, attribute):
                query = query.order_by(direction(getattr(model, attribute)))
        return query

    def get_paginated_list(self, db: Session, model: Type[Base], limit: int, page: int):
        """Retrieve paginated list of items."""
        # Calculate the offset based on the page and limit
        offset = (page - 1) * limit
        # Query the total count of items
        total_count = db.query(model).count()
        # Query the paginated items from the database
        items = db.query(model).offset(offset).limit(limit).all()
        return items, total_count

    def get_filtred_paginated_list(
        self,
        db: Session,
        model: Type[Base],
        limit: int,
        page: int,
        filter_by: Optional[Dict[str, Any]] = None,
    ):
        """Retrieve paginated list of items."""
        # Calculate the offset based on the page and limit
        offset = (page - 1) * limit

        # Start a query
        query = db.query(model)

        # Apply filters if provided
        if filter_by:
            filters = [getattr(model, key) == value for key, value in filter_by.items()]
            query = query.filter(or_(*filters))

        # Query the total count of items
        total_count = query.count()

        # Query the paginated items from the database
        items = query.offset(offset).limit(limit).all()

        return items, total_count

    def check_unique_field(
        self, db: Session, model, field: str, value: str, id: Optional[int] = None
    ) -> bool:
        query = db.query(model).filter(getattr(model, field) == f"{value}")
        if id is not None:
            query = query.filter(model.id != id)
        return db.query(query.exists()).scalar()


class QueryWithCount(BaseService):
    """Class to handle query with total count."""

    def __init__(self, query, total_count=0):
        super().__init__()
        self.query = query
        self.total_count = total_count

    def apply_filter(self, db, model, field, value):
        """Apply a filter to the query if the value is not None."""
        if value is not None:
            try:
                self.query, count = self.filter_like(
                    db=db, model=model, field=field, value=value
                )
            except Exception as e:
                raise

    def apply_filter_equal(self, db, model, field, value):
        """Apply a filter to the query if the value is not None."""
        if value is not None:
            try:
                self.query, count = self.filter_equal(
                    db=db, model=model, field=field, value=value
                )
                self.total_count += count
            except Exception as e:
                raise
        
        return self.query, self.total_count


class BulkCreateService:
    """Service class for bulk creation of instances."""

    @staticmethod
    def create_instances(db: Session, model: Type, records: List[Dict]) -> List[Type]:
        """Create multiple instances of a model."""
        instances = []
        model_name = model.__name__ if hasattr(model, '__name__') else str(model)
        
        for record in records:
            instance_data = {}
            for column, value in record.items():
                column = column.lower()
                if hasattr(model, column):
                    instance_data[column] = value
            if instance_data:
                instance = model(**instance_data)
                instances.append(instance)
        
        if not instances:
            return []
            
        try:
            db.add_all(instances)
            db.commit()
            
            created_instances = (
                db.query(model)
                .filter(model.id.in_([instance.id for instance in instances]))
                .all()
            )
            
            return created_instances
        except Exception as e:
            db.rollback()
            raise

    @staticmethod
    def delete_all(db: Session, model: Type, attribute: Column, value: Any) -> int:
        """Delete all instances of a model where attribute matches the value."""
        model_name = model.__name__ if hasattr(model, '__name__') else str(model)
        attribute_name = attribute.key if hasattr(attribute, 'key') else str(attribute)
        
        try:
            instances = db.query(model).filter(attribute == value).all()
            count = len(instances)
            
            if count == 0:
                return 0
                
            for instance in instances:
                db.delete(instance)
            db.commit()
            
            return count
        except Exception as e:
            db.rollback()
            raise
