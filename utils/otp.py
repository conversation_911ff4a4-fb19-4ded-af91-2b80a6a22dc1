"""
This module provides functions for generating and sending OTPs (One-Time Passwords).
"""

import random


def generate_otp():
    """
    Generate a 6-digit OTP.

    Returns:
        str: The generated OTP as a string.
    """
    otp = random.randint(100000, 999999)
    return str(otp)


def send_otp(phone_number: str, otp: str):
    """
    Send OTP to a phone number.

    Args:
        phone_number (str): The phone number to which the OTP should be sent.
        otp (str): The OTP to be sent.

    Returns:
        str: The sent OTP.
    """
    # Implement your OTP sending logic here
    return otp
