import pandas as pd
from io import BytesIO


class PandaUtils:
    """ A class to convert file contents to a pandas DataFrame."""
    def convert_to_dataframe(self, contents):
        """
        Convert the file contents to a pandas DataFrame.

        Args:
            contents (bytes): The contents of the file.

        Returns:
            pd.DataFrame: The DataFrame created from the file contents.
        """
        return pd.read_excel(BytesIO(contents))

    def convert_to_dict(self, df: pd.DataFrame):
        """
        Convert the DataFrame to a list of dictionaries.

        Args:
            df (pd.DataFrame): The DataFrame to be converted.

        Returns:
            list: A list of dictionaries representing the DataFrame records.
        """
        return df.to_dict(orient="records")
    
    def convert_contents_to_records(self,contents: bytes):
        """ Convert the file contents to a list of dictionaries."""
        df = self.convert_to_dataframe(contents=contents)
        records = self.convert_to_dict(df=df) # Convert DataFrame to list of dicts
        return records
