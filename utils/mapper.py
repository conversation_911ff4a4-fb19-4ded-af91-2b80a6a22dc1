"""
This module contains the AttributeMapper class which is used for mapping attributes dynamically.
"""


class AttributeMapper:
    """
    The AttributeMapper class is used for mapping attributes dynamically.

    Args:
        **kwargs: Keyword arguments representing the attributes to be mapped.

    """

    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)
