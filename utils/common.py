from app.database.database import <PERSON><PERSON><PERSON><PERSON>, get_db,get_db_session
from sqlalchemy import inspect, text
from sqlalchemy.exc import IntegrityError

from utils.logger import setup_logger
logger = setup_logger("core.log")

FOREIGN_KEY_CONSTRAINT_ERROR_MSG = "Cannot delete {table_name} with primary key {primary_key} because it is referenced in {related_tables}."
OBJECT_DELETED_MSG = "{table_name} with primary key {primary_key} has been deleted."


def get_foreign_key_references(session, obj):
    """
    Checks if the given object has any foreign key references in the database.

    :param session: SQLAlchemy session object
    :param obj: SQLAlchemy model instance to be checked
    :return: List of related table names that reference the object
    """
    table_name = obj.__tablename__
    primary_key = inspect(obj).identity
    related_tables = []

    logger.debug(f"Checking foreign key references for table: {table_name}, primary key: {primary_key}")

    # Direct SQL query to check for foreign key constraints
    query = text("""
        SELECT TABLE_NAME, COLUMN_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
        WHERE REFERENCED_TABLE_NAME = :table_name
        AND REFERENCED_COLUMN_NAME = 'id'
        AND TABLE_SCHEMA = DATABASE()
    """)
    result = session.execute(query, {'table_name': table_name}).fetchall()
    logger.debug(f"Foreign key references found: {result}")

    for row in result:
        related_table = row[0]  # Accessing the first element of the tuple
        related_column = row[1]  # Accessing the second element of the tuple
        logger.debug(f"Checking references in table: {related_table}, column: {related_column}")
        check_query = text(f"SELECT 1 FROM `{related_table}` WHERE `{related_column}` = :pk_value LIMIT 1")
        check_result = session.execute(check_query, {'pk_value': primary_key[0]}).scalar()
        if check_result:
            logger.debug(f"Reference found in table: {related_table}")
            related_tables.append((related_table, related_column))
    logger.debug(f"Related tables: {related_tables}")
    return related_tables

def delete_object(session, obj, force_delete=False, soft_delete=False):
    """
    Deletes an object from the database.

    :param session: SQLAlchemy session object
    :param obj: SQLAlchemy model instance to be deleted
    :param force_delete: Boolean flag to force delete without checking foreign key constraints
    :param soft_delete: Boolean flag to perform a soft delete instead of a hard delete
    """
    try:
        primary_key = inspect(obj).identity
        primary_key_str = ', '.join(map(str, primary_key))  # Convert tuple to string
        logger.info(f"Deleting {obj.__tablename__} with primary key {primary_key_str}")
        related_tables = get_foreign_key_references(session, obj)
        if related_tables and not force_delete:
            logger.warning(f"Cannot delete {obj.__tablename__} with primary key {primary_key_str} because it is referenced in {[table for table, column in related_tables]}")
            return FOREIGN_KEY_CONSTRAINT_ERROR_MSG.format(
                table_name=obj.__tablename__,
                primary_key=primary_key_str,
                related_tables=", ".join([table for table, column in related_tables])
            )

        if force_delete:
            # Disable foreign key checks
            logger.info("Disabling foreign key checks")
            session.execute(text("SET FOREIGN_KEY_CHECKS=0"))
            logger.debug(f"related_tables: {related_tables}")
            # Delete related records from all related tables
            for related_table, related_column in related_tables:
                delete_query = text(f"DELETE FROM `{related_table}` WHERE `{related_column}` = :pk_value")
                logger.debug(f"Deleting from {related_table} where {related_column} = {primary_key[0]}")
                session.execute(delete_query, {'pk_value': primary_key[0]})

        if soft_delete:
            # Perform a soft delete by updating the `deleted` column
            logger.info(f"Performing soft delete for {obj.__tablename__} with primary key {primary_key_str}")
            obj.is_deleted = True
            session.commit()
        else:
            # Perform a hard delete
            logger.info(f"Performing hard delete for {obj.__tablename__} with primary key {primary_key_str}")
            session.delete(obj)
            session.commit()

        if force_delete:
            # Re-enable foreign key checks
            logger.info("Re-enabling foreign key checks")
            session.execute(text("SET FOREIGN_KEY_CHECKS=1"))

        logger.info(f"{obj.__tablename__} with primary key {primary_key_str} has been deleted")
        return OBJECT_DELETED_MSG.format(
            table_name=obj.__tablename__,
            primary_key=primary_key_str
        )
    except IntegrityError as e:
        session.rollback()
        logger.error(f"Integrity error occurred: {e}")
        raise