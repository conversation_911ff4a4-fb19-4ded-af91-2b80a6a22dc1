import pandas as pd
import re

def split_pre_investigation(pre_inv):
    """
    Splits a string containing pre-investigation values separated by commas,
    excluding commas inside parentheses.

    Args:
        pre_inv (str): The string containing pre-investigation values.

    Returns:
        list: A list of pre-investigation values.

    Example:
         split_pre_investigation("value1, value2, (value3, value4), value5")
        ['value1', 'value2', '(value3, value4)', 'value5']
    """
    # Regex to match commas outside parentheses
    return re.split(r',(?![^\(]*\))', pre_inv)

def create_document_dataframe(df, column_name, step_name, strip_char= ''):
    """
    Create a dataframe with document information from a given dataframe.

    Args:
        df (pandas.DataFrame): The input dataframe.
        column_name (str): The name of the column containing document information.
        step_name (str): The name of the step.
        strip_char (str, optional): The character used to split the document name. Defaults to ''.

    Returns:
        pandas.DataFrame: The resulting dataframe with document information.
    """
    columns = ['PROCEDURE_NAME', 'DOCUMENT_NAME', 'STEP']
    dataframe = pd.DataFrame(columns=columns)
    
    if strip_char == '/':
        dataframe['DOCUMENT_NAME'] = df[column_name].str.split(strip_char)
    else:
        dataframe['DOCUMENT_NAME'] = df[column_name].apply(split_pre_investigation)
    dataframe = dataframe.explode('DOCUMENT_NAME')
    dataframe['DOCUMENT_NAME'] = dataframe['DOCUMENT_NAME'].str.strip()
    dataframe['STEP'] = step_name
    dataframe['PROCEDURE_NAME'] = df['PROCEDURE_NAME']

    return dataframe


def get_data(table_name, engine):
    """
    Retrieve data from a specified table in a database.

    Parameters:
    - table_name (str): The name of the table to retrieve data from.
    - engine: The database engine to execute the query.

    Returns:
    - data (pandas.DataFrame): The retrieved data loaded into a DataFrame.
    """
    query = "SELECT * FROM {}".format(table_name)

    # Execute the query and load the data into a DataFrame
    data = pd.read_sql_query(query, engine)

    # Display the DataFrame
    return data

def get_document_master_ids(table_name, documents, engine):
    """
    Retrieve data from a specified table in a database.

    Parameters:
    - table_name (str): The name of the table to retrieve data from.
    - engine: The database engine to execute the query.

    Returns:
    - data (pandas.DataFrame): The retrieved data loaded into a DataFrame.
    """
    
    # Handle empty document list
    if not documents:
        return pd.DataFrame(columns=['id', 'name'])
    
    placeholders = ','.join(['%s'] * len(documents))  # Use '%s' for PostgreSQL, MySQL, etc.
    query = "SELECT id, name FROM {} WHERE name IN ({})".format(table_name, placeholders)

    # Execute the query and load the data into a DataFrame
    data = pd.read_sql_query(query, engine, params=tuple(documents))

    # Display the DataFrame
    return data




def collect_data(dataframe, document_master, step_master, package_master, package_step_master, package_step_document_master):
    """
    Collects data from various data sources and performs merging and transformations to create a final dataframe.

    Args:
        dataframe (pandas.DataFrame): The input dataframe containing the data to be processed.
        document_master (pandas.DataFrame): The dataframe containing the document master data.
        step_master (pandas.DataFrame): The dataframe containing the step master data.
        package_master (pandas.DataFrame): The dataframe containing the package master data.
        package_step_master (pandas.DataFrame): The dataframe containing the package step master data.

    Returns:
        pandas.DataFrame: The final dataframe after merging and transformations.

    """
        
    # Find the ids in package_step_master that are also in package_step_id of PACKAGE_STEP_DOCUMENT_MASTER_DF
    ids_to_drop = package_step_document_master['package_step_id'].unique()

    # Drop the records from package_step_master where 'id' is in ids_to_drop
    filtered_package_step_master = package_step_master[~package_step_master['id'].isin(ids_to_drop)]
    package_step_master = filtered_package_step_master
    
    # First merge to add DOCUMENT_ID
    # Convert both columns to lowercase
    dataframe['DOCUMENT_NAME'] = dataframe['DOCUMENT_NAME'].str.lower()

    document_master['name'] = document_master['name'].str.lower()
    document_master = document_master.drop_duplicates(subset='name', keep='first')
    df_merged = dataframe.merge(document_master, how='left', left_on='DOCUMENT_NAME', right_on='name')

    # Selecting and renaming relevant columns
    df_result = df_merged[['PROCEDURE_NAME', 'DOCUMENT_NAME', 'STEP', 'id']]
    df_result.rename(columns={'id': 'DOCUMENT_ID'}, inplace=True)
    df_result.fillna(0, inplace=True)
    df_result['DOCUMENT_ID'] = df_result['DOCUMENT_ID'].astype(int)

    # Second merge to add STEP_ID
    df_final = df_result.merge(step_master, how='left', left_on='STEP', right_on='type')

    # Selecting and renaming relevant columns
    df_final_result = df_final[['PROCEDURE_NAME', 'DOCUMENT_NAME', 'STEP', 'DOCUMENT_ID', 'id']]
    df_final_result.rename(columns={'id': 'STEP_ID'}, inplace=True)
    df_final_result.fillna(0, inplace=True)
    df_final_result['STEP_ID'] = df_final_result['STEP_ID'].astype(int)
    
    df_final_result['PROCEDURE_NAME'] = df_final_result['PROCEDURE_NAME'].str.lower().str.replace(' ', '')

    procedure_df = df_final_result.merge(package_master, how='left', left_on='PROCEDURE_NAME', right_on='procedure_name')
    procedure_df = procedure_df[['PROCEDURE_NAME', 'DOCUMENT_NAME', 'DOCUMENT_ID', 'STEP_ID', 'id']]
    procedure_df.rename(columns={'id': 'PACKAGE_MASTER_ID'}, inplace=True)
    procedure_df.fillna(0, inplace=True)
    procedure_df['PACKAGE_MASTER_ID'] = procedure_df['PACKAGE_MASTER_ID'].astype(int)
    FINAL_DF = pd.merge(procedure_df, package_step_master, left_on=['STEP_ID', 'PACKAGE_MASTER_ID'], right_on=['step_id', 'package_id'])

    # Rename the 'id' column to 'package_step_master_id'
    FINAL_DF.rename(columns={'id': 'PACKAGE_STEP_MASTER_ID'}, inplace=True)
    FINAL_DF = FINAL_DF[['PACKAGE_STEP_MASTER_ID','DOCUMENT_ID']].rename(columns={'PACKAGE_STEP_MASTER_ID':'package_step_id', 'DOCUMENT_ID':'document_id'})
    return FINAL_DF