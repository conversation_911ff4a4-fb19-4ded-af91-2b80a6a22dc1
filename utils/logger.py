import logging
import os
from logging.handlers import TimedRotatingFileHandler
import re


def get_environment():
    """
    Get the current environment from environment variables.
    
    Returns:
        str: Current environment (live, uat, development)
    """
    return os.environ.get('APP_ENV', 'development').lower()


def is_production():
    """
    Check if the current environment is production/live.
    
    Returns:
        bool: True if environment is production/live, False otherwise
    """
    env = get_environment()
    return env in ['production', 'live', 'prod']

def setup_logger(name=None, log_file="core.log", level=None):
    """
    Set up and configure a logger with time-based rotation.
    
    Args:
        name (str, optional): Logger name. If None, uses the module name.
        log_file (str): Name of the log file.
        level (int): Logging level (DEBUG, INFO, etc.). If None, will be set based on environment.
        
    Returns:
        logging.Logger: Configured logger instance
    """
    # Set default logging level based on environment
    if level is None:
        # In production, only log ERROR and above
        # In UAT/development, log INFO and above
        level = logging.ERROR if is_production() else logging.INFO

    # Create a logger
    logger = logging.getLogger(name or __name__)

    # Clear existing handlers to avoid duplicate logs
    if logger.hasHandlers():
        logger.handlers.clear()

    # Set the logging level
    logger.setLevel(level)

    # Set the log folder to 'logs' and create it if it doesn't exist
    log_folder = "logs"
    if not os.path.exists(log_folder):
        os.makedirs(log_folder)

    # Create the full log file path
    full_log_file = os.path.join(log_folder, log_file)

    # Create a time-rotating file handler (rotate daily, keep 30 days of logs)
    file_handler = TimedRotatingFileHandler(
        full_log_file,
        when='midnight',
        interval=1,
        backupCount=1
    )
    file_handler.setLevel(level)
    
    # Set the suffix for rotated files
    file_handler.suffix = "%Y-%m-%d"  # Add date suffix to rotated files

    # Update the extMatch regex to match the suffix format.
    file_handler.extMatch = re.compile(r"^\d{4}-\d{2}-\d{2}$")

    # Create a formatter with more detailed information
    formatter = logging.Formatter(
        "%(asctime)s - %(levelname)s - [%(name)s] - %(filename)s:%(lineno)d - %(funcName)s - %(message)s"
    )
    file_handler.setFormatter(formatter)

    # Add the file handler to the logger
    logger.addHandler(file_handler)

    return logger


# Create a default core logger
core_logger = setup_logger()


class Logger:
    """
    A wrapper class for logging that provides consistent logging methods.
    """
    def __init__(self, log_file="core.log", name=None, level=None):
        """
        Initialize a logger instance.
        
        Args:
            log_file (str): Name of the log file
            name (str, optional): Logger name
            level (int): Logging level
        """
        self.logger = setup_logger(name=name, log_file=log_file, level=level)
        self.module_name = name or __name__
        self.is_production = is_production()

    def debug(self, message, *args, **kwargs):
        """
        Log a debug message.
        
        Args:
            message (str): Message to log
            *args: Variable arguments to format the message
            **kwargs: Keyword arguments for the logger
        """
        # In production, debug logs are suppressed
        if not self.is_production:
            self.logger.debug(message, *args, **kwargs)

    def info(self, message, *args, **kwargs):
        """
        Log an info message.
        
        Args:
            message (str): Message to log
            *args: Variable arguments to format the message
            **kwargs: Keyword arguments for the logger
        """
        # In production, only log info if explicitly allowed
        if not self.is_production or kwargs.pop('force_production_log', False):
            self.logger.info(message, *args, **kwargs)

    def warning(self, message, *args, **kwargs):
        """
        Log a warning message.
        
        Args:
            message (str): Message to log
            *args: Variable arguments to format the message
            **kwargs: Keyword arguments for the logger
        """
        # Warnings are logged in all environments
        self.logger.warning(message, *args, **kwargs)

    def error(self, message, *args, **kwargs):
        """
        Log an error message.
        
        Args:
            message (str): Message to log
            *args: Variable arguments to format the message
            **kwargs: Keyword arguments for the logger
            exc_info (bool): Whether to include exception info, defaults to False
        """
        exc_info = kwargs.pop('exc_info', False)
        self.logger.error(message, *args, exc_info=exc_info, **kwargs)

    def critical(self, message, *args, **kwargs):
        """
        Log a critical message.
        
        Args:
            message (str): Message to log
            *args: Variable arguments to format the message
            **kwargs: Keyword arguments for the logger
            exc_info (bool): Whether to include exception info, defaults to True
        """
        exc_info = kwargs.pop('exc_info', True)
        self.logger.critical(message, *args, exc_info=exc_info, **kwargs)

    def exception(self, message, *args, **kwargs):
        """
        Log an exception message with traceback.
        
        Args:
            message (str): Message to log
            *args: Variable arguments to format the message
            **kwargs: Keyword arguments for the logger
        """
        self.logger.exception(message, *args, **kwargs)

    # CRUD operation logging
    def create(self, entity):
        """
        Log entity creation.
        
        Args:
            entity: Entity that was created
        """
        if not self.is_production:
            self.logger.info("%s created successfully", entity)
        else:
            self.logger.debug("%s created successfully", entity)

    def read(self, entity):
        """
        Log entity read.
        
        Args:
            entity: Entity that was read
        """
        if not self.is_production:
            self.logger.debug("%s read successfully", entity)

    def update(self, entity):
        """
        Log entity update.
        
        Args:
            entity: Entity that was updated
        """
        if not self.is_production:
            self.logger.info("%s updated successfully", entity)
        else:
            self.logger.debug("%s updated successfully", entity)

    def delete(self, entity):
        """
        Log entity deletion.
        
        Args:
            entity: Entity that was deleted
        """
        # Always log deletions as they're important operations
        self.logger.info("%s deleted successfully", entity)
