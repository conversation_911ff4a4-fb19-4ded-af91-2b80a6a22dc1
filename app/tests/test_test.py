import unittest
from fastapi.testclient import TestClient
from .main import app  # replace with the actual module where your FastAPI app is defined

class TestUserRoutes(unittest.TestCase):
    def setUp(self):
        self.client = TestClient(app)

    def test_get_users(self):
        response = self.client.get("/users")
        self.assertEqual(response.status_code, 200)

    def test_get_user(self):
        response = self.client.get("/users/1")  # replace 1 with a valid user ID
        self.assertEqual(response.status_code, 200)

    def test_create_user(self):
        user_data = {
            "first_name": "<PERSON>",
            "last_name": "<PERSON><PERSON>",
            "username": "johndo<PERSON>",
            "email": "<EMAIL>",
            "mobile_number": "1234567890",
            "is_active": True,
            "is_superuser": False,
            "timestamp_id": 123456
        }
        response = self.client.post("/users", json=user_data)
        self.assertEqual(response.status_code, 201)

    def test_update_user(self):
        user_data = {
            "first_name": "<PERSON>",
            "last_name": "<PERSON><PERSON>",
            "username": "johndo<PERSON>",
            "email": "johndo<PERSON>@example.com",
            "mobile_number": "1234567890",
            "is_active": True,
            "is_superuser": False,
            "timestamp_id": 123456
        }
        response = self.client.put("/users/1", json=user_data)  # replace 1 with a valid user ID
        self.assertEqual(response.status_code, 200)

    def test_delete_user(self):
        response = self.client.delete("/users/1")  # replace 1 with a valid user ID
        self.assertEqual(response.status_code, 200)

if __name__ == '__main__':
    unittest.main()