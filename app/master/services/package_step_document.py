from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session


from app.database.database import engine, Base, SessionLocal

from app.master.exception.errors.package_step_document import PackageStepDocumentErrors
from app.master.models.package import PackageStepDocumentDB

from app.master.services.document_master import DocumentMasterHelper
from app.master.utils.mapping.document_master import StepDocumentMapping
from app.master.helpers.logger import package_logger

from utils.db import BaseService

# Base.metadata.create_all(bind=engine)


class PackageStepDocumentService(BaseService):
    """
    Service class for managing package step document-related operations.
    """

    def __init__(self) -> None:
        self.db = SessionLocal()

    def get_package_step_document_by_id(
        self, package_step_document_id: int, db: Session
    ):
        """Retrieve a package step document by ID."""
        try:
            is_exist, package_step_document = (
                self.get_package_step_document_by_attribute(
                    value=package_step_document_id, db=db
                )
            )
            return is_exist, package_step_document
        except Exception as e:
            package_logger.error("Error retrieving package step document by id=%s: %s", 
                               package_step_document_id, str(e))
            raise

    def get_package_step_document_by_attribute(
        self, value: str, db: Session, attribute: str = None
    ):
        """Retrieve a package step document by a specific attribute."""
        try:
            if not attribute:
                attribute = PackageStepDocumentDB.id
            # Query the package step document from the database
            package_step_document = self.get_by_attribute(
                db, PackageStepDocumentDB, attribute, value
            )
            if not package_step_document:
                package_logger.debug("Package step document not found with %s=%s", 
                                   attribute.key if hasattr(attribute, 'key') else 'id', value)
                return False, None
            package_logger.debug("Retrieved package step document with %s=%s", 
                               attribute.key if hasattr(attribute, 'key') else 'id', value)
            return True, package_step_document
        except Exception as e:
            package_logger.error("Error retrieving package step document by attribute %s=%s: %s", 
                               attribute.key if hasattr(attribute, 'key') else 'id', value, str(e))
            raise

    def get_first_package_step_document(self, package_step_document_data: dict):
        """Retrieve the first package step document."""
        return self.get_first_record(
            db=self.db, model=PackageStepDocumentDB, **package_step_document_data
        )

    def get_all_package_step_documents(
        self, db: Session
    ) -> List[PackageStepDocumentDB]:
        """Retrieve all package step documents."""
        try:
            package_step_documents = self.get_all(db, PackageStepDocumentDB)
            package_logger.debug("Retrieved %d package step documents", len(package_step_documents))
            return package_step_documents
        except Exception as e:
            package_logger.error("Error retrieving all package step documents: %s", str(e))
            raise

    def create_package_step_document_base(
        self, package_step_document_data, db: Session
    ) -> PackageStepDocumentDB:
        """Create a new package step document."""
        try:
            is_exist = self.validate_create_data(package_step_document_data)
            if is_exist:
                package_logger.warning("Package step document already exists: %s", package_step_document_data)
                PackageStepDocumentErrors().raise_package_step_document_already_exists_exception(
                    package_step_document=package_step_document_data
                )
            package_step_document = self.create(
                db, PackageStepDocumentDB, **package_step_document_data
            )
            package_logger.info("Created package step document with id=%s", package_step_document.id)
            return package_step_document
        except Exception as e:
            package_logger.error("Error creating package step document: %s", str(e))
            raise

    def delete_package_step_document(
        self, package_step_document_id: int, db: Session
    ) -> int:
        """Delete a package step document."""
        try:
            is_exist, package_step_document = self.get_package_step_document_by_id(
                package_step_document_id, db=db
            )
            if not is_exist:
                package_logger.warning("Cannot delete: package step document not found with id=%s", 
                                     package_step_document_id)
                PackageStepDocumentErrors().raise_package_step_document_not_found_exception(
                    package_step_document_id=package_step_document_id
                )
            db.delete(package_step_document)
            db.commit()
            package_logger.info("Deleted package step document with id=%s", package_step_document_id)
            return package_step_document_id
        except Exception as e:
            package_logger.error("Error deleting package step document with id=%s: %s", 
                               package_step_document_id, str(e))
            raise

    def validate_update_data(
        self, package_step_document_data, package_step_document_id
    ) -> bool:
        """Validate the update data for a package step document."""
        try:
            package_step_document = self.get_first_package_step_document(
                package_step_document_data.dict()
            )
            if (
                package_step_document
                and package_step_document.id != package_step_document_id
            ):
                return False
            return True
        except Exception as e:
            package_logger.error("Error validating update data for package step document: %s", str(e))
            raise

    def validate_create_data(self, package_step_document_data) -> bool:
        """Validate the create data for a package step document."""
        try:
            package_step_document = self.get_first_package_step_document(
                package_step_document_data
            )
            if bool(package_step_document):
                return True
            return False
        except Exception as e:
            package_logger.error("Error validating create data for package step document: %s", str(e))
            raise

    def create_package_step_documents(self, package_id: int, package_step_documents: dict):
        """Create package step documents for a package."""
        try:
            for mapping in package_step_documents:
                package_step_id = mapping.package_step_id
                document_ids = mapping.document_ids

                # Fetch existing mappings for the given package_step_id
                existing_mappings = self.db.query(PackageStepDocumentDB).filter_by(package_step_id=package_step_id).all()
                existing_document_ids = {doc.document_id for doc in existing_mappings}

                # Find documents to remove
                document_ids_to_remove = existing_document_ids - set(document_ids)
                if document_ids_to_remove:
                    self.db.query(PackageStepDocumentDB).filter(
                        PackageStepDocumentDB.package_step_id == package_step_id,
                        PackageStepDocumentDB.document_id.in_(document_ids_to_remove)
                    ).delete(synchronize_session=False)

                # Find documents to add
                document_ids_to_add = set(document_ids) - existing_document_ids
                for doc_id in document_ids_to_add:
                    new_mapping = PackageStepDocumentDB(
                        package_step_id=package_step_id,
                        document_id=doc_id
                    )
                    self.db.add(new_mapping)

            self.db.commit()
            package_logger.info("Created package step documents for package_id=%s", package_id)
        except Exception as e:
            self.db.rollback() 
            package_logger.error("Error creating package step documents for package_id=%s: %s", package_id, str(e))
            raise


class PackageStepDocumentHelper:
    def __init__(self):
        self.db = SessionLocal()

    def create_package_step_document(self, step_id, document_ids):
        """Create a package step document."""
        try:
            package_logger.debug("Creating package step documents for step_id=%s with document_ids=%s", 
                               step_id, document_ids)
            for document_id in document_ids:
                package_step_document_data = AttributeMapper(
                    package_step_id=step_id, document_id=document_id
                ).__dict__
                is_exist = PackageStepDocumentService().validate_create_data(
                    package_step_document_data
                )
                if not is_exist:
                    package_step_document = (
                        PackageStepDocumentService().create_package_step_document_base(
                            package_step_document_data=package_step_document_data,
                            db=self.db,
                        )
                    )
            package_logger.info("Successfully created package step documents for step_id=%s", step_id)
            return True
        except Exception as e:
            package_logger.error("Error creating package step document for step_id=%s: %s", step_id, str(e))
            raise

    def is_document_exsists_by_ids(self, document_ids):
        """Check if the documents exist by IDs."""
        from app.master.services.document_master import DocumentMasterService
        from app.master.exception.errors.document_master import DocumentMasterErrors

        try:
            package_logger.debug("Checking if documents exist with ids=%s", document_ids)
            missing_ids = DocumentMasterService().check_documents_exist_by_ids(
                document_ids=document_ids
            )
            if missing_ids:
                package_logger.warning("Documents not found with ids=%s", missing_ids)
                DocumentMasterErrors().raise_document_not_exists_with_ids_exception(
                    document_ids=missing_ids
                )
            return True
        except Exception as e:
            package_logger.error("Error checking if documents exist with ids=%s: %s", document_ids, str(e))
            raise

    def _create_package_step_documents(self, package_step_id, document_ids):
        """Get the pre-investigation documents."""
        try:
            package_logger.debug("Creating package step documents for package_step_id=%s", package_step_id)
            is_exists = self.is_document_exsists_by_ids(document_ids)
            if is_exists:
                return self.create_package_step_document(package_step_id, document_ids)
            return None
        except Exception as e:
            package_logger.error("Error creating package step documents for package_step_id=%s: %s", 
                               package_step_id, str(e))
            raise

    def create_all_package_step_documents(self, packge_step_id, document_ids):
        """Create all package step documents."""
        try:
            package_logger.debug("Creating all package step documents for package_step_id=%s", packge_step_id)
            return self._create_package_step_documents(packge_step_id, document_ids)
        except Exception as e:
            package_logger.error("Error creating all package step documents for package_step_id=%s: %s", 
                               packge_step_id, str(e))
            raise

    def handle_package_step_document(self, package_id: int, step_document: dict):
        from app.master.services.package_step import PackageStepHelper

        try:
            package_logger.info("Handling package step documents for package_id=%s", package_id)
            for step_type, document_ids in step_document.items():
                package_logger.debug("Processing step_type=%s with document_ids=%s", step_type, document_ids)
                package_step_id = (
                    PackageStepHelper().get_package_step_id_from_step_type(
                        package_id=package_id, step_type=step_type
                    )
                )
                document_names = StepDocumentMapping().get_documents(step=step_type)
                if document_names:
                    op_doc_ids = DocumentMasterHelper().get_document_ids(
                        document_names=document_names
                    )
                    document_ids.extend(op_doc_ids)
                    package_logger.debug("Extended document_ids with optional documents: %s", op_doc_ids)
                self.create_all_package_step_documents(package_step_id, document_ids)
            package_logger.info("Successfully handled package step documents for package_id=%s", package_id)
            return True
        except Exception as e:
            package_logger.error("Error handling package step documents for package_id=%s: %s", 
                               package_id, str(e))
            raise
