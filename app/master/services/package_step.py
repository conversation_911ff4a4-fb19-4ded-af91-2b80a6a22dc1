from typing import Any, Dict, List, Optional

from pandas import Data<PERSON>rame
from sqlalchemy.orm import Session

from app.database.database import Base, SessionLocal, engine
from app.master.exception.errors.package import PackageErrors
from app.master.exception.errors.package_step import PackageStepErrors
from app.master.exception.errors.step_master import StepMasterErrors
from app.master.models.package import PackageDB, PackageStepMasterDB, StepMaster
from app.master.helpers.logger import master_logger, package_logger

from utils.db import BaseService, QueryWithCount
from utils.mapper import AttributeMapper
from utils.pagination import PaginationHelper
from utils.panda import PandaUtils

# Base.metadata.create_all(bind=engine)


class PackageStepMasterService(BaseService):
    """
    Service class for managing package step master-related operations.
    """

    def __init__(self) -> None:
        self.db = SessionLocal()
        self.logger = package_logger

    def get_package_step_master_by_attribute(
        self, value: str, db: Session, attribute: str = None
    ):
        """Retrieve a package_step_master by a specific attribute."""
        try:
            if not attribute:
                attribute = PackageStepMasterDB.id
            # Query the package step from the database
            package_step_master = self.get_by_attribute(
                db, PackageStepMasterDB, attribute, value
            )
            if not package_step_master:
                self.logger.debug("Package step master not found with %s=%s", 
                                 attribute.key if hasattr(attribute, 'key') else 'id', value)
                return False, None
            return True, package_step_master
        except Exception as e:
            self.logger.error("Error retrieving package step master by attribute %s=%s: %s", 
                             attribute.key if hasattr(attribute, 'key') else 'id', value, str(e), 
                             exc_info=True)
            raise

    def get_package_step_master_by_id(self, package_step_master_id: int, db: Session):
        """Retrieve a package_step_master by ID."""
        try:
            self.logger.debug("Retrieving package step master with id=%s", package_step_master_id)
            is_exist, package_step_master = self.get_package_step_master_by_attribute(
                value=package_step_master_id, db=db
            )
            return is_exist, package_step_master
        except Exception as e:
            self.logger.error("Error retrieving package step master with id=%s: %s", 
                             package_step_master_id, str(e), exc_info=True)
            raise

    def get_first_package_step(self, package_step_data: dict):
        """Retrieve the first package step."""
        return self.get_first_record(
            db=self.db, model=PackageStepMasterDB, **package_step_data
        )

    def get_all_package_step_masters(
        self, db: Session, **kwargs
    ) -> List[PackageStepMasterDB]:
        """Retrieve all package step masters."""
        try:
            self.logger.debug("Retrieving all package step masters with filters: %s", kwargs)
            # package_step_masters = self.get_all(db, PackageStepMasterDB)
            package_step_masters, total_count = self.get_all_by_attributes(
                db, PackageStepMasterDB, **kwargs
            )
            self.logger.debug("Retrieved %s package step masters", len(package_step_masters))
            return package_step_masters
        except Exception as e:
            self.logger.error("Error retrieving all package step masters: %s", str(e), exc_info=True)
            raise

    def create_package_step_master(
        self, package_step_master_data, db: Session
    ) -> PackageStepMasterDB:
        """Create a new package step master."""
        try:
            self.logger.info("Creating new package step master with data: %s", package_step_master_data.dict())
            is_exist = self.validate_create_data(package_step_master_data)
            if is_exist:
                self.logger.warning("Package step already exists with data: %s", package_step_master_data.dict())
                PackageStepErrors().raise_package_step_already_exists_exception(
                    package_step=package_step_master_data.dict()
                )
            package_step_master = self.create(
                db, PackageStepMasterDB, **package_step_master_data.dict()
            )
            self.logger.info("Created package step master with id=%s", package_step_master.id)
            return package_step_master
        except Exception as e:
            self.logger.error("Error creating package step master: %s", str(e), exc_info=True)
            raise

    def create_or_update_package_steps_for_package(
        self, package_id: int, step_ids: list, db: Session
    ) -> List[PackageStepMasterDB]:
        """Create or update package step master associations for a package."""
        try:
            self.logger.info("Creating or updating package steps for package_id=%s with step_ids=%s", 
                            package_id, step_ids)
            # Get all steps and existing package steps in a single query each
            steps = db.query(StepMaster.id).filter(StepMaster.id.in_(step_ids)).all()
            existing_step_ids = (
                db.query(PackageStepMasterDB.step_id)
                .filter_by(package_id=package_id)
                .all()
            )

            # Convert to sets for faster membership testing
            existing_step_ids_set = {step_id for step_id, in existing_step_ids}
            valid_step_ids_set = {step_id for step_id, in steps}

            # Identify steps to add and remove
            steps_to_add = [step_id for step_id in step_ids if step_id in valid_step_ids_set and step_id not in existing_step_ids_set]
            steps_to_remove = [step_id for step_id in existing_step_ids_set if step_id not in step_ids]

            self.logger.debug("Steps to add: %s, Steps to remove: %s", steps_to_add, steps_to_remove)

            # Add new steps
            if steps_to_add:
                new_steps = self.create_steps_for_package(package_id, steps_to_add, db)
                self.logger.info("Added %s new steps to package_id=%s", len(new_steps), package_id)

            # Remove steps that are no longer needed
            if steps_to_remove:
                (
                    db.query(PackageStepMasterDB)
                    .filter(
                        PackageStepMasterDB.package_id == package_id,
                        PackageStepMasterDB.step_id.in_(steps_to_remove),
                    )
                    .delete(synchronize_session=False)
                )
                # Let the caller handle the commit
                # db.commit()
                self.logger.info("Removed %s steps from package_id=%s", len(steps_to_remove), package_id)

            # Return all current package steps
            package_steps = (
                db.query(PackageStepMasterDB)
                .filter_by(package_id=package_id)
                .all()
            )
            return package_steps
        except Exception as e:
            self.logger.error("Error creating or updating package steps for package_id=%s: %s", 
                             package_id, str(e), exc_info=True)
            db.rollback()
            raise

    def create_steps_for_package(
        self, package_id: int, step_ids: List[int], db: Session
    ) -> List[PackageStepMasterDB]:
        """Create steps for a package."""
        try:
            self.logger.info("Creating steps for package_id=%s with step_ids=%s", package_id, step_ids)
            bulk_instances = PackageStepMasterPayloadGenerator(
                package_id=package_id, step_ids=step_ids
            ).generate_bulk_instances()
            if bulk_instances:
                db.add_all(bulk_instances)
                db.commit()
                self.logger.info("Created %s steps for package_id=%s", len(bulk_instances), package_id)
            return bulk_instances
        except Exception as e:
            self.logger.error("Error creating steps for package_id=%s: %s", package_id, str(e), exc_info=True)
            db.rollback()
            raise

    def create_bulk_package_steps(
        self, db: Session, bulk_payload: List[Dict[str, Any]]
    ) -> List[PackageStepMasterDB]:
        """Create a bulk of records."""
        try:
            self.logger.info("Creating bulk package steps with %s items", len(bulk_payload))
            bulk_instances = [PackageStepMasterDB(**payload) for payload in bulk_payload]
            db.add_all(bulk_instances)
            db.commit()
            self.logger.info("Created %s bulk package steps", len(bulk_instances))
            return bulk_instances
        except Exception as e:
            self.logger.error("Error creating bulk package steps: %s", str(e), exc_info=True)
            db.rollback()
            raise

    def update_package_step_master(
        self, package_step_master_id: int, package_step_master_data: dict, db: Session
    ) -> PackageStepMasterDB:
        """Update a package step master."""
        try:
            self.logger.info("Updating package step master with id=%s", package_step_master_id)
            # Check if the package step master exists
            is_exist, package_step_master = self.get_package_step_master_by_id(
                package_step_master_id, db=db
            )
            if not is_exist:
                self.logger.warning("Package step master not found with id=%s", package_step_master_id)
                PackageErrors().raise_package_not_found_exception(
                    package_id=package_step_master_id
                )
            else:
                is_valid = self.validate_update_data(
                    package_step_master_data, package_step_master_id
                )
                if not is_valid:
                    self.logger.warning("Package already exists with name=%s", package_step_master_data.name)
                    PackageErrors().raise_package_already_exists_exception(
                        package_name=package_step_master_data.name
                    )

                package_step_master_data_dict = package_step_master_data.dict()
                # Update the package step master attributes
                for key, value in package_step_master_data_dict.items():
                    setattr(package_step_master, key, value)
                db.commit()
                db.refresh(package_step_master)
                self.logger.info("Updated package step master with id=%s", package_step_master_id)
                return True, package_step_master
        except Exception as e:
            self.logger.error("Error updating package step master with id=%s: %s", 
                             package_step_master_id, str(e), exc_info=True)
            db.rollback()
            raise

    def delete_package_step_master(
        self, package_step_master_id: int, db: Session
    ) -> int:
        """Delete a package step master."""
        try:
            self.logger.info("Deleting package step master with id=%s", package_step_master_id)
            is_exist, package_step_master = self.get_package_step_master_by_id(
                package_step_master_id, db=db
            )
            if not is_exist:
                self.logger.warning("Package step master not found with id=%s", package_step_master_id)
                PackageErrors().raise_package_not_found_exception(
                    package_id=package_step_master_id
                )
            db.delete(package_step_master)
            db.commit()
            self.logger.info("Deleted package step master with id=%s", package_step_master_id)
            return True, None
        except Exception as e:
            self.logger.error("Error deleting package step master with id=%s: %s", 
                             package_step_master_id, str(e), exc_info=True)
            db.rollback()
            raise

    def is_package_step_exists_by_id(self, package_step_id: int) -> bool:
        """Check if a package_step id exists."""
        try:
            self.logger.debug("Checking if package step exists with id=%s", package_step_id)
            is_exist, _ = self.get_package_step_master_by_attribute(
                value=package_step_id, db=self.db, attribute=PackageStepMasterDB.id
            )
            return is_exist
        except Exception as e:
            self.logger.error("Error checking if package step exists with id=%s: %s", 
                             package_step_id, str(e), exc_info=True)
            raise
        finally:
            self.db.close()

    def validate_update_data(self, package_step_data, package_step_id) -> bool:
        """Validate the update data for a package step."""
        try:
            self.logger.debug("Validating update data for package step with id=%s", package_step_id)
            package_step = self.get_first_package_step(package_step_data.dict())
            if package_step and package_step.id != package_step_id:
                self.logger.warning("Package step with same data already exists with id=%s", package_step.id)
                return False
            return True
        except Exception as e:
            self.logger.error("Error validating update data for package step with id=%s: %s", 
                             package_step_id, str(e), exc_info=True)
            raise

    def validate_create_data(self, package_step_data) -> bool:
        """Validate the create data for a package step."""
        try:
            self.logger.debug("Validating create data for package step")
            package_step = self.get_first_package_step(package_step_data.dict())
            if bool(package_step):
                self.logger.warning("Package step with same data already exists with id=%s", 
                                   package_step.id if package_step else None)
                return True
            return False
        except Exception as e:
            self.logger.error("Error validating create data for package step: %s", str(e), exc_info=True)
            raise


class PackageStepMasterPayloadGenerator:
    def __init__(
        self, package_id: int, step_ids: list, model: Any = PackageStepMasterDB
    ):
        self.package_id = package_id
        self.step_ids = step_ids
        self.bulk_instaces = []
        self.model = model
        self.logger = package_logger

    def generate_bulk_instances(self):
        try:
            self.logger.debug("Generating bulk instances for package_id=%s with step_ids=%s", 
                             self.package_id, self.step_ids)
            payload = {}
            for step_id in self.step_ids:
                payload = AttributeMapper(
                    package_id=self.package_id, step_id=step_id
                ).__dict__
                is_exists = PackageStepMasterService().get_first_package_step(
                    package_step_data=payload
                )
                if not is_exists:
                    instance = self.model(**payload)
                    self.bulk_instaces.append(instance)
            self.logger.debug("Generated %s bulk instances", len(self.bulk_instaces))
            return self.bulk_instaces
        except Exception as e:
            self.logger.error("Error generating bulk instances for package_id=%s: %s", 
                             self.package_id, str(e), exc_info=True)
            raise


class PackageStepHelper:

    def __init__(self):
        self.db = SessionLocal()
        self.logger = package_logger

    def get_package_step_id_from_step_type(self, package_id: int, step_type: str):
        try:
            self.logger.debug("Getting package step ID from step_type=%s for package_id=%s", 
                             step_type, package_id)
            step_type = step_type.upper()
            step = self.db.query(StepMaster).filter_by(type=step_type).first()
            if not step:
                self.logger.warning("Step master not found with type=%s", step_type)
                StepMasterErrors().raise_step_master_not_found_exception(type=step_type)
            package_step = (
                self.db.query(PackageStepMasterDB)
                .filter_by(package_id=package_id, step_id=step.id)
                .first()
            )
            if not package_step:
                self.logger.warning("Package step not found for package_id=%s and step_id=%s", 
                                   package_id, step.id)
                PackageStepErrors().raise_package_step_not_found_exception(
                    package_step_id=package_id
                )
            self.logger.debug("Found package step ID %s for step_type=%s and package_id=%s", 
                             package_step.id, step_type, package_id)
            return package_step.id
        except Exception as e:
            self.logger.error("Error getting package step ID from step_type=%s for package_id=%s: %s", 
                             step_type, package_id, str(e), exc_info=True)
            raise
        finally:
            self.db.close()
