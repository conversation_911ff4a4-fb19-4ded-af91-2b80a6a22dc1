from typing import List
from sqlalchemy.orm import Session, joinedload

from app.database.database import engine, Base, SessionLocal
from app.master.exception.errors.scheme_hospital import SchemeHospitalErrors

from app.master.models.scheme_hospital import SchemeHospitalDB


from app.master.models.hospital import HospitalDB


from utils.db import BaseService
from utils.mapper import AttributeMapper
from utils.logger import setup_logger


# Base.metadata.create_all(bind=engine)

scheme_hospital_logger = setup_logger("scheme_hospital.log")


class SchemeHospitalService(BaseService):
    def __init__(self) -> None:
        self.db = SessionLocal()

    def get_scheme_hospital_by_attribute(
        self,
        value: str,
        db: Session,
        attribute: str = None,
        raise_exception: bool = False,
    ):
        """Retrieve a scheme document by a specific attribute."""
        try:
            if not attribute:
                attribute = SchemeHospitalDB.id
            # Query the scheme document from the database
            scheme_hospital = self.get_by_attribute(
                db, SchemeHospitalDB, attribute, value
            )
            if not scheme_hospital:
                if raise_exception:
                    SchemeHospitalErrors().raise_scheme_hospital_not_found_exception(
                        scheme_hospital_id=value
                    )
                scheme_hospital_logger.debug("No scheme hospital found with %s=%s", attribute.key, value)
                return False, None
            return True, scheme_hospital
        except Exception as e:
            scheme_hospital_logger.error("Error retrieving scheme hospital by attribute: %s", str(e))
            raise

    def get_scheme_hospitals_by_scheme_id(self, scheme_id: int, db: Session):
        """Retrieve all scheme hospitals associated with a scheme type."""
        try:
            # Query the database for scheme hospitals
            scheme_hospitals = (
                db.query(SchemeHospitalDB)
                .filter(SchemeHospitalDB.scheme_id == scheme_id)
                .all()
            )
            if not scheme_hospitals:
                scheme_hospital_logger.warning("No scheme hospitals found for scheme_id=%s", scheme_id)
                SchemeHospitalErrors().raise_scheme_hospital_not_found_with_scheme_id_exception(
                    scheme_id=scheme_id
                )
            return scheme_hospitals
        except Exception as e:
            scheme_hospital_logger.error("Error retrieving scheme hospitals by scheme_id=%s: %s", scheme_id, str(e))
            raise

    def assign_scheme_to_hospital(self, scheme_id: int, hospital_id: int, db: Session):
        """Assign a hospital to a scheme type."""
        try:
            payload = AttributeMapper(
                scheme_id=scheme_id, hospital_id=hospital_id
            ).__dict__
            new_scheme_hospital = self.create(db, SchemeHospitalDB, **payload)
            scheme_hospital_logger.info("Assigned hospital_id=%s to scheme_id=%s", hospital_id, scheme_id)
            return new_scheme_hospital
        except Exception as e:
            scheme_hospital_logger.error("Error assigning hospital_id=%s to scheme_id=%s: %s", 
                                        hospital_id, scheme_id, str(e))
            raise

    def assign_hospital_to_schemes(self, hospital_id: int, scheme_ids: List[int]):
        """Assign a hospital to multiple scheme types."""
        try:
            new_scheme_hospitals = [
                SchemeHospitalDB(scheme_id=scheme_id, hospital_id=hospital_id)
                for scheme_id in scheme_ids
            ]
            self.db.bulk_save_objects(new_scheme_hospitals)
            self.db.commit()
            self.db.close()
            scheme_hospital_logger.info("Assigned hospital_id=%s to %d schemes", hospital_id, len(scheme_ids))
            return new_scheme_hospitals
        except Exception as e:
            scheme_hospital_logger.error("Error assigning hospital_id=%s to schemes: %s", hospital_id, str(e))
            raise

    def assign_hospitals_to_scheme_type(self, scheme_id: int, hospital_ids: list):
        """
        Assign hospitals to a scheme type.

        Args:
            scheme_id (int): The ID of the scheme type.
            hospital_ids (List[int]): The IDs of the hospitals to assign.

        Returns:
            Tuple[bool, Union[List[SchemeHospitalDB], Tuple[bool, str]]]: A tuple containing a boolean indicating success or failure,
            and either the list of assigned scheme hospitals objects or an error message.
        """
        try:
            # Fetch all hospitals at once
            hospitals = (
                self.db.query(HospitalDB).filter(HospitalDB.id.in_(hospital_ids)).all()
            )
            existing_hospital_ids = {hospital.id for hospital in hospitals}
            # Fetch all existing scheme-hospital relationships for the given scheme_id at once
            scheme_hospitals = (
                self.db.query(SchemeHospitalDB)
                .filter(
                    SchemeHospitalDB.scheme_id == scheme_id,
                    SchemeHospitalDB.hospital_id.in_(hospital_ids),
                )
                .all()
            )
            existing_scheme_hospital_ids = {
                scheme_hospital.hospital_id for scheme_hospital in scheme_hospitals
            }
            # Determine which hospital_ids are not found and which are new
            new_hospital_ids = existing_hospital_ids - existing_scheme_hospital_ids
            # Use a bulk insert operation to add all new scheme-hospital relationships at once
            new_scheme_hospitals = [
                SchemeHospitalDB(scheme_id=scheme_id, hospital_id=hospital_id)
                for hospital_id in new_hospital_ids
            ]
            self.db.bulk_save_objects(new_scheme_hospitals)
            self.db.commit()
            self.db.close()
            scheme_hospital_logger.info("Assigned %d hospitals to scheme_id=%s", len(new_hospital_ids), scheme_id)
            return new_scheme_hospitals
        except Exception as e:
            scheme_hospital_logger.error("Error assigning hospitals to scheme_id=%s: %s", scheme_id, str(e))
            raise

    def get_scheme_hospitals(self, scheme_id):
        """Retrieve all hospitals associated with a scheme type."""
        scheme_hospitals = self.get_scheme_hospitals_by_scheme_id(
            scheme_id=scheme_id, db=self.db
        )
        # Query the database for scheme hospitals
        scheme_hospitals = (
            self.db.query(SchemeHospitalDB)
            .filter(SchemeHospitalDB.scheme_id == scheme_id)
            .all()
        )
        # Return the scheme hospitals
        return scheme_hospitals

    def get_scheme_hospitals_by_scheme_type_and_hospital_ids(
        self, scheme_id: int, hospital_ids: list
    ):
        """
        Retrieve scheme hospitals by scheme type ID and hospital IDs.

        Args:
            scheme_id (int): The ID of the scheme type.
            hospital_ids (List[int]): The IDs of the hospitals.

        Returns:
            List[SchemeHospitalDB]: A list of scheme hospital objects.
        """
        try:
            # Query the database for scheme hospitals
            scheme_hospitals = (
                self.db.query(SchemeHospitalDB)
                .filter(
                    SchemeHospitalDB.scheme_id == scheme_id,
                    SchemeHospitalDB.hospital_id.in_(hospital_ids),
                )
                .all()
            )
            scheme_hospital_logger.debug("Found %d scheme hospitals for scheme_id=%s and hospital_ids=%s", 
                                       len(scheme_hospitals), scheme_id, hospital_ids)
            return scheme_hospitals
        except Exception as e:
            scheme_hospital_logger.error("Error retrieving scheme hospitals by scheme_id=%s and hospital_ids: %s", 
                                       scheme_id, str(e))
            raise

    def remove_hospitals_from_scheme_type(self, scheme_id: int, hospital_ids: list):
        """
        Remove assigned hospitals from a scheme type.

        Args:
            scheme_id (int): The ID of the scheme type.
            hospital_ids (List[int]): The IDs of the hospitals to remove.

        Returns:
            Tuple[bool, str]: A tuple containing a boolean indicating success or failure,
            and a message indicating the result of the operation.
        """
        try:
            self.get_scheme_hospitals_by_scheme_id(scheme_id=scheme_id, db=self.db)
            # Remove the scheme-hospital relationships
            self.db.query(SchemeHospitalDB).filter(
                SchemeHospitalDB.scheme_id == scheme_id,
                SchemeHospitalDB.hospital_id.in_(hospital_ids),
            ).delete(synchronize_session=False)
            self.db.commit()
            self.db.close()
            scheme_hospital_logger.info("Removed %d hospitals from scheme_id=%s", len(hospital_ids), scheme_id)
            return scheme_id
        except Exception as e:
            scheme_hospital_logger.error("Error removing hospitals from scheme_id=%s: %s", scheme_id, str(e))
            raise

    def is_scheme_hospital_exists_by_scheme_id(self, scheme_id: int) -> bool:
        """Check if a scheme hospital exists by scheme_id."""
        try:
            is_exist, scheme_hospital = self.get_scheme_hospital_by_attribute(
                value=scheme_id, db=self.db, attribute=SchemeHospitalDB.scheme_id
            )
            return is_exist
        except Exception as e:
            scheme_hospital_logger.error("Error checking if scheme hospital exists for scheme_id=%s: %s", 
                                       scheme_id, str(e))
            raise
        finally:
            self.db.close()

    def get_first_scheme_hospital(self, scheme_hospital_payload):
        """Retrieve the first scheme hospital."""
        return self.get_first_record(
            db=self.db, model=SchemeHospitalDB, **scheme_hospital_payload
        )

    def is_scheme_hospital_unique(self, scheme_hospital_payload) -> bool:
        """Check if a scheme hospital is unique."""
        is_scheme_hospital_exists = self.get_first_scheme_hospital(
            scheme_hospital_payload
        )
        return bool(is_scheme_hospital_exists)

    def is_hospital_assigned_to_schemes(self, scheme_ids, hospital_id: int):
        """Check if a hospital is assigned to any scheme type."""
        try:
            # Query the database for scheme hospitals
            scheme_hospitals = (
                self.db.query(SchemeHospitalDB)
                .filter(
                    SchemeHospitalDB.scheme_id.in_(scheme_ids),
                    SchemeHospitalDB.hospital_id == hospital_id,
                )
                .all()
            )
            is_assigned = bool(scheme_hospitals)
            scheme_hospital_logger.debug("Hospital_id=%s is%s assigned to schemes %s", 
                                      hospital_id, "" if is_assigned else " not", scheme_ids)
            return is_assigned
        except Exception as e:
            scheme_hospital_logger.error("Error checking if hospital_id=%s is assigned to schemes: %s", 
                                      hospital_id, str(e))
            raise
