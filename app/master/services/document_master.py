import datetime
import imp
import re
from datetime import datetime
from typing import List, Optional

import pandas as pd
from sqlalchemy.orm import Session, joinedload

from app.database.database import SessionLocal, engine
from app.master.exception.custom.document_master import DocumentMasterExceptions
from app.master.exception.errors.document_master import DocumentMasterErrors
from app.master.models.document_master import DocumentMaster
from app.master.models.package import PackageStepDocumentDB, PackageStepMasterDB
from app.master.models.scheme_document import SchemeDocumentDB
from app.master.services.scheme_document import SchemeDocumentService
from utils.db import BaseService, QueryWithCount
from utils.document_master import (
    collect_data,
    create_document_dataframe,
    get_data,
    get_document_master_ids,
)
from utils.logger import setup_logger
from utils.pagination import PaginationHelper

from ..utils.execute.package_step import insert_all_documents_scheme_master

document_logger = setup_logger("document_master.log")


class DocumentMasterService(BaseService):
    """
    Service class for managing document master-related operations.
    """

    def __init__(self) -> None:
        self.db = SessionLocal()

    def create_document_scheme_master(self, db: Session, scheme_id: str):
        """Create steps for a package."""
        try:
            return insert_all_documents_scheme_master(db=db, scheme_id=scheme_id)
        except Exception as e:
            document_logger.error(str(e))
            raise

    def get_scheme_master_ids(self, db, scheme_name: str):
        try:
            df = pd.read_sql_query(
                f"select id from scheme_type_master where type = '{scheme_name}';",
                engine,
            )
            if len(df) == 0:
                raise Exception("Scheme name does not exist")
            return df
        except Exception as e:
            document_logger.error(str(e))
            raise

    def get_document_by_attribute(self, value: str, db: Session, attribute: str = None):
        """Retrieve a document master by a specific attribute."""
        try:
            if not attribute:
                attribute = DocumentMaster.id
            # Query the document master from the database
            document = self.get_by_attribute(db, DocumentMaster, attribute, value)
            if not document:
                error = (
                    DocumentMasterExceptions.generate_document_master_not_found_error(
                        document_id=value
                    )
                )
                return False, error

            return True, document
        except Exception as e:
            document_logger.error(str(e))
            raise

    def get_or_raise_document_by_attribute(
        self, value: str, db: Session, attribute: str = None
    ):
        """Retrieve a document master by a specific attribute or raise an exception."""
        is_exist, document = self.get_document_by_attribute(value, db, attribute)
        if not is_exist:
            DocumentMasterErrors().raise_document_not_exists_exception(
                document_id=value
            )
        return document

    def get_document_master_or_raise(self, document_id: int, db: Session):
        """Retrieve a document master by ID or raise an exception."""
        document = self.get_by_attribute(
            db, DocumentMaster, DocumentMaster.id, document_id
        )
        if not document:
            DocumentMasterErrors().raise_document_not_exists_exception(
                document_id=document_id
            )
        return document

    def get_document_by_id(self, document_id: int, db: Session):
        """Retrieve a document master by ID."""

        try:
            document = self.get_or_raise_document_by_attribute(value=document_id, db=db)
            return document
        except Exception as e:
            document_logger.error(str(e))
            raise

    def get_all_documents(self, db: Session):
        """Retrieve all document masters."""

        try:
            # Query all document masters from the database.
            documents = self.get_all(db, DocumentMaster)
            return documents
        except Exception as e:
            document_logger.error(str(e))
            raise

    def create_document(self, document_data, db: Session):
        """Create a new document master."""
        try:
            document_payload = document_data.dict()
            scheme_ids = document_payload.pop("scheme_ids")

            with db.begin():
                document = self.create_document_directly(document_payload, db)
                self.assign_document_to_schemes_directly(document.id, scheme_ids, db)
                db.refresh(document)

            return True, document
        except Exception as e:
            document_logger.error(str(e))
            raise

    def create_document_directly(self, document_payload: dict, db: Session):
        """Directly create a document master."""
        try:
            document = DocumentMaster(
                name=document_payload["name"],
                is_required=document_payload.get("is_required", False),
                is_geotag_required=document_payload.get("is_geotag_required", False),
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )
            db.add(document)
            db.flush()  # Ensure document.id is generated
            return document
        except Exception as e:
            document_logger.error(str(e))
            raise

    def assign_document_to_schemes_directly(
        self, document_id: int, scheme_ids: list, db: Session
    ):
        """Directly assign a document to multiple schemes."""
        try:
            new_scheme_documents = [
                SchemeDocumentDB(scheme_id=scheme_id, document_id=document_id)
                for scheme_id in scheme_ids
            ]
            db.bulk_save_objects(new_scheme_documents)
        except Exception as e:
            document_logger.error(str(e))
            raise

    def filter_documents(
        self,
        name: Optional[str],
        db: Session,
        page: int,
        limit: int,
    ):
        """Filter documents by code and name."""
        query_with_count = QueryWithCount(self.init_query(db=db, model=DocumentMaster))

        query_with_count.apply_filter(db, DocumentMaster, DocumentMaster.name, name)

        query_with_count.query = PaginationHelper.paginate_query(
            query_with_count.query, page, limit
        )
        return query_with_count.query.all(), query_with_count.query.count()

    def filter_and_sort_documens(
        self,
        name: Optional[str],
        scheme: Optional[int],
        is_geotag_required: Optional[bool],
        order_by: Optional[str],
        db: Session,
        page: int,
        limit: int,
    ):
        """Filter hospitals by name and city."""
        try:
            from app.master.models.scheme_type import SchemeType

            query = self.init_query(db=db, model=DocumentMaster)

            if name is not None:
                query = self.filter_value_like(
                    db, DocumentMaster, DocumentMaster.name, name
                )
            if scheme is not None:
                query = (
                    query.join(DocumentMaster.schemes)
                    .filter(SchemeType.type.like(f"%{scheme}%"))
                    .distinct()
                )
            if is_geotag_required is not None:
                query = query.filter(
                    DocumentMaster.is_geotag_required == is_geotag_required
                )

            query = self.order_query(query, DocumentMaster, order_by)
            total_count = query.count()
            query = PaginationHelper.paginate_query(query, page, limit)
            return query.all(), total_count
        except Exception as e:
            document_logger.error(str(e))
            raise

    def update_document(self, document_id: int, document_data: dict, db: Session):
        """Update a document master."""
        try:
            with db.begin():
                # Fetch document and current scheme IDs in a single query
                document = (
                    db.query(DocumentMaster)
                    .filter(DocumentMaster.id == document_id)
                    .options(joinedload(DocumentMaster.schemes))
                    .one_or_none()
                )
                if not document:
                    raise DocumentMasterErrors().raise_document_not_exists_exception(
                        document_id=document_id
                    )

                document_payload = document_data.dict()
                scheme_ids = document_payload.pop("scheme_ids", None)

                # Update document details
                self.update_document_directly(document, document_payload, db)

                # Update document schemes if scheme_ids are provided
                if scheme_ids is not None:
                    self.update_document_schemes(document, scheme_ids, db)

                db.refresh(document)

            return document
        except Exception as e:
            document_logger.error(str(e))
            raise

    def update_document_directly(
        self, document: DocumentMaster, document_payload: dict, db: Session
    ):
        """Directly update a document master."""
        try:
            for key, value in document_payload.items():
                setattr(document, key, value)
            document.updated_at = datetime.now()
            db.add(document)
            db.flush()
            db.refresh(document)
        except Exception as e:
            document_logger.error(str(e))
            raise

    def update_document_schemes(
        self, document: DocumentMaster, scheme_ids: list, db: Session
    ):
        """Update schemes for a document."""
        try:
            # Current scheme IDs associated with the document
            existing_scheme_ids = {scheme.id for scheme in document.schemes}

            # Determine which schemes to remove and which to add
            scheme_ids_set = set(scheme_ids)
            schemes_to_remove = existing_scheme_ids - scheme_ids_set
            schemes_to_add = scheme_ids_set - existing_scheme_ids

            # Remove schemes no longer associated
            if schemes_to_remove:
                db.query(SchemeDocumentDB).filter(
                    SchemeDocumentDB.document_id == document.id,
                    SchemeDocumentDB.scheme_id.in_(schemes_to_remove),
                ).delete(synchronize_session=False)

            # Add new schemes
            if schemes_to_add:
                new_scheme_documents = [
                    SchemeDocumentDB(scheme_id=scheme_id, document_id=document.id)
                    for scheme_id in schemes_to_add
                ]
                db.bulk_save_objects(new_scheme_documents)
        except Exception as e:
            document_logger.error(str(e))
            raise

    def get_document_by_id(self, document_id: int, db: Session):
        """Retrieve a document master by ID."""
        try:
            document = (
                db.query(DocumentMaster)
                .filter(DocumentMaster.id == document_id)
                .first()
            )
            if not document:
                raise DocumentMasterErrors().raise_document_not_exists_exception(
                    document_id=document_id
                )
            return document
        except Exception as e:
            document_logger.error(str(e))
            raise

    def delete_document(self, document_id: int, db: Session):
        """Delete a document master."""

        try:
            document = self.get_document_master_or_raise(document_id, db=db)
            if document.package_step_document:
                raise DocumentMasterErrors().raise_document_master_has_packages_error(
                    document_id=document_id
                )
            elif document.case_step_documents:
                raise DocumentMasterErrors().raise_document_master_has_associated_cases_error(
                    document_id=document_id
                )
            else:
                is_deleted = self.delete(db, DocumentMaster, document_id)
                return is_deleted, document_id
        except Exception as e:
            document_logger.error(str(e))
            raise

    def is_document_exists(self, document_name: str):
        """Check if a document master exists."""

        try:
            is_exist, document = self.get_document_by_name(document_name)
            return is_exist
        except Exception as e:
            raise

    def get_document_by_name(self, document_name: str):
        """Retrieve a document master by name."""
        try:
            is_exist, document = self.get_document_by_attribute(
                value=document_name, db=self.db, attribute=DocumentMaster.name
            )
            if not is_exist:
                error = (
                    DocumentMasterExceptions.generate_document_master_not_found_error(
                        document_name=document_name
                    )
                )
                return False, error

            return True, document
        except Exception as e:
            document_logger.error(str(e))
            raise

    def is_document_exists_by_id(self, document_id: int):
        """Check if a document master exists."""
        try:
            is_exist, document = self.get_document_by_attribute(document_id, self.db)
            return is_exist
        except Exception as e:
            raise

    def get_first_document(self, document_data: dict):
        """Retrieve the first document."""
        return self.get_first_record(db=self.db, model=DocumentMaster, **document_data)

    def get_first_package(self, package_data: dict):
        """Retrieve the first package."""
        return self.get_first_record(db=self.db, model=DocumentMaster, **package_data)

    def is_package_data_unique(self, package_payload) -> bool:
        """Check if a package is unique."""
        is_package_exists = self.get_first_package(package_payload)
        return bool(is_package_exists)

    def check_documents_exist_by_ids(self, document_ids: List[int]):
        """Check if documents exist with the given IDs."""
        missing_ids = []
        for document_id in document_ids:
            is_exist = self.is_document_exists_by_id(document_id)
            if not is_exist:
                missing_ids.append(document_id)
        return missing_ids

    def check_document_belongs_to_yojana(self, document_data):
        """Check if a document master belongs to a yojana."""
        if not document_data.is_mjp_jay and not document_data.is_ab_pmjay:
            return (
                False,
                DocumentMasterExceptions.generate_document_master_should_belong_to_yojana_error(
                    is_ab_pmjay=document_data.is_ab_pmjay,
                    is_mjp_jay=document_data.is_mjp_jay,
                ),
            )
        return True, None

    def bulk_upload_documents(
        self, dataframe: pd.DataFrame, db: Session, scheme_id: int
    ):
        """Bulk upload documents from a dataframe to the document master table."""
        try:
            """Update column names to uppercase and replace spaces with underscores."""
            dataframe.rename(
                columns=lambda x: x.upper().replace(" ", "_"), inplace=True
            )
            """Clean the data in the dataframe."""
            # import pdb;pdb.set_trace()
            copy_df = dataframe.copy()
            documents = []
            unique_documents = unique_values(dataframe)
            for doc in unique_documents:
                # Check if the document contains 'op doc' or 'Optional Document' to mark as non-mandatory
                is_required = not ('op doc' in doc.lower() or 'Optional Document' in doc)
                if not is_required:
                    continue
                """Create a dictionary with the document data."""
                document_data = {
                    "name": doc,
                    "is_required": is_required,
                    "is_geotag_required": False,
                    "created_at": datetime.now(),
                    "updated_at": datetime.now(),
                }

                # Check if the document name already exists in the database
                if self.is_document_exists(document_data["name"]):
                    continue  # Skip the insert if the document name already exists
                document = self.create(db, DocumentMaster, **document_data)

                documents.append(document)

            document_id_df = get_document_master_ids(
                table_name="document_master", documents=unique_documents, engine=engine
            )
            document_id_df.rename(columns={"id": "document_id"}, inplace=True)

            self.create_document_scheme_master(db=db, scheme_id=scheme_id)

            status = create_mapping(copy_df, db)

            return documents
        except Exception as e:
            document_logger.error(str(e))
            raise



class DocumentMasterHelper:
    def __init__(self):
        self.db = SessionLocal()

    def get_document_ids(self, document_names: List[str]) -> List[int]:
        """Retrieve the IDs of documents with the provided names."""
        # Ensure document_names is a list
        if not isinstance(document_names, list):
            document_names = list(document_names)

        # Query the database for documents with names in the provided list
        documents = (
            self.db.query(DocumentMaster)
            .filter(DocumentMaster.name.in_(document_names))
            .all()
        )
        # Extract the IDs of the documents
        document_ids = list(map(lambda document: document.id, documents))
        return document_ids


def split_by_pattern(input_string, pattern_str, strip_char="/"):
    """
    Splits the input string by the pattern and strips the characters from the split parts.
    
    This function is designed to separate required documents from optional documents.
    Documents with forward slashes in their names (like "Photograph/Radiographic Image/Cd")
    should be treated as a single document.
    """
    # Handle None or empty values
    if input_string is None or str(input_string).strip() == "":
        return "", ""
        
    input_string = str(input_string)  # Ensure string type
    
    # Check if the input contains parentheses which typically indicate optional documents
    if "(" in input_string and ")" in input_string:
        # Use regex to split required and optional parts
        pattern = re.compile(pattern_str)
        split_strings = pattern.split(input_string, 1)
        split_strings = [part for part in split_strings if part is not None]
        
        # Get the required and optional parts
        part1 = split_strings[0].strip() if len(split_strings) > 0 else ""
        part2 = split_strings[1].strip() if len(split_strings) > 1 else ""
        
        return part1, part2
    else:
        # If no parentheses, all documents are required
        return input_string.strip(), ""


def split_ignore_brackets(input_string, separator=","):
    """
    Splits the input string by the separator but ignores separators inside brackets.
    Only splits by comma by default, preserving forward slashes as part of document names.
    """
    # Handle None or empty values
    if input_string is None or str(input_string).strip() == "":
        return []
        
    input_string = str(input_string)  # Ensure string type
    
    # Only split by comma, not by forward slash
    separator = ","
    
    parts = []
    bracket_level = 0
    current_part = []

    for char in input_string:
        if char == "(":
            bracket_level += 1
        elif char == ")":
            bracket_level -= 1
        elif char == separator and bracket_level == 0:
            parts.append("".join(current_part).strip())
            current_part = []
            continue
        current_part.append(char)

    # Add the last part
    if current_part:
        parts.append("".join(current_part).strip())

    return parts


def split_to_list(s):
    return s.split("/")


def split_to_list1(s):
    return s.split(",")


# pattern_pre_doc = r"(?=/Pre op doc)"
# pattern_post_doc = r"(?=/Post op doc)"
# pattern_pre_inve = r"(?=,Post op doc)"
pattern_pre_doc = r"(?=/Optional Document)"
pattern_post_doc = r"(?=/Optional Document)"
pattern_pre_inve = r"(?=,Optional Document)"


def clean_data(dataframe):
    """Clean the data in the dataframe."""
    df = dataframe.copy()
    
    
    # Standardize column names first
    df.rename(columns=lambda x: x.upper().replace(" ", "_"), inplace=True)
    
    # Check and handle column name variations
    column_mappings = {
        'PRE_INVESTIGATIONS': 'PRE_INVESTIGATION',
        'POST_INVESTIGATIONS': 'POST_INVESTIGATION',
        'DURING_TREATMENT_DOCUMENTS': 'DURING_TREATMENT_DOCUMENTS',
        'PRE_INVESTIGATION_DOCUMENTS': 'PRE_INVESTIGATION',
        'POST_INVESTIGATION_DOCUMENTS': 'POST_INVESTIGATION',
        'PRE_AUTH_DOCUMENTS': 'FIXED_PREAUTH_REQUIRED_DOCUMENTS',
        'DISCHARGE_DOCUMENTS': 'FIXED_DISCHARGE_REQUIRED_DOCUMENTS'
    }
    
    for original, target in column_mappings.items():
        if original in df.columns and target not in df.columns:
            df[target] = df[original]
    
    # Ensure all required columns exist with default empty values if missing
    required_columns = [
        'PRE_INVESTIGATION', 'POST_INVESTIGATION', 'DURING_TREATMENT_DOCUMENTS',
        'FIXED_PREAUTH_REQUIRED_DOCUMENTS', 'FIXED_DISCHARGE_REQUIRED_DOCUMENTS'
    ]
    
    for col in required_columns:
        if col not in df.columns:
            df[col] = ""
    
    pattern_post_doc = r"(.*?)(?:\s*\((.*?)\))?"
    
    # Only apply split_by_pattern if the columns exist
    if "FIXED_PREAUTH_REQUIRED_DOCUMENTS" in df.columns:
        df[["FIXED_PREAUTH_REQUIRED_DOCUMENTS", "PREAUTH_OPTIONAL_DOCUMENTS"]] = df[
            "FIXED_PREAUTH_REQUIRED_DOCUMENTS"
        ].apply(lambda x: pd.Series(split_by_pattern(x, pattern_post_doc)))
    else:
        df["PREAUTH_OPTIONAL_DOCUMENTS"] = ""
    
    if "FIXED_DISCHARGE_REQUIRED_DOCUMENTS" in df.columns:
        df[["FIXED_DISCHARGE_REQUIRED_DOCUMENTS", "DISCHARGE_OPTIONAL_DOCUMENTS"]] = df[
            "FIXED_DISCHARGE_REQUIRED_DOCUMENTS"
        ].apply(lambda x: pd.Series(split_by_pattern(x, pattern_post_doc)))
    else:
        df["DISCHARGE_OPTIONAL_DOCUMENTS"] = ""

    if "PRE_INVESTIGATION" in df.columns:
        df[["PRE_INVESTIGATION", "PRE_INVESTIGATION_OPTIONAL"]] = df[
            "PRE_INVESTIGATION"
        ].apply(lambda x: pd.Series(split_by_pattern(x, pattern_post_doc, strip_char=",")))
    else:
        df["PRE_INVESTIGATION_OPTIONAL"] = ""

    if "POST_INVESTIGATION" in df.columns:
        df[["POST_INVESTIGATION", "POST_INVESTIGATION_OPTIONAL"]] = df[
            "POST_INVESTIGATION"
        ].apply(lambda x: pd.Series(split_by_pattern(x, pattern_post_doc, strip_char=",")))
    else:
        df["POST_INVESTIGATION_OPTIONAL"] = ""
        
    if "DURING_TREATMENT_DOCUMENTS" in df.columns:
        df[["DURING_TREATMENT_DOCUMENTS", "DURING_TREATMENT_DOCUMENTS_OPTIONAL"]] = df[
            "DURING_TREATMENT_DOCUMENTS"
        ].apply(
            lambda x: pd.Series(split_by_pattern(str(x), pattern_post_doc, strip_char=","))
        )    
    return df


def unique_values(dataframe):
    df = clean_data(dataframe)
    """ Display unique values in the DataFrame. """
    
    # Check if columns exist and print sample values
    document_columns = [
        "FIXED_PREAUTH_REQUIRED_DOCUMENTS", "PREAUTH_OPTIONAL_DOCUMENTS",
        "FIXED_DISCHARGE_REQUIRED_DOCUMENTS", "DISCHARGE_OPTIONAL_DOCUMENTS",
        "PRE_INVESTIGATION", "PRE_INVESTIGATION_OPTIONAL",
        "POST_INVESTIGATION", "POST_INVESTIGATION_OPTIONAL",
        "DURING_TREATMENT_DOCUMENTS", "DURING_TREATMENT_DOCUMENTS_OPTIONAL"
    ]
    
    # for col in document_columns:
    #     if col in df.columns:
    #         print(f"Column {col} exists with sample: {df[col].head(2).tolist()}")
    #     else:
    #         print(f"Column {col} does not exist in dataframe")
    
    # Process each document column
    document_lists = {}
    for col in document_columns:
        if col in df.columns:
            document_lists[col] = df[col].apply(split_ignore_brackets).tolist()
    
    # Flatten all document lists
    all_documents = []
    for col, doc_lists in document_lists.items():
        flattened = [item.strip() for sublist in doc_lists for item in sublist if item.strip()]
        all_documents.extend(flattened)
    
    # Get unique documents
    unique_documents = list(set(all_documents))
    filtered_documents = [item for item in unique_documents if item not in ["", " ", "None","NaN","nan"]]
    
    return filtered_documents


def create_mapping(dataframe, db):
    try:
        df = clean_data(dataframe)
        """
            create empty dataframes and then append the data to the dataframes.
        """
        PRE_INVESTIGATION_DF = create_document_dataframe(
            df, "PRE_INVESTIGATION", "PRE_INVESTIGATION"
        )
        POST_INVESTIGATION_DF = create_document_dataframe(
            df, "POST_INVESTIGATION", "CLAIM"
        )
        FIXED_PREAUTH_REQUIRED_DOCUMENTS_DF = create_document_dataframe(
            df, "FIXED_PREAUTH_REQUIRED_DOCUMENTS", "PRE_AUTHORIZATION"
        )
        FIXED_PREAUTH_OPTIONAL_DOCUMENTS_DF = create_document_dataframe(
            df, "PREAUTH_OPTIONAL_DOCUMENTS", "PRE_AUTHORIZATION"
        )
        FIXED_DISCHARGE_REQUIRED_DOCUMENTS_DF = create_document_dataframe(
            df, "FIXED_DISCHARGE_REQUIRED_DOCUMENTS", "DISCHARGE"
        )
        FIXED_DISCHARGE_OPTIONAL_DOCUMENTS_DF = create_document_dataframe(
            df, "DISCHARGE_OPTIONAL_DOCUMENTS", "DISCHARGE"
        )
        TREATMENT_DF = create_document_dataframe(
            df, "DURING_TREATMENT_DOCUMENTS", "TREATMENT"
        )

        TREATMENT_DF_OPTIONAL = create_document_dataframe(
            df, "DURING_TREATMENT_DOCUMENTS_OPTIONAL", "TREATMENT"
        )
        """
            Fetch tables from database.
        """
        DOCUMENT_MASTER_DF = get_data("document_master", engine)
        STEP_MASTER_DF = get_data("step_master", engine)
        PACKAGE_MASTER_DF = get_data("package_master", engine)
        PACKAGE_MASTER_DF["procedure_name"] = (
            PACKAGE_MASTER_DF["procedure_name"].str.lower().str.replace(" ", "")
        )
        PACKAGE_STEP_MASTER_DF = get_data("package_step_master", engine)
        PACKAGE_STEP_DOCUMENT_MASTER_DF = get_data(
            "package_step_document_master", engine
        )

        """
            Generate mapping for each dataframe.
        """

        PRE_INVESTIGATION_FINAL_DF = collect_data(
            PRE_INVESTIGATION_DF,
            DOCUMENT_MASTER_DF,
            STEP_MASTER_DF,
            PACKAGE_MASTER_DF,
            PACKAGE_STEP_MASTER_DF,
            PACKAGE_STEP_DOCUMENT_MASTER_DF,
        )
        POST_INVESTIGATION_FINAL_DF = collect_data(
            POST_INVESTIGATION_DF,
            DOCUMENT_MASTER_DF,
            STEP_MASTER_DF,
            PACKAGE_MASTER_DF,
            PACKAGE_STEP_MASTER_DF,
            PACKAGE_STEP_DOCUMENT_MASTER_DF,
        )
        FIXED_PREAUTH_REQUIRED_DOCUMENTS_FINAL_DF = collect_data(
            FIXED_PREAUTH_REQUIRED_DOCUMENTS_DF,
            DOCUMENT_MASTER_DF,
            STEP_MASTER_DF,
            PACKAGE_MASTER_DF,
            PACKAGE_STEP_MASTER_DF,
            PACKAGE_STEP_DOCUMENT_MASTER_DF,
        )
        FIXED_PREAUTH_OPTIONAL_DOCUMENTS_DF = collect_data(
            FIXED_PREAUTH_OPTIONAL_DOCUMENTS_DF,
            DOCUMENT_MASTER_DF,
            STEP_MASTER_DF,
            PACKAGE_MASTER_DF,
            PACKAGE_STEP_MASTER_DF,
            PACKAGE_STEP_DOCUMENT_MASTER_DF,
        )
        FIXED_DISCHARGE_REQUIRED_DOCUMENTS_FINAL_DF = collect_data(
            FIXED_DISCHARGE_REQUIRED_DOCUMENTS_DF,
            DOCUMENT_MASTER_DF,
            STEP_MASTER_DF,
            PACKAGE_MASTER_DF,
            PACKAGE_STEP_MASTER_DF,
            PACKAGE_STEP_DOCUMENT_MASTER_DF,
        )
        FIXED_DISCHARGE_OPTIONAL_DOCUMENTS_DF = collect_data(
            FIXED_DISCHARGE_OPTIONAL_DOCUMENTS_DF,
            DOCUMENT_MASTER_DF,
            STEP_MASTER_DF,
            PACKAGE_MASTER_DF,
            PACKAGE_STEP_MASTER_DF,
            PACKAGE_STEP_DOCUMENT_MASTER_DF,
        )
        TREATMENT_FINAL_DF = collect_data(
            TREATMENT_DF,
            DOCUMENT_MASTER_DF,
            STEP_MASTER_DF,
            PACKAGE_MASTER_DF,
            PACKAGE_STEP_MASTER_DF,
            PACKAGE_STEP_DOCUMENT_MASTER_DF,
        )
        TREATMENT_FINAL_DF_OPTIONAL = collect_data(
            TREATMENT_DF_OPTIONAL,
            DOCUMENT_MASTER_DF,
            STEP_MASTER_DF,
            PACKAGE_MASTER_DF,
            PACKAGE_STEP_MASTER_DF,
            PACKAGE_STEP_DOCUMENT_MASTER_DF,
        )

        """"
            Drop duplicates and Merge The dataframes.
        """

        # List of all dataframes
        dataframes = [
            PRE_INVESTIGATION_FINAL_DF,
            POST_INVESTIGATION_FINAL_DF,
            FIXED_PREAUTH_REQUIRED_DOCUMENTS_FINAL_DF,
            FIXED_PREAUTH_OPTIONAL_DOCUMENTS_DF,
            FIXED_DISCHARGE_REQUIRED_DOCUMENTS_FINAL_DF,
            FIXED_DISCHARGE_OPTIONAL_DOCUMENTS_DF,
            TREATMENT_FINAL_DF,
            TREATMENT_FINAL_DF_OPTIONAL,
        ]

        # Concatenate all dataframes
        combined_df = pd.concat(dataframes)

        combined_df.drop_duplicates(inplace=True)
        # combined_df.to_excel('/home/<USER>/Desktop/combined_df.xlsx', index=False)

        # Get the existing document IDs from the document_master table
        existing_package_master_document_id_df = get_data(
            "package_step_document_master", engine
        )

        # Filter the combined_df dataframe to only include rows with existing document IDs
        # Merge DataFrames to find matches
        merged_df = pd.merge(
            existing_package_master_document_id_df,
            combined_df,
            on=["package_step_id", "document_id"],
            how="inner",
        )

        # Remove matched rows from existing_package_master_document_id_df
        filtered_df = existing_package_master_document_id_df[
            ~existing_package_master_document_id_df.set_index(
                ["package_step_id", "document_id"]
            ).index.isin(merged_df.set_index(["package_step_id", "document_id"]).index)
        ]

        # Check if there are no existing package master document IDs
        if len(existing_package_master_document_id_df) == 0:
            existing_document_ids = pd.read_sql_query(
                "SELECT id FROM document_master", engine
            )["id"].tolist()

            # Filter the combined_df dataframe to only include rows with existing document IDs
            filtered_df = combined_df[
                combined_df["document_id"].isin(existing_document_ids)
            ]
            # Insert all rows from combined_df into package_step_document_master table
            filtered_df.to_sql(
                "package_step_document_master",
                con=engine,
                if_exists="append",
                index=False,
            )
        else:
            # Insert the filtered_df into package_step_document_master table

            existing_package_document_ids = pd.read_sql_query(
                "SELECT id FROM package_step_document_master", engine
            )

            combined_df["id"] = 0
            document_logger.debug("filtered_df columns: %s", filtered_df.columns)
            merge_final_df = pd.merge(
                existing_package_master_document_id_df,
                combined_df,
                on=["package_step_id", "document_id"],
                how="outer",
                indicator=True,
            )
            unique_df = merge_final_df[merge_final_df["_merge"] == "right_only"]
            unique_df.drop("_merge", axis=1, inplace=True)
            unique_df.reset_index(drop=True, inplace=True)
            unique_df.drop(["id_x", "id_y"], axis=1, inplace=True)
            unique_df = unique_df[unique_df["document_id"] != 0]

            # new_df = filtered_df.drop_duplicates(subset=['package_step_id', 'document_id'], keep='first')
            # existing_package_master_document_id_df.drop('id', axis=1, inplace=True)
            # final_data = pd.merge(new_df, existing_package_master_document_id_df, on=['package_step_id', 'document_id'], how='inner')
            # unique_df.to_excel('/home/<USER>/Desktop/unique_df.xlsx', index=False)
            # for index, row in unique_df.iterrows():
            #     document_data = {
            #         'document_id': int(row['document_id']),
            #         'package_step_id': int(row['package_step_id']),
            #     }

            #     # Add the row to the package_step_document_master table
            #     BaseService().create(db, PackageStepDocumentDB, **document_data)

            unique_df.to_sql(
                "package_step_document_master",
                con=engine,
                if_exists="append",
                index=False,
            )
        # Insert the filtered dataframe into the package_step_document_master table
        if len(filtered_df) == 0:
            filtered_df.to_sql(
                "package_step_document_master",
                con=engine,
                if_exists="append",
                index=False,
            )

        return True
    except Exception as e:
        document_logger.error("An error occurred: %s", str(e))
        return False


class DocumentHelperService:
    """Service class for dpcument-related operations."""

    def update_document_schemes(self, db: Session, document, scheme_ids: list):
        """Update schemes for a document."""
        from app.master.services.scheme_type import SchemeTypeService

        return SchemeTypeService().update_associated_schemes(
            db, document, scheme_ids, "schemes"
        )
