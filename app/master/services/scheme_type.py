from typing import List
from sqlalchemy.orm import Session
from sqlalchemy import not_


from app.database.database import Session<PERSON>ocal
from app.master.exception.errors.scheme_type import SchemesErrors
from app.master.models.scheme_type import SchemeType

from app.master.exception.custom.schema_type import SchemeTypeExceptions

from app.master.services.scheme_document import SchemeDocumentService

from utils.db import BaseService
from utils.logger import setup_logger


scheme_type_logger = setup_logger("scheme_type.log")


class SchemeTypeService(BaseService):
    """
    Service class for managing scheme type-related operations.
    """

    def __init__(self) -> None:
        self.db = SessionLocal()

    def get_scheme_type_by_attribute(
        self, value: str, db: Session, attribute: str = None
    ):
        """Retrieve a step type master by a specific attribute."""
        try:
            if not attribute:
                attribute = SchemeType.id
            step_type = self.get_by_attribute(db, SchemeType, attribute, value)
            if not step_type:
                error = SchemeTypeExceptions.generate_scheme_type_not_found_error(
                    scheme_type_id=value
                )
                scheme_type_logger.warning("Scheme type not found with %s=%s", 
                                         attribute.key if hasattr(attribute, 'key') else attribute, value)
                return False, error
            scheme_type_logger.debug("Retrieved scheme type with %s=%s", 
                                   attribute.key if hasattr(attribute, 'key') else attribute, value)
            return True, step_type
        except Exception as e:
            scheme_type_logger.error("Error retrieving scheme type by attribute: %s", str(e))
            raise

    def get_scheme_type_by_id(self, scheme_type_id: int, db: Session):
        """Retrieve a scheme type by ID."""
        try:
            is_exist, scheme_type = self.get_scheme_type_by_attribute(
                value=scheme_type_id, db=db
            )
            return is_exist, scheme_type
        except Exception as e:
            scheme_type_logger.error("Error retrieving scheme type by id=%s: %s", scheme_type_id, str(e))
            raise

    def get_all_scheme_types(self, db: Session):
        """Retrieve all scheme types."""
        try:
            scheme_types = self.get_all(db, SchemeType)
            scheme_type_logger.debug("Retrieved %d scheme types", len(scheme_types))
            return scheme_types
        except Exception as e:
            scheme_type_logger.error("Error retrieving all scheme types: %s", str(e))
            raise

    def create_scheme_type(self, scheme_type_data, db: Session):
        """Create a new scheme type."""
        try:
            scheme_type = self.create(db, SchemeType, **scheme_type_data.dict())
            scheme_type_logger.info("Created new scheme type with id=%s and type=%s", 
                                  scheme_type.id, scheme_type.type)
            return scheme_type
        except Exception as e:
            scheme_type_logger.error("Error creating new scheme type: %s", str(e))
            raise

    def update_scheme_type(
        self, scheme_type_id: int, scheme_type_data: dict, db: Session
    ):
        """Update a scheme type."""
        try:
            is_exist, scheme_type = self.get_scheme_type_by_id(scheme_type_id, db=db)
            if not is_exist:
                error = SchemeTypeExceptions.generate_scheme_type_not_found_error(
                    scheme_type_id=scheme_type_id
                )
                scheme_type_logger.warning("Cannot update: scheme type not found with id=%s", scheme_type_id)
                return False, error
            else:
                scheme_type_data_dict = scheme_type_data.dict()
                scheme_type = self.update(
                    db, SchemeType, scheme_type_id, **scheme_type_data_dict
                )
                scheme_type_logger.info("Updated scheme type with id=%s", scheme_type_id)
                return True, scheme_type
        except Exception as e:
            scheme_type_logger.error("Error updating scheme type with id=%s: %s", scheme_type_id, str(e))
            raise

    def delete_scheme_type(
        self, scheme_type_id: int, db: Session, force_delete: bool = False
    ):
        """Delete a scheme type."""
        try:
            is_exist, scheme_type = self.get_scheme_type_by_id(scheme_type_id, db=db)
            if not is_exist:
                error = SchemeTypeExceptions.generate_scheme_type_not_found_error(
                    scheme_type_id=scheme_type_id
                )
                scheme_type_logger.warning("Cannot delete: scheme type not found with id=%s", scheme_type_id)
                return False, error
            else:
                if not force_delete and self.has_documents(scheme_type_id):
                    scheme_type_logger.warning("Cannot delete: scheme type id=%s has associated documents", scheme_type_id)
                    SchemesErrors().raise_scheme_has_documents_exception(
                        scheme_type_id=scheme_type_id
                    )
                is_deleted = self.delete(db, SchemeType, scheme_type_id)
                scheme_type_logger.info("Deleted scheme type with id=%s", scheme_type_id)
                return is_deleted, scheme_type_id
        except Exception as e:
            scheme_type_logger.error("Error deleting scheme type with id=%s: %s", scheme_type_id, str(e))
            raise

    def is_scheme_type_exists(self, scheme_type: str):
        """Check if a scheme type exists."""
        try:
            is_exist = self.is_scheme_exists_by_type(scheme_type, self.db)
            scheme_type_logger.debug("Scheme type '%s' exists: %s", scheme_type, is_exist)
            return is_exist
        except Exception as e:
            scheme_type_logger.error("Error checking if scheme type '%s' exists: %s", scheme_type, str(e))
            raise

    def is_scheme_exists_by_type(self, scheme_type: str, db: Session):
        """Check if a scheme type name exists."""
        try:
            is_exist, _ = self.get_scheme_type_by_attribute(
                value=scheme_type, db=db, attribute=SchemeType.type
            )
            return is_exist
        except Exception as e:
            scheme_type_logger.error("Error checking if scheme type '%s' exists by type: %s", scheme_type, str(e))
            raise
        finally:
            db.close()

    def is_scheme_exists_by_id(self, scheme_id: int):
        """Check if a scheme type ID exists."""
        try:
            is_exist, _ = self.get_scheme_type_by_attribute(
                value=scheme_id, db=self.db, attribute=SchemeType.id
            )
            return is_exist
        except Exception as e:
            scheme_type_logger.error("Error checking if scheme exists by id=%s: %s", scheme_id, str(e))
            raise
        finally:
            self.db.close()

    def check_scheme_existence(self, scheme_ids: List[int]) -> List[int]:
        """Check if schemes exist for the given list of scheme IDs."""
        exist_ids = (
            self.db.query(SchemeType.id).filter(SchemeType.id.in_(scheme_ids)).all()
        )
        exist_ids = [id[0] for id in exist_ids]  # Unpack IDs from tuples
        not_exist_ids = list(set(scheme_ids) - set(exist_ids))
        scheme_type_logger.debug("Checked existence of %d scheme ids, %d do not exist", 
                               len(scheme_ids), len(not_exist_ids))
        return not_exist_ids

    def has_documents(self, scheme_id: int) -> bool:
        try:
            (
                is_exist,
                scheme_type,
            ) = SchemeDocumentService().get_scheme_documents_by_scheme_type_id(
                scheme_type_id=scheme_id, db=self.db
            )
            scheme_type_logger.debug("Scheme id=%s has documents: %s", scheme_id, is_exist)
            return is_exist
        except Exception as e:
            scheme_type_logger.error("Error checking if scheme id=%s has documents: %s", scheme_id, str(e))
            raise

    def update_associated_schemes(self, db: Session, entity, scheme_ids: list, attribute: str):
        """Update schemes for an entity."""
        try:
            # Fetch the relationship dynamically using the attribute name
            current_schemes = getattr(entity, attribute)

            if not scheme_ids:  # If the list is empty, clear the relationship
                current_schemes.clear()
                scheme_type_logger.info("Cleared all schemes for entity %s", entity.__class__.__name__)
            else:
                # Fetch the new schemes from the database
                new_schemes = db.query(SchemeType).filter(SchemeType.id.in_(scheme_ids)).all()
                setattr(entity, attribute, new_schemes)  # Assign new schemes
                scheme_type_logger.info("Updated schemes for entity %s with %d schemes", 
                                      entity.__class__.__name__, len(new_schemes))

            # Commit the changes and refresh the entity
            db.commit()
            db.refresh(entity)
            return entity
        except Exception as e:
            scheme_type_logger.error("Error updating associated schemes for entity %s: %s", 
                                   entity.__class__.__name__, str(e))
            raise