from sqlalchemy.orm import Session

from app.database.database import engine, Base, SessionLocal
from app.master.models.package import StepMaster
from app.master.exception.custom.step_master import StepMasterExceptions

from utils.db import BaseService
from utils.logger import setup_logger

step_logger = setup_logger("step_master.log")


class StepMasterService(BaseService):
    """
    Service class for managing step master-related operations.
    """

    def __init__(self) -> None:
        self.db = SessionLocal()

    def get_step_by_attribute(self, value: str, db: Session, attribute: str = None):
        """Retrieve a step master by a specific attribute."""
        try:
            if not attribute:
                attribute = StepMaster.id
            # Query the step master from the database
            step = self.get_by_attribute(db, StepMaster, attribute, value)
            if not step:
                error = StepMasterExceptions.generate_step_master_not_found_error(
                    step_id=value
                )
                return False, error

            return True, step
        except Exception as e:
            step_logger.error("Error retrieving step by attribute: %s", str(e))
            raise

    def get_step_by_id(self, step_id: int, db: Session):
        """Retrieve a step master by ID."""

        try:
            is_exist, step = self.get_step_by_attribute(value=step_id, db=db)
            return is_exist, step
        except Exception as e:
            step_logger.error("Error retrieving step by ID %s: %s", step_id, str(e))
            raise

    def get_all_steps(self, db: Session):
        """Retrieve all step masters."""

        try:
            # Query all step masters from the database.
            steps = self.get_all(db, StepMaster)
            return steps
        except Exception as e:
            step_logger.error("Error retrieving all steps: %s", str(e))
            raise

    def create_step(self, step_data, db: Session):
        """Create a new step master."""
        try:
            # Create a new step master instance and add it to the database
            step = self.create(db, StepMaster, **step_data.dict())
            step_logger.info("Created new step with ID %s", step.id)
            return step
        except Exception as e:
            step_logger.error("Error creating step: %s", str(e))
            raise

    def update_step(self, step_id: int, step_data: dict, db: Session):
        """Update a step master."""

        try:
            is_exist, step = self.get_step_by_id(step_id, db=db)
            if not is_exist:
                error = StepMasterExceptions.generate_step_master_not_found_error(
                    step_id=step_id
                )
                step_logger.warning("Step with ID %s not found for update", step_id)
                return False, error
            else:
                step_data_dict = step_data.dict()
                step = self.update(db, StepMaster, step_id, **step_data_dict)
                step_logger.info("Updated step with ID %s", step_id)
                return True, step

        except Exception as e:
            step_logger.error("Error updating step with ID %s: %s", step_id, str(e))
            raise

    def delete_step(self, step_id: int, db: Session):
        """Delete a step master."""

        try:
            is_exist, step = self.get_step_by_id(step_id, db=db)
            if not is_exist:
                error = StepMasterExceptions.generate_step_master_not_found_error(
                    step_id=step_id
                )
                step_logger.warning("Step with ID %s not found for deletion", step_id)
                return False, error
            else:
                is_deleted = self.delete(db, StepMaster, step_id)
                step_logger.info("Deleted step with ID %s", step_id)
                return is_deleted, step_id
        except Exception as e:
            step_logger.error("Error deleting step with ID %s: %s", step_id, str(e))
            raise

    def is_step_exists(self, step_type: int):
        """Check if a step master exists."""

        try:
            is_exist, step = self.get_step_by_type(step_type, self.db)
            return is_exist
        except Exception as e:
            step_logger.error("Error checking if step type %s exists: %s", step_type, str(e))
            raise

    def is_step_exists_by_order(self, order: int):
        """Check if a step master exists by order."""

        try:
            is_exist, _ = self.get_step_by_order(order, self.db)
            return is_exist
        except Exception as e:
            step_logger.error("Error checking if step with order %s exists: %s", order, str(e))
            raise

    def get_step_by_type(self, step_type: str, db: Session):
        """Retrieve a step master by type."""
        try:
            is_exist, step = self.get_step_by_attribute(
                value=step_type, db=db, attribute=StepMaster.type
            )
            if not is_exist:
                error = StepMasterExceptions.generate_step_type_not_valid_error(
                    step_type=step_type
                )
                step_logger.warning("Step type %s not valid", step_type)
                return False, error

            return True, step
        except Exception as e:
            step_logger.error("Error retrieving step by type %s: %s", step_type, str(e))
            raise

    def get_step_by_order(self, order: str, db: Session):
        """Retrieve a step master by type."""
        try:
            is_exist, step = self.get_step_by_attribute(
                value=order, db=db, attribute=StepMaster.order
            )
            if not is_exist:
                step_logger.debug("No step found with order %s", order)
                return False, None

            return True, step
        except Exception as e:
            step_logger.error("Error retrieving step by order %s: %s", order, str(e))
            raise

    def is_step_exists_by_id(self, step_id: int):
        """Check if a step master exists."""

        try:
            is_exist, step = self.get_step_by_attribute(step_id, self.db)
            return is_exist
        except Exception as e:
            step_logger.error("Error checking if step with ID %s exists: %s", step_id, str(e))
            raise
