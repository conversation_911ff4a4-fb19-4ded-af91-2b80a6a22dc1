from typing import List
from sqlalchemy.orm import Session, joinedload

from app.database.database import engine, Base, SessionLocal
from app.master.exception.errors.scheme_document import SchemeDocumentErrors

from app.master.models.scheme_document import SchemeDocumentDB


from app.master.models.document_master import DocumentMaster


from utils.db import BaseService
from utils.mapper import AttributeMapper
from utils.logger import setup_logger


# Base.metadata.create_all(bind=engine)

scheme_document_logger = setup_logger("scheme_document.log")


class SchemeDocumentService(BaseService):
    def __init__(self) -> None:
        self.db = SessionLocal()

    def get_scheme_document_by_attribute(
        self,
        value: str,
        db: Session,
        attribute: str = None,
        raise_exception: bool = False,
    ):
        """Retrieve a scheme document by a specific attribute."""
        try:
            if not attribute:
                attribute = SchemeDocumentDB.id
            # Query the scheme document from the database
            scheme_document = self.get_by_attribute(
                db, SchemeDocumentDB, attribute, value
            )
            if not scheme_document:
                if raise_exception:
                    scheme_document_logger.warning("Scheme document not found with %s=%s, raising exception", 
                                                attribute.key if hasattr(attribute, 'key') else attribute, value)
                    SchemeDocumentErrors().raise_scheme_document_not_found_exception(
                        scheme_document_id=value
                    )
                scheme_document_logger.debug("No scheme document found with %s=%s", 
                                          attribute.key if hasattr(attribute, 'key') else attribute, value)
                return False, None
            scheme_document_logger.debug("Retrieved scheme document with %s=%s", 
                                      attribute.key if hasattr(attribute, 'key') else attribute, value)
            return True, scheme_document
        except Exception as e:
            scheme_document_logger.error("Error retrieving scheme document by attribute: %s", str(e))
            raise

    def get_scheme_documents_by_scheme_id(self, scheme_id: int, db: Session):
        """Retrieve all scheme documents associated with a scheme type."""
        try:
            # Query the database for scheme documents
            scheme_documents = (
                db.query(SchemeDocumentDB)
                .filter(SchemeDocumentDB.scheme_id == scheme_id)
                .all()
            )
            if not scheme_documents:
                scheme_document_logger.warning("No scheme documents found for scheme_id=%s", scheme_id)
                SchemeDocumentErrors().raise_scheme_document_not_found_with_scheme_id_exception(
                    scheme_id=scheme_id
                )
            scheme_document_logger.debug("Found %d scheme documents for scheme_id=%s", 
                                      len(scheme_documents), scheme_id)
            return scheme_documents
        except Exception as e:
            scheme_document_logger.error("Error retrieving scheme documents by scheme_id=%s: %s", 
                                      scheme_id, str(e))
            raise

    def assign_scheme_to_document(self, scheme_id: int, document_id: int, db: Session):
        """Assign a document to a scheme type."""
        try:
            payload = AttributeMapper(
                scheme_id=scheme_id, document_id=document_id
            ).__dict__

            new_scheme_document = self.create(db, SchemeDocumentDB, **payload)
            scheme_document_logger.info("Assigned document_id=%s to scheme_id=%s", document_id, scheme_id)
            return new_scheme_document
        except Exception as e:
            scheme_document_logger.error("Error assigning document_id=%s to scheme_id=%s: %s", 
                                      document_id, scheme_id, str(e))
            raise

    def assign_document_to_schemes(self, document_id: int, scheme_ids: List[int]):
        """Assign a document to multiple scheme types."""
        try:
            new_scheme_documents = [
                SchemeDocumentDB(scheme_id=scheme_id, document_id=document_id)
                for scheme_id in scheme_ids
            ]
            self.db.bulk_save_objects(new_scheme_documents)
            self.db.commit()
            self.db.close()
            scheme_document_logger.info("Assigned document_id=%s to %d schemes", document_id, len(scheme_ids))
            return new_scheme_documents
        except Exception as e:
            scheme_document_logger.error("Error assigning document_id=%s to schemes: %s", document_id, str(e))
            raise

    def assign_documents_to_scheme_type(self, scheme_id: int, document_ids: list):
        """
        Assign documents to a scheme type.

        Args:
            scheme_id (int): The ID of the scheme type.
            document_ids (List[int]): The IDs of the documents to assign.

        Returns:
            Tuple[bool, Union[List[SchemeDocumentDB], Tuple[bool, str]]]: A tuple containing a boolean indicating success or failure,
            and either the list of assigned scheme documents objects or an error message.
        """
        try:

            # Fetch all documents at once
            documents = (
                self.db.query(DocumentMaster)
                .filter(DocumentMaster.id.in_(document_ids))
                .all()
            )
            existing_document_ids = {document.id for document in documents}
            # Fetch all existing scheme-document relationships for the given scheme_id at once
            scheme_documents = (
                self.db.query(SchemeDocumentDB)
                .filter(
                    SchemeDocumentDB.scheme_id == scheme_id,
                    SchemeDocumentDB.document_id.in_(document_ids),
                )
                .all()
            )
            existing_scheme_document_ids = {
                scheme_document.document_id for scheme_document in scheme_documents
            }
            # Determine which document_ids are not found and which are new
            new_document_ids = existing_document_ids - existing_scheme_document_ids
            # Use a bulk insert operation to add all new scheme-document relationships at once
            new_scheme_documents = [
                SchemeDocumentDB(scheme_id=scheme_id, document_id=document_id)
                for document_id in new_document_ids
            ]
            self.db.bulk_save_objects(new_scheme_documents)
            self.db.commit()
            self.db.close()
            scheme_document_logger.info("Assigned %d documents to scheme_id=%s", len(new_scheme_documents), scheme_id)
            return new_scheme_documents
        except Exception as e:
            scheme_document_logger.error("Error assigning documents to scheme_id=%s: %s", scheme_id, str(e))
            raise

    def get_scheme_documents(self, scheme_id):
        """Retrieve all documents associated with a scheme type."""
        scheme_documents = self.get_scheme_documents_by_scheme_id(
            scheme_id=scheme_id, db=self.db
        )

        # Query the database for scheme documents
        scheme_documents = (
            self.db.query(SchemeDocumentDB)
            .filter(SchemeDocumentDB.scheme_id == scheme_id)
            .all()
        )
        # Return the scheme documents
        scheme_document_logger.debug("Found %d scheme documents for scheme_id=%s", 
                                  len(scheme_documents), scheme_id)
        return scheme_documents

    def get_scheme_documents_by_scheme_type_and_document_ids(
        self, scheme_id: int, document_ids: list
    ):
        """
        Retrieve scheme documents by scheme type ID and document IDs.

        Args:
            scheme_id (int): The ID of the scheme type.
            document_ids (List[int]): The IDs of the documents.

        Returns:
            List[SchemeDocumentDB]: A list of scheme document objects.
        """
        try:
            # Query the database for scheme documents
            scheme_documents = (
                self.db.query(SchemeDocumentDB)
                .filter(
                    SchemeDocumentDB.scheme_id == scheme_id,
                    SchemeDocumentDB.document_id.in_(document_ids),
                )
                .all()
            )
            scheme_document_logger.debug("Found %d scheme documents for scheme_id=%s and document_ids=%s", 
                                      len(scheme_documents), scheme_id, document_ids)
            return scheme_documents
        except Exception as e:
            scheme_document_logger.error("Error retrieving scheme documents by scheme_id=%s and document_ids: %s", 
                                      scheme_id, str(e))
            raise

    def remove_documents_from_scheme_type(self, scheme_id: int, document_ids: list):
        """
        Remove assigned documents from a scheme type.

        Args:
            scheme_id (int): The ID of the scheme type.
            document_ids (List[int]): The IDs of the documents to remove.

        Returns:
            Tuple[bool, str]: A tuple containing a boolean indicating success or failure,
            and a message indicating the result of the operation.
        """
        try:
            self.get_scheme_documents_by_scheme_id(scheme_id=scheme_id, db=self.db)

            # Remove the scheme-document relationships
            self.db.query(SchemeDocumentDB).filter(
                SchemeDocumentDB.scheme_id == scheme_id,
                SchemeDocumentDB.document_id.in_(document_ids),
            ).delete(synchronize_session=False)
            self.db.commit()
            self.db.close()
            scheme_document_logger.info("Removed %d documents from scheme_id=%s", len(document_ids), scheme_id)
            return scheme_id
        except Exception as e:
            scheme_document_logger.error("Error removing documents from scheme_id=%s: %s", scheme_id, str(e))
            raise

    def is_scheme_document_exists_by_scheme_id(self, scheme_id: int) -> bool:
        """Check if a scheme document exists by scheme_id."""
        try:
            is_exist, scheme_document = self.get_scheme_document_by_attribute(
                value=scheme_id, db=self.db, attribute=SchemeDocumentDB.scheme_id
            )
            scheme_document_logger.debug("Scheme document exists for scheme_id=%s: %s", scheme_id, is_exist)
            return is_exist
        except Exception as e:
            scheme_document_logger.error("Error checking if scheme document exists for scheme_id=%s: %s", 
                                      scheme_id, str(e))
            raise
        finally:
            self.db.close()

    def get_first_scheme_document(self, scheme_document_payload):
        """Retrieve the first scheme document."""
        return self.get_first_record(
            db=self.db, model=SchemeDocumentDB, **scheme_document_payload
        )

    def is_scheme_document_unique(self, scheme_document_payload) -> bool:
        """Check if a scheme document is unique."""
        is_scheme_document_exists = self.get_first_scheme_document(
            scheme_document_payload
        )
        scheme_document_logger.debug("Scheme document is unique: %s", bool(is_scheme_document_exists))
        return bool(is_scheme_document_exists)

    def is_document_assigned_to_schemes(self, scheme_ids, document_id: int):
        """Check if a document is assigned to any scheme type."""
        try:
            # Query the database for scheme documents
            scheme_documents = (
                self.db.query(SchemeDocumentDB)
                .filter(
                    SchemeDocumentDB.scheme_id.in_(scheme_ids),
                    SchemeDocumentDB.document_id == document_id,
                )
                .all()
            )
            is_assigned = bool(scheme_documents)
            scheme_document_logger.debug("Document_id=%s is%s assigned to schemes %s", 
                                      document_id, "" if is_assigned else " not", scheme_ids)
            return is_assigned
        except Exception as e:
            scheme_document_logger.error("Error checking if document_id=%s is assigned to schemes: %s", 
                                      document_id, str(e))
            raise
