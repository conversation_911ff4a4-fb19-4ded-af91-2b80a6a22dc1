from typing import Any, Dict, List, Optional

from app.cases.services.case import CaseService
from app.database.database import SessionLocal
from app.master.exception.errors.hospital import HospitalErrors
from app.master.models.hospital import HospitalDB
from app.master.models.scheme_hospital import SchemeHospitalDB
from app.master.services.scheme_hospital import SchemeHospitalService
from app.models.user import UserHospitalDB
from pandas import DataFrame
from sqlalchemy import and_, select
from sqlalchemy.orm import Session
from utils.db import BaseService, QueryWithCount
from utils.logger import setup_logger
from utils.mapper import AttributeMapper
from utils.pagination import PaginationHelper
from utils.panda import PandaUtils

# Base.metadata.create_all(bind=engine)

hospital_logger = setup_logger("hospital.log")


class HospitalService(BaseService):
    """
    Service class for managing hospital-related operations.
    """

    def __init__(self) -> None:
        self.db = SessionLocal()

    def get_or_raise_hospital_by_attribute(
        self, value: str, db: Session, attribute: str = None
    ):
        """Retrieve a hospital by a specific attribute."""
        try:
            if not attribute:
                attribute = HospitalDB.id
            # Query the hospital from the database
            hospital = self.get_by_attribute(db, HospitalDB, attribute, value)
            if not hospital:
                HospitalErrors().raise_hospital_not_exists_exception(hospital_id=value)
                return
            return True, hospital
        except Exception as e:
            hospital_logger.error(str(e))
            raise

    def get_hospital_by_attribute(self, value: str, db: Session, attribute: str = None):
        """Retrieve a hospital by a specific attribute."""
        try:
            if not attribute:
                attribute = HospitalDB.id
            # Query the hospital from the database
            hospital = self.get_by_attribute(db, HospitalDB, attribute, value)
            if not hospital:
                return False, None
            return True, hospital
        except Exception as e:
            hospital_logger.error(str(e))
            raise

    def get_hospital_by_id(self, hospital_id: int, db: Session):
        """Retrieve a hospital by ID."""
        try:
            is_exist, hospital = self.get_hospital_by_attribute(
                value=hospital_id, db=db
            )
            return is_exist, hospital
        except Exception as e:
            hospital_logger.error(str(e))
            raise

    def get_hospital_or_raise(self, hospital_id: int, db: Session):
        hospital = self.get_by_attribute(db, HospitalDB, HospitalDB.id, hospital_id)
        if not hospital:
            HospitalErrors().raise_hospital_not_exists_exception(
                hospital_id=hospital_id
            )
        return hospital

    def get_all_hospitals(self, db: Session, **kwargs) -> List[HospitalDB]:
        """Retrieve all hospitals."""
        try:
            user_id = kwargs.pop("user", None)
            scheme = kwargs.pop("scheme", None)

            # Initialize the query
            query = self.init_query(db=db, model=HospitalDB)

            # Join the user_hospital table if filtering by user
            if user_id:
                query = query.join(
                    UserHospitalDB, HospitalDB.id == UserHospitalDB.hospital_id
                )
                query = query.filter(UserHospitalDB.user_id == user_id)

            # Join the scheme_hospital_master table if filtering by scheme
            if scheme:
                query = query.join(
                    SchemeHospitalDB, HospitalDB.id == SchemeHospitalDB.hospital_id
                )
                query = query.filter(SchemeHospitalDB.scheme_id == scheme)

            # Execute the query and retrieve results
            hospitals = query.filter(HospitalDB.is_deleted == False).all()

            return hospitals

        except Exception as e:
            hospital_logger.error(str(e))
            raise

    def filter_hospitals(
        self,
        name: Optional[str],
        scheme: Optional[int],
        order_by: Optional[str],
        db: Session,
        page: int,
        limit: int,
    ):
        """Filter hospitals by name and city."""
        try:
            from app.master.models.scheme_type import SchemeType

            query = self.init_query(db=db, model=HospitalDB)

            if name is not None:
                query = self.filter_value_like(db, HospitalDB, HospitalDB.name, name)
            if scheme is not None:
                query = (
                    query.join(HospitalDB.schemes)
                    .filter(SchemeType.type.like(f"%{scheme}%"))
                    .distinct()
                )
            query = self.order_query(query, HospitalDB, order_by)
            query = query.filter(HospitalDB.is_deleted == False)
            total_count = query.count()
            query = PaginationHelper.paginate_query(query, page, limit)
            return query.all(), total_count
        except Exception as e:
            hospital_logger.error(str(e))
            raise

    async def get_first_hospital(self, hospital_data: dict):
        """Retrieve the first hospital."""
        stmt = (
            select(HospitalDB)
            .filter(
                and_(
                    *[
                        getattr(HospitalDB, key) == value
                        for key, value in hospital_data.items()
                    ]
                )
            )
            .limit(1)
        )
        result = await self.db.execute(stmt)
        return result.scalars().first()

    def create_hospital(self, db: Session, hospital_data: dict) -> HospitalDB:
        """Create a new hospital."""
        try:
            with db.begin():
                hospital_payload = hospital_data.dict()
                scheme_ids = hospital_payload.pop("schemes", [])
                is_hospital_exists = self.is_hospital_data_unique(hospital_payload)
                if is_hospital_exists:
                    HospitalErrors().raise_hospital_already_exists_exception(
                        hospital=hospital_data.dict()
                    )

                new_hospital = HospitalDB(**hospital_payload)
                db.add(new_hospital)
                db.flush()  # Flush to persist the new hospital and get its ID

                # Directly use SchemeHospitalDB to assign schemes to the hospital
                if scheme_ids:
                    new_scheme_hospitals = [
                        SchemeHospitalDB(
                            scheme_id=scheme_id, hospital_id=new_hospital.id
                        )
                        for scheme_id in scheme_ids
                    ]

                    db.bulk_save_objects(new_scheme_hospitals)
                db.refresh(
                    new_hospital
                )  # Refresh to get the updated state of the new hospital
                return new_hospital
        except Exception as e:

            hospital_logger.error(str(e))
            raise

    def update_hospital(
        self, hospital_id: int, hospital_data: dict, db: Session
    ) -> HospitalDB:
        """Update a hospital."""
        try:
            with db.begin():
                hospital = self.get_hospital_or_raise(hospital_id=hospital_id, db=db)

                hospital_payload = hospital_data.dict()
                name = hospital_payload.get("name")
                short_name = hospital_payload.get("short_name")

                if name and self.check_unique_field(
                    db, HospitalDB, "name", name, hospital_id
                ):
                    HospitalErrors().raise_hospital_name_already_exists_exception(name)
                if short_name and self.check_unique_field(
                    db, HospitalDB, "short_name", short_name, hospital_id
                ):
                    HospitalErrors().raise_hospital_short_name_already_exists_exception(
                        short_name
                    )

                scheme_ids = hospital_payload.pop("schemes", [])
                removing_schemes = [
                    scheme.id
                    for scheme in hospital.schemes
                    if scheme.id not in scheme_ids
                ]

                if removing_schemes:
                    has_cases = (
                        CaseService().check_case_exist_with_hospital_and_schemes(
                            db, hospital_id, removing_schemes
                        )
                    )
                    if has_cases:
                        HospitalErrors().raise_hospital_and_scheme_associated_with_cases_exception(
                            schemes=removing_schemes
                        )

                for key, value in hospital_payload.items():
                    setattr(hospital, key, value)

                db.add(hospital)
                db.flush()

                # Update hospital schemes
                self.update_hospital_schemes(db, hospital_id, scheme_ids)
                db.refresh(hospital)
                return hospital
        except Exception as e:
            hospital_logger.error(str(e))
            raise

    def update_hospital_schemes(
        self, db: Session, hospital_id: int, scheme_ids: list[int]
    ):
        """Update the hospital's schemes."""
        try:
            # Fetch existing scheme IDs associated with the hospital
            existing_schemes = (
                db.query(SchemeHospitalDB.scheme_id)
                .filter(SchemeHospitalDB.hospital_id == hospital_id)
                .all()
            )
            existing_scheme_ids = {scheme.scheme_id for scheme in existing_schemes}

            # Determine schemes to be removed and added
            scheme_ids_set = set(scheme_ids)
            schemes_to_remove = existing_scheme_ids - scheme_ids_set
            schemes_to_add = scheme_ids_set - existing_scheme_ids

            # Remove mappings that are not included in the updated scheme IDs
            if schemes_to_remove:
                db.query(SchemeHospitalDB).filter(
                    and_(
                        SchemeHospitalDB.hospital_id == hospital_id,
                        SchemeHospitalDB.scheme_id.in_(schemes_to_remove),
                    )
                ).delete(synchronize_session=False)

            # Add new mappings for schemes that are not already associated with the hospital
            new_schemes = [
                SchemeHospitalDB(hospital_id=hospital_id, scheme_id=scheme_id)
                for scheme_id in schemes_to_add
            ]
            if new_schemes:
                db.bulk_save_objects(new_schemes)

        except Exception as e:
            hospital_logger.error(str(e))

    def delete_hospital(
        self, hospital_id: int, db: Session, force_delete: bool = False
    ) -> int:
        """Delete a hospital."""
        try:
            hospital = self.get_hospital_or_raise(hospital_id=hospital_id, db=db)
            if hospital.users:
                HospitalErrors().raise_hospital_has_users_exception(
                    hospital_id=hospital_id
                )
            elif hospital.schemes:
                HospitalErrors().raise_hospital_has_schemes_exception(
                    hospital_id=hospital_id
                )
            elif hospital.cases:
                HospitalErrors().raise_hospital_has_cases_exception(
                    hospital_id=hospital_id
                )
            if force_delete:
                return self.delete(db, HospitalDB, hospital_id)
            else:
                self.update(db, HospitalDB, hospital_id, is_deleted=True)
                return True
        except Exception as e:
            hospital_logger.error(str(e))
            raise

    def is_hospital_exists_by_name(self, name: str) -> bool:
        """Check if a hospital name exists."""
        try:
            is_exist, _ = self.get_hospital_by_attribute(
                value=name, db=self.db, attribute=HospitalDB.name
            )
            return is_exist
        except Exception as e:
            hospital_logger.error(str(e))
            raise

    def is_hospital_exists_by_id(self, hospital_id: int) -> bool:
        """Check if a hospital ID exists."""
        try:
            is_exist, _ = self.get_hospital_by_id(hospital_id, self.db)
            return is_exist
        except Exception as e:
            hospital_logger.error(str(e))
            raise


    def are_hospitals_exist(self, hospital_ids):
        try:
            query = self.db.query(HospitalDB.id).filter(HospitalDB.id.in_(hospital_ids))
            existing_ids = {id_[0] for id_ in query.all()}
            not_exist_ids = list(set(hospital_ids) - existing_ids)
            if not_exist_ids:
                return False, not_exist_ids
            return True, None
        except Exception as e:
            hospital_logger.error(str(e))
            raise

    def is_hospital_exists_by_name(self, name: str) -> bool:
        """Check if a hospital name exists."""
        try:
            is_exist, _ = self.get_hospital_by_attribute(
                value=name, db=self.db, attribute=HospitalDB.name
            )
            return is_exist
        except Exception as e:
            hospital_logger.error(str(e))
            raise

    def is_hospital_exists_by_short_name(self, short_name: str) -> bool:
        """Check if a hospital short_name exists."""
        try:
            is_exist, _ = self.get_hospital_by_attribute(
                value=short_name, db=self.db, attribute=HospitalDB.short_name
            )
            return is_exist
        except Exception as e:
            hospital_logger.error(str(e))
            raise

    def get_hospital_by_code(self, code: str, db: Session):
        """Retrieve a hospital by code."""
        try:
            is_exist, hospital = self.get_hospital_by_attribute(
                value=code, db=db, attribute=HospitalDB.code
            )
            return hospital
        except Exception as e:
            hospital_logger.error(str(e))
            raise

    def get_hospital_by_name(self, name: str, db: Session):
        """Retrieve a hospital by name."""
        try:
            is_exist, hospital = self.get_hospital_by_attribute(
                value=name, db=db, attribute=HospitalDB.name
            )
            return hospital
        except Exception as e:
            hospital_logger.error(str(e))
            raise

    def get_first_hospital(self, hospital_data: dict):
        """Retrieve the first hospital."""
        return self.get_first_record(db=self.db, model=HospitalDB, **hospital_data)

    def is_hospital_data_unique(self, hospital_data) -> bool:
        """Check if a hospital is unique."""
        is_hospital_exists = self.get_first_hospital(hospital_data)
        return bool(is_hospital_exists)

    def bulk_insert_hospitals(self, contents: Any) -> List[Dict[str, Any]]:
        """Bulk insert the hospitals."""
        return HospitalsInserter(self.db).bulk_insert_hospitals(contents)


class HospitalsInserter:
    """Class to bulk insert the hospitals."""

    def __init__(self, db: Session):
        self.db = db

    def bulk_insert_hospitals(self, contents: Any) -> List[Dict[str, Any]]:
        """Bulk insert the hospitals."""
        df = self._convert_to_dataframe(contents)
        hospitals = [self._get_hospitals_mapping(row) for _, row in df.iterrows()]
        self.db.bulk_insert_mappings(HospitalDB, hospitals)
        self.db.commit()
        return hospitals

    @staticmethod
    def _convert_to_dataframe(contents: Any) -> DataFrame:
        """Convert the contents to a DataFrame."""
        return PandaUtils().convert_to_dataframe(contents=contents)

    def _get_hospitals_mapping(self, row: DataFrame) -> Dict[str, Any]:
        """Get the hospitals mapping."""
        name = row["HOSPITAL_NAME"]
        return AttributeMapper(name=name).__dict__

    # def _get_hospitals_mapping(self, row: DataFrame) -> Dict[str, Any]:
    #     """Get the hospitals mapping."""
    #     name = row["HOSPITAL_NAME"]
    #     is_mjp_jay = True if row["MJPJAY_SCHEME"] == "YES" else False
    #     is_ab_pmjay = True if row["ABPMJAY_SCHEME"] == "YES" else False
    #     return AttributeMapper(
    #         name=name, is_ab_pmjay=is_ab_pmjay, is_mjp_jay=is_mjp_jay
    #     ).__dict__


class HospitalHelperService:
    """Service class for hospital-related operations."""

    def update_hospital_schemes(self, db: Session, hospital, scheme_ids: list):
        """Update schemes for a hospital."""
        from app.master.services.scheme_type import SchemeTypeService

        return SchemeTypeService().update_associated_schemes(
            db, hospital, scheme_ids, "schemes"
        )
