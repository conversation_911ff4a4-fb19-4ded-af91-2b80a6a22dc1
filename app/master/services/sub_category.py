from typing import Any, Dict, List, Optional

from app.database.database import <PERSON><PERSON><PERSON><PERSON>
from app.master.exception.custom.sub_category import SubCategoryExceptions
from app.master.exception.errors.sub_category import SubCategoryErrors
from app.master.models.category import CategoryDB
from app.master.models.scheme_type import SchemeType
from app.master.models.sub_category import SubCategoryDB
from app.master.services.package import PackageService
from pandas import DataFrame
from sqlalchemy.orm import Session, joinedload
from utils.db import BaseService, QueryWithCount
from utils.logger import setup_logger
from utils.mapper import AttributeMapper
from utils.pagination import PaginationHelper
from utils.panda import PandaUtils
from sqlalchemy import and_  # Add this import for the and_ function

# Base.metadata.create_all(bind=engine)


sub_category_logger = setup_logger("sub_category.error")


class SubCategoryService(BaseService):
    """
    Service class for managing sub-category-related operations.
    """

    def __init__(self) -> None:
        self.db = SessionLocal()

    def get_subcategory_by_attribute(
        self, value: str, db: Session, attribute: str = None
    ):
        """Retrieve a sub-category by a specific attribute."""
        try:
            if not attribute:
                attribute = SubCategoryDB.id
            # Query the sub-category from the database
            sub_category = self.get_by_attribute(db, SubCategoryDB, attribute, value)
            if not sub_category:
                error = SubCategoryExceptions.generate_sub_category_not_found_error(
                    sub_category_id=value
                )
                return False, error
            return True, sub_category
        except Exception as e:
            sub_category_logger.error(str(e))
            raise

    def get_subcategory_or_raise(self, sub_category_id: int, db: Session):

        sub_category = self.get_by_attribute(
            db, SubCategoryDB, SubCategoryDB.id, sub_category_id
        )
        if not sub_category:
            SubCategoryErrors().raise_sub_category_not_exists_exception(
                sub_category=sub_category_id
            )

        return sub_category

    def get_subcategory_by_id(self, sub_category_id: int, db: Session):
        """Retrieve a sub-category by ID."""
        try:
            is_exist, sub_category = self.get_subcategory_by_attribute(
                value=sub_category_id, db=db
            )
            return is_exist, sub_category
        except Exception as e:
            sub_category_logger.error(str(e))
            raise

    def get_all_subcategories(self, db: Session, **kwargs) -> List[SubCategoryDB]:
        """Retrieve all sub-categories."""
        try:
            query = db.query(SubCategoryDB).options(
                joinedload(SubCategoryDB.category), joinedload(SubCategoryDB.package)
            )

            # Filter out None values from kwargs
            filter_kwargs = {
                key: value for key, value in kwargs.items() if value is not None
            }

            # Apply filters
            if filter_kwargs:
                query = query.filter_by(**filter_kwargs)

            subcategories = query.order_by(SubCategoryDB.name).all()

            return subcategories
        except Exception as e:
            sub_category_logger.error(str(e))

    def filter_subcategories(
        self,
        code: Optional[str],
        name: Optional[str],
        db: Session,
        page: int,
        limit: int,
    ):
        """Filter sub-categories by code and name."""
        query_with_count = QueryWithCount(self.init_query(db=db, model=SubCategoryDB))
        query_with_count.apply_filter(db, SubCategoryDB, SubCategoryDB.code, code)
        query_with_count.apply_filter(db, SubCategoryDB, SubCategoryDB.name, name)
        query_with_count.query = PaginationHelper.paginate_query(
            query_with_count.query, page, limit
        )
        return query_with_count.query.all(), query_with_count.query.count()

    def filter_and_sort_sub_categories(
        self,
        name: Optional[str],
        code: Optional[str],
        category_name: Optional[str],
        scheme: Optional[str],
        order_by: Optional[str],
        db: Session,
        page: int,
        limit: int,
    ):
        """Filter hospitals by name and city."""
        try:

            query = self.init_query(db=db, model=SubCategoryDB)

            if name is not None:
                query = self.filter_value_like(
                    db, SubCategoryDB, SubCategoryDB.name, name
                )
            if code is not None:
                query = query.filter(SubCategoryDB.code.like(f"%{code}%"))
            if category_name is not None:
                query = (
                    query.join(SubCategoryDB.category)
                    .filter(CategoryDB.name.like(f"%{category_name}%"))
                    .distinct()
                )
            if scheme is not None:
                query = (
                    query.join(SubCategoryDB.category)
                    .join(CategoryDB.scheme_type)
                    .filter(SchemeType.type.like(f"%{scheme}%"))
                    .distinct()
                )
            query = self.order_query(query, SubCategoryDB, order_by)
            total_count = query.count()  # Get total count before pagination

            query = PaginationHelper.paginate_query(query, page, limit)
            return query.all(), total_count
        except Exception as e:
            sub_category_logger.error(str(e))
            raise

    def create_subcategory(self, sub_category_data, db: Session) -> SubCategoryDB:
        """Create a new sub-category."""
        try:
            is_sub_category_exists = self.is_sub_category_data_unique(db,sub_category_data)
            sub_category = self.create(db, SubCategoryDB, **sub_category_data.dict())
            return sub_category
        except Exception as e:
            sub_category_logger.error(str(e))
            raise

    def update_subcategory(
        self, sub_category_id: int, sub_category_data: dict, db: Session
    ) -> SubCategoryDB:
        """Update a sub-category."""
        try:
            sub_category = self.get_subcategory_or_raise(
                sub_category_id=sub_category_id, db=db
            )
            is_valid = self.validate_update_data(sub_category_data, sub_category)
            sub_category_data_dict = sub_category_data.dict()
            sub_category = self.update(
                db, SubCategoryDB, sub_category_id, **sub_category_data_dict
            )
            return sub_category
        except Exception as e:
            sub_category_logger.error(str(e))
            raise

    def delete_subcategory(
        self, sub_category_id: int, db: Session, force_delete: bool = False
    ) -> int:
        """Delete a sub-category."""
        try:
            sub_category = self.get_subcategory_or_raise(
                sub_category_id=sub_category_id, db=db
            )
            if not force_delete and self.has_packages(sub_category_id):
                SubCategoryErrors().raise_sub_category_associated_with_package_exception(
                    sub_category_id=sub_category_id
                )
            is_deleted = self.delete(db, SubCategoryDB, sub_category_id)
            return is_deleted, None
        except Exception as e:
            sub_category_logger.error(str(e))
            raise

    def get_first_sub_category(self, sub_category_data: dict):
        """Retrieve the first sub_category."""
        return self.get_first_record(
            db=self.db, model=SubCategoryDB, **sub_category_data
        )

    def is_subcategory_code_unique_within_category(self, db: Session, code: str, category_id: int) -> bool:
        """Check if a subcategory code is unique within its category."""
        try:
            # Use count for reliable results
            code_count = db.query(SubCategoryDB).filter(
                SubCategoryDB.code == code,
                SubCategoryDB.category_id == category_id
            ).count()
            return code_count == 0
        except Exception as e:
            sub_category_logger.error(f"Error checking subcategory code uniqueness: {e}")
            # If there's any error, assume it's not unique to be safe
            return False
    
    def is_subcategory_name_unique_within_category(self, db: Session, name: str, category_id: int) -> bool:
        """Check if a subcategory name is unique within its category."""
        try:
            # Use count for reliable results
            name_count = db.query(SubCategoryDB).filter(
                SubCategoryDB.name == name,
                SubCategoryDB.category_id == category_id
            ).count()
            return name_count == 0
        except Exception as e:
            sub_category_logger.error(f"Error checking subcategory name uniqueness: {e}")
            # If there's any error, assume it's not unique to be safe
            return False
    
    def is_sub_category_data_unique(self, db: Session, sub_category_data) -> bool:
        """Check if a subcategory is unique within its category."""
        try:
            # Get the category_id, code and name from the subcategory data
            category_id = sub_category_data.category_id
            code = sub_category_data.code
            name = sub_category_data.name
            
            # First check both conditions together in one query for efficiency
            existing_count = db.query(SubCategoryDB).filter(
                SubCategoryDB.category_id == category_id,
                (SubCategoryDB.code == code) | (SubCategoryDB.name == name)
            ).count()
            
            if existing_count > 0:
                # If we found potential duplicates, do more specific checks for better error messages
                
                # Check if the subcategory code is unique within its category
                if not self.is_subcategory_code_unique_within_category(db, code, category_id):
                    SubCategoryErrors().raise_sub_category_code_already_exists_exception(code)
                
                # Check if the subcategory name is unique within its category
                if not self.is_subcategory_name_unique_within_category(db, name, category_id):
                    SubCategoryErrors().raise_sub_category_name_already_exists_exception(name)
                
                # If we get here, something is wrong with our logic, so raise a general error
                SubCategoryErrors().raise_sub_category_already_exists_exception(
                    msg=f"Subcategory with code '{code}' or name '{name}' in category {category_id} already exists",
                    sub_category=sub_category_data.dict()
                )
            
            return False  # No duplicates found
        except Exception as e:
            sub_category_logger.error(f"Error in is_sub_category_data_unique: {e}")
            raise

    def is_subcategory_exists_by_name(self, name: str) -> bool:
        """Check if a sub-category name exists."""
        try:
            is_exist, _ = self.get_subcategory_by_attribute(
                value=name, db=self.db, attribute=SubCategoryDB.name
            )
            return is_exist
        except Exception as e:
            sub_category_logger.error(str(e))
            raise
        finally:
            self.db.close()

    def is_subcategory_exists_by_id(self, sub_category_id: int) -> bool:
        """Check if a sub-category ID exists."""
        try:
            is_exist, _ = self.get_subcategory_by_id(sub_category_id, self.db)
            return is_exist
        except Exception as e:
            sub_category_logger.error(str(e))
            raise
        finally:
            self.db.close()

    def is_subcategory_exists_by_category_id(self, category_id: int) -> bool:
        """Check if a sub-category exists by category_id."""
        try:
            is_exist, sub_category = self.get_subcategory_by_attribute(
                value=category_id, db=self.db, attribute=SubCategoryDB.category_id
            )
            return is_exist
        except Exception as e:
            sub_category_logger.error(str(e))
            raise
        finally:
            self.db.close()

    def is_subcategory_exists_by_code(self, code: str) -> bool:
        """Check if a sub-category code exists."""
        try:
            is_exist, _ = self.get_subcategory_by_attribute(
                value=code, db=self.db, attribute=SubCategoryDB.code
            )
            return is_exist
        except Exception as e:
            sub_category_logger.error(str(e))
            raise
        finally:
            self.db.close()

    def has_packages(self, sub_category_id: int) -> bool:
        """Check if a sub-category has packages."""
        return PackageService().is_package_exists_by_subcategory_id(sub_category_id)

    def get_subcategory_by_code(self, code: str, db: Session):
        """Retrieve a sub-category by code."""
        try:
            is_exist, sub_category = self.get_subcategory_by_attribute(
                value=code, db=db, attribute=SubCategoryDB.code
            )
            return is_exist, sub_category
        except Exception as e:
            sub_category_logger.error(str(e))
            raise

    def get_subcategory_by_name(self, name: str, db: Session):
        """Retrieve a sub-category by name."""
        try:
            is_exist, sub_category = self.get_subcategory_by_attribute(
                value=name, db=db, attribute=SubCategoryDB.name
            )
            return is_exist, sub_category
        except Exception as e:
            sub_category_logger.error(str(e))
            raise
    def validate_update_data(self, sub_category_data, sub_category) -> bool:
        """Validate the update data for a subcategory to ensure uniqueness within its category."""
        # Extract fields from subcategory_data
        sub_category_dict = sub_category_data.dict(exclude_unset=True)
        sub_category_id = sub_category.id
        
        # If nothing is being updated, return True immediately
        if not sub_category_dict:
            return True
            
        # Get new values or use existing ones as defaults
        category_id = sub_category_dict.get("category_id", sub_category.category_id)
        code = sub_category_dict.get("code", sub_category.code)
        name = sub_category_dict.get("name", sub_category.name)
        
        # Check for code uniqueness if code is being updated or category is changing
        if "code" in sub_category_dict or ("category_id" in sub_category_dict and category_id != sub_category.category_id):
            code_exists = self.db.query(SubCategoryDB).filter(
                SubCategoryDB.code == code,
                SubCategoryDB.category_id == category_id,
                SubCategoryDB.id != sub_category_id
            ).first()
            
            if code_exists:
                SubCategoryErrors().raise_sub_category_code_already_exists_exception(code)
        
        # Check for name uniqueness if name is being updated or category is changing
        if "name" in sub_category_dict or ("category_id" in sub_category_dict and category_id != sub_category.category_id):
            name_exists = self.db.query(SubCategoryDB).filter(
                SubCategoryDB.name == name,
                SubCategoryDB.category_id == category_id,
                SubCategoryDB.id != sub_category_id
            ).first()
            
            if name_exists:
                SubCategoryErrors().raise_sub_category_name_already_exists_exception(name)
        
        return True

    def bulk_insert_subcategories(self, contents: Any) -> List[Dict[str, Any]]:
        """Bulk insert the sub-categories."""
        return SubCategoriesInserter(self.db).bulk_insert_subcategories(contents)


class SubCategoriesInserter:
    """Class to bulk insert the sub-categories."""

    def __init__(self, db: Session):
        self.db = db

    def bulk_insert_subcategories(self, contents: Any) -> List[Dict[str, Any]]:
        """Bulk insert the sub-categories."""
        df = self._convert_to_dataframe(contents)
        category_ids = df["CATEGORY_CODE"].unique()
        existing_categories = self._get_existing_categories(category_ids)
        category_mapping = {category.code: category for category in existing_categories}
        sub_categories = [
            self._get_sub_categories_mapping(row, category_mapping)
            for _, row in df.iterrows()
            if self._get_sub_categories_mapping(row, category_mapping) is not None
        ]
        self.db.bulk_insert_mappings(SubCategoryDB, sub_categories)
        self.db.commit()
        return sub_categories

    @staticmethod
    def _convert_to_dataframe(contents: Any) -> DataFrame:
        """Convert the contents to a DataFrame."""
        return PandaUtils().convert_to_dataframe(contents=contents)

    def _get_existing_categories(self, category_codes: List[int]) -> List[CategoryDB]:
        """Get the existing categories."""
        return (
            self.db.query(CategoryDB).filter(CategoryDB.code.in_(category_codes)).all()
        )

    def _get_sub_categories_mapping(
        self, row: DataFrame, category_mapping: Dict[int, CategoryDB]
    ) -> Dict[str, Any]:
        """Get the sub-categories mapping."""
        category_id = row["CATEGORY_CODE"]
        sub_category_name = row["SUB_CATEGORY_NAME"]
        sub_category_code = row["SUB_CATEGORY_CODE"]
        category = category_mapping.get(category_id)
        if category is None:
            return None
        return AttributeMapper(
            code=sub_category_code, name=sub_category_name, category_id=category.id
        ).__dict__
