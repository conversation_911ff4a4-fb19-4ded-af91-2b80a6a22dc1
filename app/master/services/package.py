from typing import Any, Dict, List, Optional
from sqlalchemy.exc import IntegrityError

import pandas as pd
import io
from app.database.database import SessionLocal
from app.master.exception.errors.package import PackageErrors
from app.master.exception.errors.package_step import PackageStepErrors
from app.master.models.category import CategoryDB
from app.master.models.document_master import DocumentMaster
from app.master.models.package import (
    PackageDB,
    PackageStepDocumentDB,
    PackageStepMasterDB,
    StepMaster,
)
from app.master.models.sub_category import SubCategoryDB
from app.master.services.document_master import DocumentMasterService
from app.master.services.package_step import PackageStepMasterService
from app.master.services.package_step_document import PackageStepDocumentHelper
from pandas import DataFrame
from sqlalchemy import and_, or_, func
from sqlalchemy.orm import Session, joinedload
from utils.db import BaseService, QueryWithCount

# Base.metadata.create_all(bind=engine)
from utils.logger import setup_logger
from utils.mapper import AttributeMapper
from utils.pagination import PaginationHelper
from utils.panda import PandaUtils

from ..utils.execute.package_step import insert_all_package_steps,create_optional_documents,create_optional_documents_for_package_steps

from app.master.helpers.logger import  package_logger

class PackageService(BaseService):
    """
    Service class for managing package-related operations.
    """

    def __init__(self) -> None:
        self.db = SessionLocal()

    def get_package_by_id(self, package_id: int, db: Session):
        """Retrieve a package by ID."""
        try:
            package = self.get_package_or_raise_exception(value=package_id, db=db)
            return package
        except Exception as e:
            package_logger.error(str(e))
            raise

    def get_all_packages(self, db: Session) -> List[PackageDB]:
        """Retrieve all packages."""
        try:
            packages = self.get_all(db, PackageDB)
            return packages
        except Exception as e:
            package_logger.error(str(e))
            raise

    def row_to_dict(self, row):
        """Convert SQLAlchemy row result to dictionary."""
        if isinstance(row, dict):
            return row
        if hasattr(row, "_asdict"):
            return row._asdict()
        return {column: value for column, value in row.items()}

    def get_all_packages_for_dropdown(self, db: Session, **kwargs) -> List[dict]:
        """Retrieve all packages with category and subcategory names and codes."""
        try:
            from sqlalchemy import case
            
            scheme_type_id = kwargs.pop("scheme_type_id", None)
            category_id = kwargs.pop("category_id", None)
            sub_category_id = kwargs.pop("sub_category_id", None)
            verbose_code_search = kwargs.pop("verbose_code", None)  # New parameter
            # Define MJP_JAY_SCHEME_ID constant
            MJP_JAY_SCHEME_ID = 1  # Update with the actual ID value
            
            # Create a verbose_code expression directly in the query - updated syntax
            verbose_code_expr = case(
                (CategoryDB.scheme_type_id == MJP_JAY_SCHEME_ID,
                    case(
                        (SubCategoryDB.code != None,
                            func.concat(
                                CategoryDB.code,
                                SubCategoryDB.code,
                                ".",
                                PackageDB.procedure_code
                            )
                        ),
                        else_=func.concat(
                            CategoryDB.code,
                            ".",
                            PackageDB.procedure_code
                        )
                    )
                ),
                else_=PackageDB.procedure_code
            ).label("verbose_code")
            
            packages = (
                db.query(
                    PackageDB.id,
                    PackageDB.procedure_code,
                    PackageDB.procedure_name,
                    PackageDB.package_amount,
                    verbose_code_expr,  # Use the expression instead of the hybrid property
                    CategoryDB.name.label("category_name"),
                    CategoryDB.code.label("category_code"),
                    SubCategoryDB.name.label("subcategory_name"),
                    SubCategoryDB.code.label("subcategory_code"),
                    PackageDB.category_id,
                    PackageDB.sub_category_id,
                    CategoryDB.scheme_type_id,
                )
                .join(CategoryDB, PackageDB.category_id == CategoryDB.id)
                .outerjoin(SubCategoryDB, PackageDB.sub_category_id == SubCategoryDB.id)
            )
    
            if scheme_type_id:
                packages = packages.filter(CategoryDB.scheme_type_id == scheme_type_id)
    
            if category_id:
                packages = packages.filter(PackageDB.category_id == category_id)
    
            if sub_category_id:
                packages = packages.filter(PackageDB.sub_category_id == sub_category_id)
            
            # Add search by verbose_code
            if verbose_code_search:
                # Using having clause since we need to filter by the computed expression
                packages = packages.having(verbose_code_expr.like(f"%{verbose_code_search}%"))
            
            # Apply sorting
            packages = packages.order_by(PackageDB.id)
            
            # Execute query
            packages = packages.all()
            return packages
            
        except Exception as e:
            package_logger.error(str(e))
            raise
    def get_package_details(self, package_id, db):
        package = (
            db.query(PackageDB)
            .options(
                joinedload(PackageDB.categories),
                joinedload(PackageDB.sub_categories).joinedload(SubCategoryDB.category),
                joinedload(PackageDB.steps),
                joinedload(PackageDB.package_step).joinedload(
                    PackageStepMasterDB.steps
                ),
                joinedload(PackageDB.package_step)
                .joinedload(PackageStepMasterDB.package_step_document)
                .joinedload(PackageStepDocumentDB.document_master),
            )
            .filter(PackageDB.id == package_id)
            .first()
        )
        return package

    def filter_packages(
        self,
        procedure_code: Optional[str],
        procedure_name: Optional[str],
        package_amount: Optional[int],
        db: Session,
        page: int,
        limit: int,
    ):
        """Filter packages by procedure code and procedure name."""
        query_with_count = QueryWithCount(self.init_query(db=db, model=PackageDB))
        query_with_count.apply_filter(
            db, PackageDB, PackageDB.procedure_code, procedure_code
        )
        query_with_count.apply_filter(
            db, PackageDB, PackageDB.procedure_name, procedure_name
        )
        query_with_count.apply_filter(
            db, PackageDB, PackageDB.package_amount, package_amount
        )

        query_with_count.query = PaginationHelper.paginate_query(
            query_with_count.query, page, limit
        )
        return query_with_count.query.all(), query_with_count.query.count()

    def filter_and_sort_packages(
        self,
        procedure_code: Optional[str],
        procedure_name: Optional[str],
        package_amount: Optional[str],
        category_name: Optional[str],
        sub_category_name: Optional[str],
        category_code: Optional[str],
        sub_category_code: Optional[str],
        verbose_code: Optional[str],  # Add this parameter
        scheme: Optional[int],
        order_by: Optional[str],
        db: Session,
        page: int,
        size: int,
    ):
        """Filter packages by various attributes including verbose code."""
        try:
            from sqlalchemy import case
            
            # Define MJP_JAY_SCHEME_ID constant
            MJP_JAY_SCHEME_ID = 1  # Update with actual ID if needed
            
            # Start the base query
            query = db.query(PackageDB)
    
            if procedure_code is not None:
                query = self.filter_value_like(
                    db, PackageDB, PackageDB.procedure_code, procedure_code
                )
            if procedure_name is not None:
                query = self.filter_value_like(
                    db, PackageDB, PackageDB.procedure_name, procedure_name
                )
            if package_amount is not None:
                # Exact match for package_amount
                query = query.filter(PackageDB.package_amount == package_amount)
        
            if category_name is not None:
                query = (
                    query.join(PackageDB.categories)
                    .filter(CategoryDB.name.like(f"%{category_name}%"))
                    .distinct()
                )
            if sub_category_name is not None:
                query = (
                    query.join(PackageDB.sub_categories)
                    .filter(SubCategoryDB.name.like(f"%{sub_category_name}%"))
                    .distinct()
                )
            if category_code is not None:
                query = (
                    query.join(PackageDB.categories)
                    .filter(CategoryDB.code.like(f"%{category_code}%"))
                    .distinct()
                )
            if sub_category_code is not None:
                query = (
                    query.join(PackageDB.sub_categories)
                    .filter(SubCategoryDB.code.like(f"%{sub_category_code}%"))
                    .distinct()
                )
            if scheme is not None:
                query = query.join(PackageDB.categories).filter(
                    CategoryDB.scheme_type_id == scheme
                )
            # Handle the verbose_code filter
            if verbose_code is not None:
                # Create a dynamic verbose_code expression
                verbose_code_expr = case(
                    (CategoryDB.scheme_type_id == MJP_JAY_SCHEME_ID,
                        case(
                            (SubCategoryDB.code != None,
                                func.concat(
                                    CategoryDB.code,
                                    SubCategoryDB.code,
                                    ".",
                                    PackageDB.procedure_code
                                )
                            ),
                            else_=func.concat(
                                CategoryDB.code,
                                ".",
                                PackageDB.procedure_code
                            )
                        )
                    ),
                    else_=PackageDB.procedure_code
                )
                
                # Apply joins and filter
                query = (
                    query.join(PackageDB.categories)
                    .outerjoin(PackageDB.sub_categories)
                    .filter(verbose_code_expr.like(f"%{verbose_code}%"))
                    .distinct()
                )
    

    
            if order_by is not None:
                query = self.order_query(query, PackageDB, order_by)  # Changed to PackageDB from DocumentMaster
    
            query = query.filter(PackageDB.is_deleted == False)
            total_count = query.count()
            query = PaginationHelper.paginate_query(query, page, size)
            if not query.all():
                return query.all(), 0
            return query.all(), total_count
        except Exception as e:
            package_logger.error(str(e))
            raise

    
    def create_package(self, package_data, db: Session) -> PackageDB:
        """Create a new package."""
        try:
            package_payload = package_data.dict()
            step_ids = package_payload.pop("steps")
            package_payload = {k: v for k, v in package_payload.items() if v is not None}

            self.is_package_data_unique(package_payload)

            # Create the package
            package = PackageDB(**package_payload)
            db.add(package)
            db.flush()

            # Create the package steps
            self.create_package_steps(
                package_id=package.id, step_ids=step_ids, db=db
            )

            db.refresh(package)
            db.commit()

            return package
        except IntegrityError as e:
            db.rollback()
            if "Duplicate entry" in str(e.orig):
                PackageErrors().raise_package_already_exists_exception(
                    package=package_data.dict()
                )
            else:
                package_logger.error(str(e), exc_info=True)
                raise
        except Exception as e:
            db.rollback()
            package_logger.error(str(e), exc_info=True)
            raise

    def update_package(
        self, package_id: int, package_data: dict, db: Session
    ) -> PackageDB:
        """Update a package."""
        try:
            with db.begin():
                existing_package = self.get_package_by_id(package_id, db=db)
                # Convert package data to dictionary
                package_payload = package_data.dict()
                # Extract steps data
                step_ids = package_payload.pop("steps", [])

                # Validate if the updated package data is unique
                is_package_exists = self.is_update_package_data_unique(
                    db=db, package_payload=package_payload, exclude_id=package_id
                )
                if is_package_exists:
                    PackageErrors().raise_package_already_exists_exception(
                        package=package_data.dict()
                    )

                # Update package details
                for key, value in package_payload.items():
                    setattr(existing_package, key, value)

                # If steps are provided, update them
                if step_ids:
                    self.update_package_steps(
                        package_id=package_id, step_ids=step_ids, db=db
                    )

                db.refresh(existing_package)
                return existing_package
        except Exception as e:
            package_logger.error(str(e))
            raise

    def update_package_steps(self, package_id: int, step_ids: list, db: Session):
        """Update steps for an existing package."""
        try:
            # Get existing package steps for the given package_id
            existing_steps = (
                db.query(PackageStepMasterDB.step_id)
                .filter_by(package_id=package_id)
                .all()
            )
            existing_step_ids_set = {step_id for step_id, in existing_steps}

            # Convert step_ids to set
            step_ids_set = set(step_ids)

            # Determine steps to add and steps to remove
            new_step_ids = step_ids_set - existing_step_ids_set
            remove_step_ids = existing_step_ids_set - step_ids_set

            # Remove outdated package steps
            if remove_step_ids:
                db.query(PackageStepMasterDB).filter(
                    PackageStepMasterDB.package_id == package_id,
                    PackageStepMasterDB.step_id.in_(remove_step_ids),
                ).delete(synchronize_session=False)

            # Add new package steps
            new_package_steps = [
                PackageStepMasterDB(package_id=package_id, step_id=step_id)
                for step_id in new_step_ids
            ]
            if new_package_steps:
                db.bulk_save_objects(new_package_steps)

            # Commit changes to step mapping
        except Exception as e:
            package_logger.error(str(e))
            raise

    def delete_package(self, package_id: int, force_delete: bool, db: Session) -> int:
        """Delete a package."""
        try:
            package = self.get_package_by_id(package_id, db=db)
            if package.package_steps:
                PackageErrors().raise_package_has_steps_exception(
                    package_id=package_id
                )
            elif package.cases:
                PackageErrors().raise_package_has_cases_exception(
                    package_id=package_id
                )
            if force_delete:
                db.delete(package)
            else:
                package.is_deleted = True
            db.commit()
            return True, None
        except Exception as e:
            package_logger.error(str(e))
            raise

    def get_package_by_attribute(self, value: str, db: Session, attribute: str = None):
        """Retrieve a package by a specific attribute."""
        try:
            if not attribute:
                attribute = "id"

            kwargs = {f"{attribute}": value, "is_deleted": False}
            # Query the package from the database
            # package = self.get_by_attribute(db, PackageDB, attribute, value)
            package = self.get_by_attributes(db, PackageDB, **kwargs)
            if not package:
                return False, None
            return True, package
        except Exception as e:
            package_logger.error(str(e))
            raise

    def get_package_or_raise_exception(self, value, db: Session, attribute=None):
        """Retrieve a package or raise an exception."""
        try:
            is_exist, package = self.get_package_by_attribute(
                value=value, db=db, attribute=attribute
            )
            if not is_exist:
                PackageErrors().raise_package_not_found_exception(package_id=value)
            return package
        except Exception as e:
            package_logger.error(str(e))
            raise

    def get_package_by_code(self, code: str, db: Session):
        """Retrieve a package by code."""
        try:
            is_exist, package = self.get_package_by_attribute(
                value=code, db=db, attribute="procedure_code"
            )
            return is_exist, package
        except Exception as e:
            package_logger.error(str(e))
            raise

    def get_package_by_name(self, name: str, db: Session):
        """Retrieve a package by name."""
        try:
            is_exist, package = self.get_package_by_attribute(
                value=name, db=db, attribute="procedure_name"
            )
            return is_exist, package
        except Exception as e:
            package_logger.error(str(e))
            raise

    # def is_package_data_unique(self, package_payload) -> bool:
    #     """Check if a package is unique."""
    #     is_package_exists = self.get_first_package(package_payload)
    #     return bool(is_package_exists)

    def is_procedure_code_unique_within_category(self, code: str, category_id: int) -> bool:
        """Check if a procedure code is unique within its category.
        
        Args:
            code: The procedure code to check
            category_id: The category ID to check within
            
        Returns:
            True if the code is unique, False if it already exists
        """
        try:
            code_exists = self.db.query(PackageDB).filter(
                and_(
                    PackageDB.procedure_code == code,
                    PackageDB.category_id == category_id,
                    PackageDB.is_deleted == False
                )
            ).first()
            return not bool(code_exists)
        except Exception as e:
            package_logger.error(f"Error checking procedure code uniqueness: {e}")
            # If there's any error, assume it's not unique to be safe
            return False




    def is_procedure_name_unique_within_category(self, name: str, category_id: int) -> bool:
        """Check if a procedure name is unique within its category.
        
        Args:
            name: The procedure name to check
            category_id: The category ID to check within
            
        Returns:
            True if the name is unique, False if it already exists
        """
        try:
            name_exists = self.db.query(PackageDB).filter(
                and_(
                    PackageDB.procedure_name == name,
                    PackageDB.category_id == category_id,
                    PackageDB.is_deleted == False
                )
            ).first()
            return not bool(name_exists)
        except Exception as e:
            package_logger.error(f"Error checking procedure name uniqueness: {e}")
            # If there's any error, assume it's not unique to be safe
            return False


    def is_package_data_unique(self, package_payload) -> bool:
        """Check if a package is unique based on procedure code and name within category.
        
        Returns True if a package with the same code or name already exists,
        False if it's unique.
        """
        category_id = package_payload.get("category_id")
        procedure_code = package_payload.get("procedure_code")
        procedure_name = package_payload.get("procedure_name")
        
        if not self.is_procedure_name_unique_within_category(name=procedure_name, category_id=category_id):
            PackageErrors().raise_procedure_name_already_exists_exception(
                procedure_name=procedure_name,
                msg=f"Package name {procedure_name} already exists in the category {category_id}"
            )
        if not self.is_procedure_code_unique_within_category(code=procedure_code, category_id=category_id):
            PackageErrors().raise_procedure_code_already_exists_exception(
                procedure_code=procedure_code,
                msg=f"Package code {procedure_code} already exists in the category {category_id}"
            )
        
        # Check if either code or name already exists in the category
        query = self.db.query(PackageDB).filter(
            and_(
                PackageDB.category_id == category_id,
                or_(
                    PackageDB.procedure_code == procedure_code,
                    PackageDB.procedure_name == procedure_name
                ),
                PackageDB.is_deleted == False
            )
        )
        
        # Handle subcategory if present
        sub_category_id = package_payload.get("sub_category_id")
        if sub_category_id:
            query = query.filter(PackageDB.sub_category_id == sub_category_id)
        else:
            query = query.filter(PackageDB.sub_category_id.is_(None))
        
        # Execute query
        existing = query.first()
        
        if existing:
            PackageErrors().raise_package_already_exists_exception(
                package=package_payload,
                msg=f"Package with code {procedure_code} or name {procedure_name} already exists in the category {category_id}"
            )
        return False

    def is_package_exists_by_name(self, name: str) -> bool:
        """Check if a package name exists."""
        try:
            is_exist, _ = self.get_package_by_attribute(
                value=name, db=self.db, attribute="procedure_name"
            )
            return is_exist
        except Exception as e:
            package_logger.error(str(e))
            raise
        finally:
            self.db.close()

    def is_package_exists_by_category_id(self, category_id) -> bool:
        """Check if a package name exists by category_id."""
        try:
            is_exist, _ = self.get_package_by_attribute(
                value=category_id, db=self.db, attribute="category_id"
            )
            return is_exist
        except Exception as e:
            package_logger.error(str(e))
            raise
        finally:
            self.db.close()

    def is_package_exists_by_subcategory_id(self, sub_category_id) -> bool:
        """Check if a package name exists by sub_category_id."""
        try:
            is_exist, _ = self.get_package_by_attribute(
                value=sub_category_id, db=self.db, attribute="sub_category_id"
            )
            return is_exist
        except Exception as e:
            package_logger.error(str(e))
            raise
        finally:
            self.db.close()

    def is_package_exists_by_code(self, code: str) -> bool:
        """Check if a package code exists."""
        try:
            is_exist, _ = self.get_package_by_attribute(
                value=code, db=self.db, attribute="procedure_code"
            )
            return is_exist
        except Exception as e:
            package_logger.error(str(e))
            raise
        finally:
            self.db.close()

    def is_package_exists_by_id(self, package_id: str) -> bool:
        """Check if a package id exists."""
        try:
            is_exist, _ = self.get_package_by_attribute(
                value=package_id, db=self.db, attribute="id"
            )
            return is_exist
        except Exception as e:
            package_logger.error(str(e))
            raise
        finally:
            self.db.close()

    def is_update_package_data_unique(
        self, db: Session, package_payload, exclude_id: int = None
    ) -> bool:
        """Check if a package is unique, excluding the current package ID."""

        # Get the category to retrieve the scheme_type_id
        category = (
            db.query(CategoryDB)
            .filter(CategoryDB.id == package_payload["category_id"])
            .first()
        )
        # Build the query to check for existing packages
        existing_package_query = (
            db.query(PackageDB)
            .join(CategoryDB)
            .filter(
                # PackageDB.procedure_code == package_payload["procedure_code"],
                PackageDB.procedure_name == package_payload["procedure_name"],
                PackageDB.package_amount == package_payload["package_amount"],
                PackageDB.category_id == package_payload["category_id"],
                PackageDB.sub_category_id == package_payload.get("sub_category_id"),
                CategoryDB.scheme_type_id == category.scheme_type_id,
            )
        )
        # Exclude the current package from the uniqueness check
        if exclude_id:
            existing_package_query = existing_package_query.filter(
                PackageDB.id != exclude_id
            )

        # Execute the query
        existing_package = existing_package_query.first()
        is_package_exists = bool(existing_package)
        return is_package_exists

    def get_first_package(self, package_data: dict):
        """Retrieve the first package."""
        return self.get_first_record(db=self.db, model=PackageDB, **package_data)

    def bulk_insert_packages(self, contents: Any) -> List[Dict[str, Any]]:
        """Bulk insert the packages."""
        return PackagesInserter(self.db).bulk_insert_packages(contents)

    def create_package_steps(
        self, package_id: int, step_ids: list, db: Session
    ) -> List[Dict[str, Any]]:
        """Create steps for a package."""
        try:
            return (
                PackageStepMasterService().create_or_update_package_steps_for_package(
                    package_id=package_id, step_ids=step_ids, db=db
                )
            )
        except Exception as e:
            package_logger.error(str(e))
            raise

    def create_package_steps_for_all_packages(self, db: Session):
        """
        Create steps for all packages, including optional documents and their mappings.

        Args:
            db (Session): The database session.
        """
        try:
            # Step 1: Create optional documents
            create_optional_documents(db=db)

            # Step 2: Insert all package steps
            insert_all_package_steps(db=db)

            # Step 3: Map optional documents to all package steps
            # create_optional_documents_for_package_steps(db=db)

        except Exception as e:
            db.rollback()
            raise
    

    def create_package_step_documents(self, package_id: int, step_document: dict):
        """Create step document for a package."""
        try:

            return PackageStepDocumentHelper().handle_package_step_document(
                package_id=package_id, step_document=step_document
            )
        except Exception as e:
            package_logger.error(str(e))
            raise

    def update_package_payload(self, package_id: int, package_data: dict) -> PackageDB:
        """Update a package payload."""
        if PackageDB.sub_category_id in package_data:
            package_data[PackageDB.sub_category_id] = int(
                package_data[PackageDB.sub_category_id]
            )
        return self.update_package(
            package_id=package_id, package_data=package_data, db=self.db
        )


class PackagesInserter:
    """Class to bulk insert the packages."""

    def __init__(self, db: Session):
        self.db = db

    def bulk_insert_packages(self, contents: Any) -> List[Dict[str, Any]]:
        """Bulk insert the packages."""
        df = self._convert_to_dataframe(contents)
        category_ids = df["CATEGORY_CODE"].unique()
        sub_category_ids = df["SUB_CATEGORY_CODE"].unique()
        existing_categories = self._get_existing_categories(category_ids)
        existing_sub_categories = self._get_existing_sub_categories(sub_category_ids)
        category_mapping = {category.code: category for category in existing_categories}
        sub_category_mapping = {
            sub_category.code: sub_category for sub_category in existing_sub_categories
        }
        packages = [
            self._get_packages_mapping(row, category_mapping, sub_category_mapping)
            for _, row in df.iterrows()
            if self._get_packages_mapping(row, category_mapping, sub_category_mapping)
            is not None
        ]
        self.db.bulk_insert_mappings(PackageDB, packages)
        self.db.commit()
        return packages

    @staticmethod
    def _convert_to_dataframe(contents: Any) -> DataFrame:
        """Convert the contents to a DataFrame."""
        return PandaUtils().convert_to_dataframe(contents=contents)

    def _get_existing_categories(self, category_codes: List[int]) -> List[CategoryDB]:
        """Get the existing categories."""
        return (
            self.db.query(CategoryDB).filter(CategoryDB.code.in_(category_codes)).all()
        )

    def _get_existing_sub_categories(
        self, sub_category_codes: List[int]
    ) -> List[SubCategoryDB]:
        """Get the existing sub-categories."""
        return (
            self.db.query(SubCategoryDB)
            .filter(SubCategoryDB.code.in_(sub_category_codes))
            .all()
        )

    def _get_packages_mapping(
        self,
        row: DataFrame,
        category_mapping: Dict[int, CategoryDB],
        sub_category_mapping: Dict[int, SubCategoryDB],
    ) -> Dict[str, Any]:
        """Get the packages mapping."""
        category_code = row["CATEGORY_CODE"]
        sub_category_code = row["SUB_CATEGORY_CODE"].strip().replace(category_code, "")
        procedure_code = row["PROCEDURE_CODE"]
        procedure_name = row["PROCEDURE_NAME"]
        package_amount = row["PACKAGE_AMOUNT"]
        category = category_mapping.get(category_code)
        sub_category = sub_category_mapping.get(sub_category_code)
        if category is None or sub_category is None:
            return None
        return AttributeMapper(
            procedure_code=procedure_code,
            procedure_name=procedure_name,
            package_amount=package_amount,
            categpry_master_id=category.id,
            sub_categpry_master_id=sub_category.id,
        ).__dict__


class PackageExcelUtil:

    def __init__(self, contents: Any, scheme_id: int) -> None:
        self.contents = contents
        self.scheme_id = scheme_id

    def read_excel(self) -> pd.DataFrame:
        """Read the Excel file."""
        try:
            df = pd.read_excel(io.BytesIO(self.contents))
            # Rename columns to uppercase with underscores
            df.rename(columns=lambda x: x.upper().replace(" ", "_"), inplace=True)
            
            # Convert PACKAGE_AMOUNT to numeric, replace NaN with 0
            df["PACKAGE_AMOUNT"] = (
                pd.to_numeric(df["PACKAGE_AMOUNT"], errors="coerce").fillna(0).astype(int)
            )
            
            # Add SUB_CATEGORY_CODE and SUB_CATEGORY_NAME columns for MJP-JAY scheme if they don't exist
            if "SUB_CATEGORY_CODE" not in df.columns:
                df["SUB_CATEGORY_CODE"] = ""
                
            if "SUB_CATEGORY_NAME" not in df.columns:
                df["SUB_CATEGORY_NAME"] = df["CATEGORY_NAME"]
                
            return df
        except Exception as e:
            package_logger.error(f"Error reading Excel file: {str(e)}")
            raise

    def create_category(self, row: pd.Series) -> CategoryDB:
        """Create a category."""
        category_code = row["CATEGORY_CODE"].strip()
        category_name = row["CATEGORY_NAME"].strip()

        # Use scheme_id directly
        scheme_type_id = self.scheme_id

        category = CategoryDB(
            code=category_code, 
            name=category_name,
            scheme_type_id=scheme_type_id
        )
        return category

    def create_sub_category(self, row: pd.Series, category_id: int) -> SubCategoryDB:
        """Create a sub-category."""
        category_code = row["CATEGORY_CODE"].strip()
        sub_category_code = row["SUB_CATEGORY_CODE"].strip().replace(category_code, "")
        sub_category_name = row["SUB_CATEGORY_NAME"].strip()
        sub_category = SubCategoryDB(
            code=sub_category_code, name=sub_category_name, category_id=category_id
        )
        return sub_category

    def create_package(
        self, row: pd.Series, category_id: int, sub_category_id: int = None
    ) -> PackageDB:
        """Create a package."""
        if sub_category_id is not None:

            sub_category_code = (
                row["SUB_CATEGORY_CODE"].strip().replace(row["CATEGORY_CODE"].strip(), "")
            )
            procedure_code = (
                row["PROCEDURE_CODE"]
                .strip()
                .replace(f"{row['CATEGORY_CODE'].strip()}{sub_category_code}.", "")
            )
        else:
            procedure_code = row["PROCEDURE_CODE"]
        package_amount = row["PACKAGE_AMOUNT"]
        procedure_name = row["PROCEDURE_NAME"].strip()
        package = PackageDB(
            procedure_code=procedure_code,
            procedure_name=procedure_name,
            package_amount=package_amount,
            category_id=category_id,
            sub_category_id=sub_category_id,
        )
        return package

    def get_category(
        self, category_code: str, category_name: str, db: Session
    ) -> CategoryDB:
        """Get the category."""
        category = (
            db.query(CategoryDB)
            .filter_by(code=category_code, name=category_name)
            .first()
        )
        return category

    def get_sub_category(
        self,
        sub_category_code: str,
        sub_category_name: str,
        category_id: int,
        db: Session,
    ) -> SubCategoryDB:
        """Get the sub-category."""
        sub_category = (
            db.query(SubCategoryDB)
            .filter_by(
                code=sub_category_code, name=sub_category_name, category_id=category_id
            )
            .first()
        )
        return sub_category

    def get_package(
        self,
        procedure_code: str,
        procedure_name: str,
        package_amount: int,
        category_id: int,
        sub_category_id: int,
        db: Session,
    ) -> PackageDB:
        """Get the package."""
        package = (
            db.query(PackageDB)
            .filter_by(
                procedure_code=procedure_code,
                procedure_name=procedure_name,
                package_amount=package_amount,
                category_id=category_id,
                sub_category_id=sub_category_id,
            )
            .first()
        )
        return package

    def create_category_if_not_exists(self, row: pd.Series, db: Session) -> CategoryDB:
        """Create a category if it does not exist."""
        category_code = row["CATEGORY_CODE"].strip()
        category_name = row["CATEGORY_NAME"].strip()
        category = self.get_category(category_code, category_name, db)
        if not category:
            category = self.create_category(row)
            db.add(category)
            db.commit()
        return category

    def create_sub_category_if_not_exists(
        self, row: pd.Series, category_id: int, db: Session
    ) -> SubCategoryDB:
        """Create a sub-category if it does not exist."""
        category_code = row["CATEGORY_CODE"].strip()
        sub_category_code = row["SUB_CATEGORY_CODE"].strip().replace(category_code, "")
        sub_category_name = row["SUB_CATEGORY_NAME"].strip()
        sub_category = self.get_sub_category(
            sub_category_code, sub_category_name, category_id, db
        )
        if not sub_category:
            sub_category = self.create_sub_category(row, category_id)
            db.add(sub_category)
            db.commit()
        return sub_category

    def create_package_if_not_exists(
        self, row: pd.Series, category_id: int, db: Session, sub_category_id: int = None
    ) -> PackageDB:
        """Create a package if it does not exist."""
        category_code = row["CATEGORY_CODE"].strip()
        if sub_category_id is not None:

            sub_category_code = (
                row["SUB_CATEGORY_CODE"].strip().replace(category_code, "")
            )
            procedure_code = (
                row["PROCEDURE_CODE"]
                .strip()
                .replace(f"{category_code}{sub_category_code}.", "")
            )
        else:
            procedure_code = row["PROCEDURE_CODE"]
        package_amount = row["PACKAGE_AMOUNT"]
        procedure_name = row["PROCEDURE_NAME"].strip()
        package = self.get_package(
            procedure_code,
            procedure_name,
            package_amount,
            category_id,
            sub_category_id,
            db,
        )
        if not package:
            package = self.create_package(row, category_id, sub_category_id)
            db.add(package)
            db.commit()
        return package

    def create_packages(self, db: Session, upload_type: str) -> List[Dict[str, Any]]:
        """
        Create packages based on the upload type (Fresh or Additional).

        - Fresh: Marks all existing procedures as inactive and uploads new ones as active.
        - Additional: Retains existing procedures, deactivates duplicates, and uploads new ones as active.
        """

        df = self.read_excel()
        packages = []

        # Define required columns
        required_columns = ["CATEGORY_CODE", "CATEGORY_NAME", "PROCEDURE_CODE", 
                            "PROCEDURE_NAME", "PACKAGE_AMOUNT", "PRE_INVESTIGATIONS", "FIXED_PREAUTH_REQUIRED_DOCUMENTS", "DURING_TREATMENT_DOCUMENTS", "POST_INVESTIGATIONS", "FIXED_DISCHARGE_REQUIRED_DOCUMENTS"]
        
        # Convert column names to uppercase with underscores
        df.rename(columns=lambda x: x.upper().replace(" ", "_"), inplace=True)
        
        # Check if all required columns exist
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            # Create specific error for missing columns
            package_errors = PackageErrors()
            # We need to add a custom method to PackageErrors class
            package_errors.raise_missing_columns_exception(missing_columns)
        

        if upload_type == "fresh":
            # Mark all existing procedures as inactive
            db.query(PackageDB).filter(PackageDB.is_deleted == False).update({"is_deleted": True})

        for index, row in df.iterrows():
            category = self.create_category_if_not_exists(row, db)
            sub_category_id = None

            if self.scheme_id == 1:  # Assuming 1 represents MJP-JAY
                sub_category = self.create_sub_category_if_not_exists(row, category.id, db)
                sub_category_id = sub_category.id
                # if sub_category_id is not None:
                #     sub_category_code = row["SUB_CATEGORY_CODE"].strip().replace(row["CATEGORY_CODE"].strip(), "")
                #     procedure_code = row["PROCEDURE_CODE"].strip().replace(f"{row['CATEGORY_CODE'].strip()}{sub_category_code}.", "")
                # else:
                #     procedure_code = row["PROCEDURE_CODE"]
            # Check if the procedure already exists and is active
            existing_package = db.query(PackageDB).filter(
                PackageDB.procedure_code == row["PROCEDURE_CODE"].strip(),
                PackageDB.is_deleted == False
            ).first()

            if upload_type == "additional" and existing_package:
                # Mark existing active procedure as inactive
                existing_package.is_deleted = True

            # Create or update the package
            package = self.create_package_if_not_exists(
                row=row, category_id=category.id, sub_category_id=sub_category_id, db=db
            )
            packages.append(package)

        db.commit()

        # Create package steps for all packages
        package_steps = PackageService().create_package_steps_for_all_packages(db=db)
        # Create documents from the dataframe
        documents = DocumentMasterService().bulk_upload_documents(df, db,self.scheme_id)
        # Map documents to package steps
        self._map_documents_to_package_steps(df, db)

        return packages

    def _map_documents_to_package_steps(self, df: pd.DataFrame, db: Session):
        """Map documents to package steps based on the dataframe."""
        try:
            package_logger.info("Mapping documents to package steps")
            
            # Define the mapping between Excel columns and step types
            column_to_step_type = {
                "PRE_INVESTIGATIONS": "PRE_INVESTIGATION",
                "FIXED_PREAUTH_REQUIRED_DOCUMENTS": "PRE_AUTHORIZATION",
                "DURING_TREATMENT_DOCUMENTS": "TREATMENT",
                "POST_INVESTIGATIONS": "CLAIM",
                "FIXED_DISCHARGE_REQUIRED_DOCUMENTS": "DISCHARGE"
            }
            
            # Process each row in the dataframe separately
            for index, row in df.iterrows():
                procedure_code = row["PROCEDURE_CODE"].strip()
                package_logger.info(f"Processing documents for procedure: {procedure_code}")
                
                # Get the package by procedure code
                package = db.query(PackageDB).filter(
                    PackageDB.procedure_code == procedure_code
                ).first()
                
                if not package:
                    package_logger.warning(f"Package not found for procedure code: {procedure_code}")
                    continue
                
                # Get all package steps for this package
                steps = db.query(PackageStepMasterDB).filter(
                    PackageStepMasterDB.package_id == package.id
                ).all()
                
                if not steps:
                    package_logger.warning(f"No steps found for package: {package.id}")
                    continue
                
                # Process each document column using the defined mapping
                for excel_column, step_type in column_to_step_type.items():
                    if (excel_column not in df.columns) or pd.isna(row[excel_column]) or not row[excel_column]:
                        continue
                    
                    # Parse document names (they might be comma or slash separated)
                    doc_names = []
                    if isinstance(row[excel_column], str):
                        if ',' in row[excel_column]:
                            doc_names = [name.strip() for name in row[excel_column].split(',') if name.strip() and name.strip().lower() != 'nan']
                        elif '/' in row[excel_column]:
                            doc_names = [name.strip() for name in row[excel_column].split('/') if name.strip() and name.strip().lower() != 'nan']
                        else:
                            doc_name = row[excel_column].strip()
                            if doc_name and doc_name.lower() != 'nan':
                                doc_names = [doc_name]
                    
                    if not doc_names:
                        package_logger.info(f"No valid documents found in column {excel_column} for procedure {procedure_code}")
                        continue
                    
                    package_logger.info(f"Column {excel_column} maps to step type {step_type} with documents: {doc_names}")
                    
                    # Get the step master record
                    step_master = db.query(StepMaster).filter(StepMaster.type == step_type).first()
                    if not step_master:
                        package_logger.warning(f"Step master not found for type: {step_type}")
                        continue
                    
                    # Find the package step for this package and step type
                    package_step = None
                    for step in steps:
                        if step.step_id == step_master.id:
                            package_step = step
                            break
                    
                    if not package_step:
                        package_logger.warning(f"Package step not found for package {package.id} and step type {step_type}")
                        continue
                    
                    # First, delete any existing document mappings for this package step
                    # This ensures we don't have duplicate or incorrect mappings
                    db.query(PackageStepDocumentDB).filter(
                        PackageStepDocumentDB.package_step_id == package_step.id
                    ).delete()
                    
                    # Find documents by name and create package step documents
                    # Keep track of documents we've already added to avoid duplicates
                    added_document_ids = set()
                    for doc_name in doc_names:
                        # Try exact match first
                        document = db.query(DocumentMaster).filter(
                            DocumentMaster.name == doc_name
                        ).first()
                        
                        # If not found, try case-insensitive match
                        if not document:
                            document = db.query(DocumentMaster).filter(
                                func.lower(DocumentMaster.name) == func.lower(doc_name)
                            ).first()
                        
                        if document and document.id not in added_document_ids:
                            # Check if this mapping already exists
                            existing_mapping = db.query(PackageStepDocumentDB).filter(
                                PackageStepDocumentDB.package_step_id == package_step.id,
                                PackageStepDocumentDB.document_id == document.id
                            ).first()
                            
                            if not existing_mapping:
                                # Create new package step document
                                package_step_doc = PackageStepDocumentDB(
                                    package_step_id=package_step.id,
                                    document_id=document.id
                                )
                                db.add(package_step_doc)
                                added_document_ids.add(document.id)
                                package_logger.info(f"Added document {doc_name} to package step {package_step.id} ({step_type})")
                            else:
                                package_logger.info(f"Document {doc_name} already mapped to package step {package_step.id} ({step_type})")
                        elif not document:
                            package_logger.warning(f"Document not found with name: {doc_name}")
                
                # Commit after processing each row to ensure clean transaction boundaries
                db.commit()
                package_logger.info(f"Completed document mapping for procedure: {procedure_code}")
            create_optional_documents_for_package_steps(db=db)
            package_logger.info("Successfully mapped documents to package steps")
            
        except Exception as e:
            db.rollback()
            package_logger.error(f"Error mapping documents to package steps: {str(e)}")
            raise
