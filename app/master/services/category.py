from typing import Any, Dict, List, Optional

from pandas import DataFrame
from sqlalchemy import and_
from sqlalchemy.orm import Session

from app.database.database import Base, SessionLocal, engine
from app.master.exception.custom.category import CategoryExceptions
from app.master.exception.errors.category import CategoryErrors
from app.master.models.category import CategoryDB
from app.master.models.scheme_type import SchemeType
from app.master.services.package import PackageService
from app.master.services.sub_category import SubCategoryService
from utils.db import BaseService, QueryWithCount
from utils.mapper import AttributeMapper
from utils.pagination import PaginationHelper
from utils.panda import PandaUtils

from utils.logger import setup_logger

category_logger = setup_logger("category_logger")

# Base.metadata.create_all(bind=engine)


class CategoryService(BaseService):
    """
    Service class for managing category-related operations.
    """

    def __init__(self) -> None:
        self.db = SessionLocal()

    def get_category_by_attribute(self, value: str, db: Session, attribute: str = None):
        """Retrieve a category by a specific attribute."""
        try:
            if not attribute:
                attribute = CategoryDB.id
            # Query the category from the database
            category = self.get_by_attribute(db, CategoryDB, attribute, value)
            if not category:
                error = CategoryExceptions.generate_category_not_found_error(
                    category_id=value
                )
                return False, error
            return True, category
        except Exception as e:
            category_logger.error(str(e))
            raise

    def get_category_by_id(self, category_id: int, db: Session):
        """Retrieve a category by ID."""
        try:
            is_exist, category = self.get_category_by_attribute(
                value=category_id, db=db
            )
            return is_exist, category
        except Exception as e:
            category_logger.error(str(e))
            raise

    def get_category_or_raise(self, category_id: int, db: Session) -> CategoryDB:
        try:
            category = self.get_by_attribute(db, CategoryDB, CategoryDB.id, category_id)

            if not category:
                CategoryErrors().raise_category_not_exists_exception(
                    category=category_id
                )
            return category
        except Exception as e:
            category_logger.error(str(e))
            raise

    def get_all_categories(self, db: Session, **kwargs) -> List[CategoryDB]:
        """Retrieve all categories."""
        try:
            scheme = kwargs.pop("scheme", None)
            categories = self.init_query(db, CategoryDB)
            if scheme:
                categories = categories.filter(CategoryDB.scheme_type_id == scheme)

            # order by name
            categories = categories.order_by(CategoryDB.name.asc())
            categories = categories.all()
            return categories
        except Exception as e:
            category_logger.error(str(e))
            raise

    def filter_categories(
        self,
        code: Optional[str],
        name: Optional[str],
        db: Session,
        page: int,
        limit: int,
    ):
        """Filter categories by code and name."""
        query_with_count = QueryWithCount(self.init_query(db=db, model=CategoryDB))

        query_with_count.apply_filter(db, CategoryDB, CategoryDB.code, code)
        query_with_count.apply_filter(db, CategoryDB, CategoryDB.name, name)

        query_with_count.query = PaginationHelper.paginate_query(
            query_with_count.query, page, limit
        )
        return query_with_count.query.all(), query_with_count.query.count()

    def filter_and_sort_categories(
        self,
        name: Optional[str],
        code: Optional[str],
        scheme: Optional[str],
        order_by: Optional[str],
        db: Session,
        page: int,
        limit: int,
    ):
        """Filter hospitals by name and city."""
        try:
            from app.master.models.scheme_type import SchemeType

            query = self.init_query(db=db, model=CategoryDB)

            if name is not None:
                query = self.filter_value_like(db, CategoryDB, CategoryDB.name, name)
            if code is not None:
                query = query.filter(CategoryDB.code.like(f"%{code}%"))
            if scheme is not None:
                query = (
                    query.join(CategoryDB.scheme_type)
                    .filter(SchemeType.type.like(f"%{scheme}%"))
                    .distinct()
                )
            query = self.order_query(query, CategoryDB, order_by)
            total_count = query.count()  # Get total count before pagination

            query = PaginationHelper.paginate_query(query, page, limit)
            return query.all(), total_count
        except Exception as e:
            category_logger.error(str(e))
            raise

    def create_category(self, category_data, db: Session) -> CategoryDB:
        """Create a new category."""
        try:
            # First do application-level validation
            self.is_category_data_unique(category_data)
            
            # Then try to create the category
            category = self.create(db, CategoryDB, **category_data.dict())
            return category
        except Exception as e:
            
            # Handle database integrity errors specifically
            self.handle_category_integrity_error(e, category_data)
            raise

    def update_category(
        self, category_id: int, category_data: dict, db: Session
    ) -> CategoryDB:
        """Update a category."""
        try:
            category = self.get_category_or_raise(category_id, db=db)
            is_valid = self.validate_update_data(category_data, category)
            category_data_dict = category_data.dict()
            code = category_data_dict.get("code")
            name = category_data_dict.get("name")
            # if code and self.check_unique_field(
            #     db, CategoryDB, "code", code, category_id
            # ):
            #     raise CategoryErrors().raise_category_code_already_exists_exception(
            #         code
            #     )
            # elif name and self.check_unique_field(
            #     db, CategoryDB, "name", name, category_id
            # ):
            #     raise CategoryErrors().raise_category_name_already_exists_exception(
            #         name
            #     )
            category = self.update(
                db, CategoryDB, category_id, **category_data_dict
            )
            return True, category
        except Exception as e:
            category_logger.error(str(e))
            # Handle database integrity errors specifically
            self.handle_category_integrity_error(e, category_data)
            raise

    def handle_category_integrity_error(self, error, category_data):
        """Handle database integrity errors specific to categories.
        
        Args:
            error: The exception that was raised
            category_data: The category data that caused the error
            
        Raises:
            Appropriate user-friendly exception based on the error
        """
        error_str = str(error)
        
        # Handle MySQL duplicate entry errors (error code 1062)
        if "1062" in error_str and "Duplicate entry" in error_str:
            # Extract more information to provide better error messages
            if isinstance(category_data, dict):
                data_dict = category_data
            else:
                # Handle Pydantic model
                data_dict = category_data.dict() if hasattr(category_data, 'dict') else {}
                
            code = data_dict.get('code', 'unknown')
            name = data_dict.get('name', 'unknown')
            scheme_type_id = data_dict.get('scheme_type_id', 'unknown')
            
            if "uq_category" in error_str:
                # General uniqueness constraint violation
                error_message = f"Category with code '{code}' and name '{name}' already exists in scheme {scheme_type_id}"
                CategoryErrors().raise_category_already_exists_exception(category=category_data, msg=error_message)
        else:
            # Re-raise other errors
            raise

    def delete_category(
        self, category_id: int, db: Session, force_delete: bool = False
    ) -> int:
        """Delete a category."""
        try:
            is_exist, category = self.get_category_by_id(category_id, db=db)
            if not is_exist:
                CategoryErrors().raise_category_not_exists_exception(
                    category=category_id
                )
            else:
                if not force_delete and self.has_subcategories(category_id):
                    CategoryErrors().raise_category_associated_with_sub_category_exception(
                        category_id=category_id
                    )
                elif not force_delete and self.has_packages(category_id):
                    CategoryErrors().raise_category_associated_with_package_exception(
                        category_id=category_id
                    )
                else:
                    is_deleted = self.delete(db, CategoryDB, category_id)
                    return is_deleted, category_id
        except Exception as e:
            category_logger.error(str(e))
            raise

    def has_subcategories(self, category_id: int) -> bool:
        """Check if a category has subcategories."""
        try:
            return SubCategoryService().is_subcategory_exists_by_category_id(
                category_id
            )
        except Exception as e:
            category_logger.error(str(e))
            raise
        finally:
            self.db.close()

    def has_packages(self, category_id: int) -> bool:
        """Check if a category has packages."""
        try:
            return PackageService().is_package_exists_by_category_id(category_id)
        except Exception as e:
            category_logger.error(str(e))
            raise
        finally:
            self.db.close()

    def is_category_exists_by_name(self, name: str) -> bool:
        """Check if a category name exists."""
        try:
            is_exist, _ = self.get_category_by_attribute(
                value=name, db=self.db, attribute=CategoryDB.name
            )
            return is_exist
        except Exception as e:
            category_logger.error(str(e))
            raise
        finally:
            self.db.close()

    def is_category_exists_by_id(self, category_id: int) -> bool:
        """Check if a category ID exists."""
        try:
            is_exist, _ = self.get_category_by_id(category_id, self.db)
            return is_exist
        except Exception as e:
            category_logger.error(str(e))
            raise
        finally:
            self.db.close()

    def is_category_exists_by_code(self, code: str) -> bool:
        """Check if a category code exists."""
        try:
            is_exist, _ = self.get_category_by_attribute(
                value=code, db=self.db, attribute=CategoryDB.code
            )
            return is_exist
        except Exception as e:
            category_logger.error(str(e))
            raise
        finally:
            self.db.close()

    def get_category_by_code(self, code: str, db: Session):
        """Retrieve a category by code."""
        try:
            is_exist, category = self.get_category_by_attribute(
                value=code, db=db, attribute=CategoryDB.code
            )
            return is_exist, category
        except Exception as e:
            category_logger.error(str(e))
            raise

    def get_category_by_name(self, name: str, db: Session):
        """Retrieve a category by name."""
        try:
            is_exist, category = self.get_category_by_attribute(
                value=name, db=db, attribute=CategoryDB.name
            )
            return is_exist, category
        except Exception as e:
            category_logger.error(str(e))
            raise

    def get_first_category(self, category_data: dict):
        """Retrieve the first category."""
        return self.get_first_record(db=self.db, model=CategoryDB, **category_data)

    # def is_category_data_unique(self, category_data) -> bool:
    #     """Check if a category is unique."""
    #     is_category_exists = self.get_first_category(category_data.dict())
    #     return bool(is_category_exists)


    def is_category_code_unique_within_scheme(self, code: str, scheme_type_id: int) -> bool:
        """Check if a category code is unique within its scheme."""
        code_exists = self.db.query(CategoryDB).filter(
            and_(
                CategoryDB.code == code,
                CategoryDB.scheme_type_id == scheme_type_id
            )
        ).first()
        return not bool(code_exists)
    
    def is_category_name_unique_within_scheme(self, name: str, scheme_type_id: int) -> bool:
        """Check if a category name is unique within its scheme."""
        name_exists = self.db.query(CategoryDB).filter(
            and_(
                CategoryDB.name == name,
                CategoryDB.scheme_type_id == scheme_type_id
            )
        ).first()
        return not bool(name_exists)

    def is_category_data_unique(self, category_data) -> bool:
        """Check if a category is unique within its scheme."""
        # Get the scheme_type_id from the category data
        scheme_type_id = category_data.scheme_type_id
        code = category_data.code
        name = category_data.name
        
        # Check if the category code is unique within its scheme
        if not self.is_category_code_unique_within_scheme(code, scheme_type_id):
            CategoryErrors().raise_category_code_already_exists_exception(code)
        
        # Check if the category name is unique within its scheme
        if not self.is_category_name_unique_within_scheme(name, scheme_type_id):
            CategoryErrors().raise_category_name_already_exists_exception(name)
        
        return True

    def validate_update_data(self, category_data, existing_category) -> bool:
        """Validate the update data for a category."""
        try:
            category_id = existing_category.id
            scheme_type_id = category_data.dict().get("scheme_type_id", existing_category.scheme_type_id)
            code = category_data.dict().get("code")
            name = category_data.dict().get("name")
            # Check code uniqueness only if code is being updated
            if code and code != existing_category.code:
                # Check if another category with the same code exists in the same scheme
                code_exists = self.db.query(CategoryDB).filter(
                    and_(
                        CategoryDB.code == code,
                        CategoryDB.scheme_type_id == scheme_type_id,
                        CategoryDB.id != category_id
                    )
                ).first()
                if code_exists and code_exists.id != category_id:
                    CategoryErrors().raise_category_code_already_exists_exception(code)
            
            # Check name uniqueness only if name is being updated
            if name and name != existing_category.name:
                # Check if another category with the same name exists in the same scheme
                name_exists = self.db.query(CategoryDB).filter(
                    and_(
                        CategoryDB.name == name,
                        CategoryDB.scheme_type_id == scheme_type_id,
                        CategoryDB.id != category_id
                    )
                ).first()
                if name_exists and name_exists.id != category_id:
                    CategoryErrors().raise_category_name_already_exists_exception(name)
            return True
        except Exception as e:
            category_logger.error(str(e))

            raise

    def bulk_insert_categories(self, contents: Any) -> List[Dict[str, Any]]:
        """Bulk insert the categories."""
        return CategoriersInserter(self.db).bulk_insert_categories(contents)


class CategoriersInserter:
    """Class to bulk insert the categories."""

    def __init__(self, db: Session):
        self.db = db

    def bulk_insert_categories(self, contents: Any) -> List[Dict[str, Any]]:
        """Bulk insert the categories."""
        df = self._convert_to_dataframe(contents)
        scheme_types = df["SCHEME_TYPE"].unique()
        existing_schemes = self._get_existing_schemes(scheme_types)
        scheme_mapping = {scheme.type: scheme for scheme in existing_schemes}

        categories = [
            self._get_categories_mapping(row, scheme_mapping)
            for _, row in df.iterrows()
        ]

        self.db.bulk_insert_mappings(CategoryDB, categories)
        self.db.commit()

        return categories

    @staticmethod
    def _convert_to_dataframe(contents: Any) -> DataFrame:
        """Convert the contents to a DataFrame."""
        return PandaUtils().convert_to_dataframe(contents=contents)

    def _get_existing_schemes(self, scheme_types: List[str]) -> List[SchemeType]:
        """Get the existing schemes."""
        return self.db.query(SchemeType).filter(SchemeType.type.in_(scheme_types)).all()

    def _get_categories_mapping(
        self, row: DataFrame, scheme_type_mapping: Dict[str, SchemeType]
    ) -> Dict[str, Any]:
        """Get the categories mapping."""
        scheme_type = row["SCHEME_TYPE"]
        category_name = row["CATEGORY_NAME"]
        category_code = row["CATEGORY_CODE"]

        scheme_type = scheme_type_mapping.get(scheme_type)
        if scheme_type is None:
            scheme_type = self._create_new_scheme_type(scheme_type)
            scheme_type_mapping[scheme_type] = scheme_type

        return AttributeMapper(
            code=category_code, name=category_name, scheme_type_id=scheme_type.id
        ).__dict__

    def _create_new_scheme_type(self, scheme_type: str) -> SchemeType:
        """Create a new scheme type."""
        scheme_type = SchemeType(type=scheme_type)
        self.db.add(scheme_type)
        self.db.commit()
        return scheme_type
