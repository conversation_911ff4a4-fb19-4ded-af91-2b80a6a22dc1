from utils.generator import ErrorGenerator
from ..errors.sub_category import (
    SubCategoryAlreadyExistsError,
    SubCategoryNotFoundError,
    SubCategoryCodeAlreadyExistsError,
    SubCategoryNameAlreadyExistsError,
)


class SubCategoryExceptions:
    """A class that generates sub-category-master-related error messages."""

    @staticmethod
    def generate_sub_category_not_found_error(
        sub_category_id: int = None,
        sub_category_code: str = None,
        sub_category_name: str = None,
    ):
        """
        Generates an error message for a sub-category-master not found error.

        Args:
            sub_category_code (str): The code of the sub-category.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=SubCategoryNotFoundError.TYPE,
            loc=SubCategoryNotFoundError.LOC,
            msg=SubCategoryNotFoundError.MSG,
            input=f"{sub_category_code if sub_category_code else sub_category_id if sub_category_id else sub_category_name}",
        )

    @staticmethod
    def generate_sub_category_already_exists_error(
        sub_category_id: int = None,
        sub_category_code: str = None,
        sub_category_name: str = None,
    ):
        """
        Generates an error message for a sub-category-master already exists error.

        Args:
            sub_category_code (str): The code of the sub-category.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=SubCategoryAlreadyExistsError.TYPE,
            loc=SubCategoryAlreadyExistsError.LOC,
            msg=SubCategoryAlreadyExistsError.MSG,
            input=f"{sub_category_code if sub_category_code else sub_category_id if sub_category_id else sub_category_name}",
        )

    @staticmethod
    def generate_sub_category_code_already_exists_error(sub_category_code: str):
        """
        Generates an error message for a sub-category-master code already exists error.

        Args:
            sub_category_code (str): The code of the sub-category.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=SubCategoryCodeAlreadyExistsError.TYPE,
            loc=SubCategoryCodeAlreadyExistsError.LOC,
            msg=SubCategoryCodeAlreadyExistsError.MSG,
            input=sub_category_code,
        )

    @staticmethod
    def generate_sub_category_name_already_exists_error(sub_category_name: str):
        """
        Generates an error message for a sub-category-master name already exists error.

        Args:
            sub_category_name (str): The name of the sub-category.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=SubCategoryNameAlreadyExistsError.TYPE,
            loc=SubCategoryNameAlreadyExistsError.LOC,
            msg=SubCategoryNameAlreadyExistsError.MSG,
            input=sub_category_name,
        )
