from utils.generator import ErrorGenerator

from ..errors.document_master import (
    DocumentMasterNotFoundError,
    DocumentMasterShouldBelongToYojanaError,
    DocumentMasterAlreadyExistsError,
)


class DocumentMasterExceptions:
    """A class that generates document-master-related error messages."""

    @staticmethod
    def generate_document_master_not_found_error(
        document_id: int = None, document_name: str = None
    ):
        """
        Generates an error message for a document-master not found error.

        Args:
            document_id (int): The ID of the document.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=DocumentMasterNotFoundError.TYPE,
            loc=DocumentMasterNotFoundError.LOC,
            msg=DocumentMasterNotFoundError.MSG,
            input=f"{document_id or document_name}",
        )

    @staticmethod
    def generate_document_master_should_belong_to_yojana_error(
        is_mjp_jay: bool, is_ab_pmjay: bool
    ):
        """
        Generates an error message for a document-master should belong to yojana error.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=DocumentMasterShouldBelongToYojanaError.TYPE,
            loc=DocumentMasterShouldBelongToYojanaError.LOC,
            msg=DocumentMasterShouldBelongToYojanaError.MSG,
            input=f"is_mjp_jay: {is_mjp_jay}, is_ab_pmjay: {is_ab_pmjay}",
        )

    @staticmethod
    def generate_document_master_already_exists_error(document_id: int):
        """
        Generates an error message for a document-master already exists error.

        Args:
            document_id (int): The ID of the document.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=DocumentMasterAlreadyExistsError.TYPE,
            loc=DocumentMasterAlreadyExistsError.LOC,
            msg=DocumentMasterAlreadyExistsError.MSG,
            input=f"{document_id}",
        )
