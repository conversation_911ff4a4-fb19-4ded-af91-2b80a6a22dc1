"""
This module contains classes for generating step master-related error messages.
"""

from utils.generator import <PERSON>rrorGenerator
from ..errors.scheme_type import SchemeTypeNotFountError


class SchemeTypeExceptions:
    """A class that generates scheme type-related error messages."""

    @staticmethod
    def generate_scheme_type_not_found_error(scheme_type_id: str):
        """
        Generates an error message for a scheme type not found error.

        Args:
            scheme_type_id (str): The type of the scheme.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=SchemeTypeNotFountError.TYPE,
            loc=SchemeTypeNotFountError.LOC,
            msg=SchemeTypeNotFountError.MSG,
            input=f"{scheme_type_id}",
        )

