"""
This module contains classes for generating step master-related error messages.
"""

from utils.generator import <PERSON>rror<PERSON>enerator
from ..errors.step_master import StepMasterNotFoundError, StepTypeNotValidError


class StepMasterExceptions:
    """A class that generates step master-related error messages."""

    @staticmethod
    def generate_step_master_not_found_error(step_id: int):
        """
        Generates an error message for a step master not found error.

        Args:
            step_id (int): The ID of the step.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=StepMasterNotFoundError.TYPE,
            loc=StepMasterNotFoundError.LOC,
            msg=StepMasterNotFoundError.MSG,
            input=f"{step_id}",
        )

    @staticmethod
    def generate_step_type_not_valid_error(step_type: str):
        """
        Generates an error message for a step type not valid error.

        Args:
            step_type (str): The type of the step.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=StepTypeNotValidError.TYPE,
            loc=StepTypeNotValidError.LOC,
            msg=StepTypeNotValidError.MSG,
            input=f"{step_type}",
        )
