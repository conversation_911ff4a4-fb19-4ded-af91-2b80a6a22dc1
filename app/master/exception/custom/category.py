from utils.generator import ErrorGenerator
from ..errors.category import (
    CategoryAlreadyExistsError,
    CategoryNotFoundError,
    CategoryCodeAlreadyExistsError,
    CategoryNameAlreadyExistsError,
)


class CategoryExceptions:
    """A class that generates category-master-related error messages."""

    @staticmethod
    def generate_category_not_found_error(
        category_id: int = None, category_code: str = None, category_name: str = None
    ):
        """
        Generates an error message for a category-master not found error.

        Args:
            category_code (str): The code of the category.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=CategoryNotFoundError.TYPE,
            loc=CategoryNotFoundError.LOC,
            msg=CategoryNotFoundError.MSG,
            input=f"{category_code if category_code else category_id if category_id else category_name}",
        )

    @staticmethod
    def generate_category_already_exists_error(
        category_id: int = None, category_code: str = None, category_name: str = None
    ):
        """
        Generates an error message for a category-master already exists error.

        Args:
            category_code (str): The code of the category.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=CategoryAlreadyExistsError.TYPE,
            loc=CategoryAlreadyExistsError.LOC,
            msg=CategoryAlreadyExistsError.MSG,
            input=f"{category_code if category_code else category_id if category_id else category_name}",
        )

    @staticmethod
    def generate_category_code_already_exists_error(category_code: str):
        """
        Generates an error message for a category-master code already exists error.

        Args:
            category_code (str): The code of the category.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=CategoryCodeAlreadyExistsError.TYPE,
            loc=CategoryCodeAlreadyExistsError.LOC,
            msg=CategoryCodeAlreadyExistsError.MSG,
            input=category_code,
        )

    @staticmethod
    def generate_category_name_already_exists_error(category_name: str):
        """
        Generates an error message for a category-master name already exists error.

        Args:
            category_name (str): The name of the category.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=CategoryNameAlreadyExistsError.TYPE,
            loc=CategoryNameAlreadyExistsError.LOC,
            msg=CategoryNameAlreadyExistsError.MSG,
            input=category_name,
        )
