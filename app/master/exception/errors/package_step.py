"""
This module contains custom exception classes for handling errors in the Arogya Yojana service.
"""

from utils.exception import CustomException, CustomExceptionDetailsBase


class PackageStepNotFoundError:
    """Exception raised when the package step is not found."""

    TYPE = "package_step_not_found"
    LOC = ["path", "package_step"]
    MSG = "package step not found"


class PackageStepAlreadyExistsError:
    """Exception raised when the package step already exists."""

    TYPE = "package_step_already_exists"
    LOC = ["path", "package_step"]
    MSG = "package step already exists"


class PackageStepAssociatedWithDocumentsError:
    """Exception raised when the package step already exists."""

    TYPE = "package_step_associated_with_documents"
    LOC = ["body", "step"]
    MSG = "package step associated with documents"

class PackageStepNotExistsValueError(ValueError):
    """Exception raised when the package step not exists."""

    def __init__(self):
        self.msg = "package step not exists"
        super().__init__(self.msg)


class PackageStepErrors(CustomExceptionDetailsBase):
    """Class to handle package step errors."""

    def raise_exception(self):
        """Raise a CustomException if there are error details."""
        if self.details:
            raise CustomException(self.details)

    def raise_package_step_not_found_exception(self, package_step_id):
        """Raise a PackageStepNotFoundError exception."""
        self.add_detail(
            PackageStepNotFoundError.TYPE,
            PackageStepNotFoundError.LOC,
            PackageStepNotFoundError.MSG,
            package_step_id,
        )
        self.raise_exception()

    def raise_package_step_already_exists_exception(self, package_step):
        """Raise a PackageStepAlreadyExistsError exception."""
        self.add_detail(
            PackageStepAlreadyExistsError.TYPE,
            PackageStepAlreadyExistsError.LOC,
            PackageStepAlreadyExistsError.MSG,
            package_step,
        )
        self.raise_exception()
    
    def raise_package_step_associated_with_documents_exception(self, package_step):
        """Raise a PackageStepAssociatedWithDocumentsError exception."""
        self.add_detail(
            PackageStepAssociatedWithDocumentsError.TYPE,
            PackageStepAssociatedWithDocumentsError.LOC,
            PackageStepAssociatedWithDocumentsError.MSG,
            package_step,
        )
        self.raise_exception()
    
