"""
This module contains custom exception classes for handling errors in the Arogya Yojana service.
"""

from utils.exception import CustomEx<PERSON>, CustomExceptionDetailsBase


class PackageStepDocumentNotFoundError:
    """Exception raised when the package step is not found."""

    TYPE = "package_step_document_not_found"
    LOC = ["path", "package_step_document"]
    MSG = "package step not found"


class PackageStepDocumentAlreadyExistsError:
    """Exception raised when the package step already exists."""

    TYPE = "package_step_document_already_exists"
    LOC = ["body", "package_step_document"]
    MSG = "package step already exists"


class PackageStepDocumentErrors(CustomExceptionDetailsBase):
    """Class to handle package step errors."""

    def raise_exception(self):
        """Raise a CustomException if there are error details."""
        if self.details:
            raise CustomException(self.details)

    def raise_package_step_document_not_found_exception(self, package_step_document_id):
        """Raise a PackageStepDocumentNotFoundError exception."""
        self.add_detail(
            PackageStepDocumentNotFoundError.TYPE,
            PackageStepDocumentNotFoundError.LOC,
            PackageStepDocumentNotFoundError.MSG,
            package_step_document_id,
        )
        self.raise_exception()

    def raise_package_step_document_already_exists_exception(
        self, package_step_document
    ):
        """Raise a PackageStepDocumentAlreadyExistsError exception."""
        self.add_detail(
            PackageStepDocumentAlreadyExistsError.TYPE,
            PackageStepDocumentAlreadyExistsError.LOC,
            PackageStepDocumentAlreadyExistsError.MSG,
            package_step_document,
        )
        self.raise_exception()
