from utils.exception import CustomException, CustomExceptionDetailsBase,CustomRequestValidationException


class DocumentMasterNotFoundError:
    """
    Exception raised when a document name is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "document_master_not_found"
    LOC = ["path", "document_id"]
    MSG = "Document name not found"


class DocumentMasterAlreadyExistsError:
    """
    Exception raised when a document name already exists.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "document_master_already_exists"
    LOC = ["path", "document_id"]
    MSG = "Document name already exists"


class DocumentMasterShouldBelongToYojanaError:
    """
    Exception raised when a document should belong to a yojana.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "document_master_should_belong_to_yojana"
    LOC = ["body", "is_mjp_jay", "is_ab_pmjay"]
    MSG = "Document should belong to one of the yojana"


class DocumentValueAlreadyExistsError(ValueError):
    """Exception raised when a document name already exists."""

    def __init__(self):
        self.msg = "Document name already exists"
        super().__init__(self.msg)


class DocumentNotExistsValueError(ValueError):
    """Exception raised when the document not exists."""

    def __init__(self, document_ids: list):
        self.msg = f"Document with ids {document_ids} does not exist"
        super().__init__(self.msg)


class DocumentNotExistsWithIds:

    TYPE = "document_not_exists_with_given_ids"
    LOC = ["body", "document_ids"]
    MSG = f"Document with ids does not exist."


class DocumentHasPackageError:
    TYPE = "document_has_package"
    LOC = ["path", "document_id"]
    MSG = f"Cannot delete document as it is associated with package."

class DocumentHasCaseError:
    TYPE = "document_has_case"
    LOC = ["path", "document_id"]
    MSG = f"Cannot delete document as it is associated with case."


class DocumentMasterErrors(CustomRequestValidationException):
    """Class to handle category errors."""

    def raise_document_not_exists_exception(self, document_id):
        """Raise a CategoryNotFoundError exception."""
        self.add_validation_error(
            loc=DocumentMasterNotFoundError.LOC,
            msg=DocumentMasterNotFoundError.MSG,
            input_value=document_id,
        )
        self.raise_validation_exception()
        
    def raise_document_master_has_packages_error(self, document_id):
        self.add_validation_error(
            loc=DocumentHasPackageError.LOC,
            msg=DocumentHasPackageError.MSG,
            input_value=document_id
        )
        self.raise_validation_exception()
        
    def raise_document_master_has_associated_cases_error(self, document_id):
        self.add_validation_error(
            loc=DocumentHasCaseError.LOC,
            msg=DocumentHasCaseError.MSG,
            input_value=document_id
        )
        self.raise_validation_exception()
        
    
    def raise_document_master_should_belong_to_yojana_error(self):
        self.add_validation_error(
            loc=DocumentMasterShouldBelongToYojanaError.LOC,
            msg=DocumentMasterShouldBelongToYojanaError.MSG,
        )
        self.raise_validation_exception()
        
    def raise_document_not_exists_with_ids_exception(self, document_ids: list):
        """Raise a CategoryNotFoundError exception."""
        self.add_validation_error(
            loc=DocumentNotExistsWithIds.LOC,
            msg=DocumentNotExistsWithIds.MSG,
            input_value=document_ids,
        )
        self.raise_validation_exception()