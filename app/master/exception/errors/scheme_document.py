"""
This module contains custom exception classes for handling errors in the Arogya Yojana service.
"""

from utils.exception import CustomException, CustomExceptionDetailsBase


class SchemeDocumentNotFoundError:
    """
    Exception raised when a scheme document is not found.
    """

    TYPE = "scheme_document_not_found"
    LOC = ["path", "scheme_document_id"]
    MSG = "Scheme document not found"


class SchemaDocumentAlreadyExistsError:
    """
    Exception raised when a scheme document already exists.
    """

    TYPE = "scheme_document_already_exists"
    LOC = ["path", "scheme_document_id"]
    MSG = "Scheme document already exists"


class SchemeDocumentAssociatedError:
    """
    Exception raised when a scheme document is associated with a scheme.

    """

    TYPE = "scheme_document_associated"
    LOC = ["path", "scheme_document_id"]
    MSG = "Scheme document is associated with a scheme"


class SchemeDocumentNotFoundWithSchemeIdError:
    """
    Exception raised when a scheme document is not found.
    """

    TYPE = "scheme_document_not_found"
    LOC = ["path", "scheme_id"]
    MSG = "Scheme document not found for scheme id"


class SchemeDocumentAlreadyExistsValueError(ValueError):
    """Exception raised when a scheme document already exists."""

    def __init__(self):
        self.msg = "Scheme document already exists"


class SchemeDocumentErrors(CustomExceptionDetailsBase):
    """Class to handle scheme document errors."""

    def raise_exception(self):
        """Raise a CustomException if there are error details."""
        if self.details:
            raise CustomException(self.details)

    def raise_scheme_document_not_found_with_scheme_id_exception(self, scheme_id):
        """Raise a SchemeDocumentNotFoundWithSchemeIdError exception."""
        self.add_detail(
            SchemeDocumentNotFoundWithSchemeIdError.TYPE,
            SchemeDocumentNotFoundWithSchemeIdError.LOC,
            SchemeDocumentNotFoundWithSchemeIdError.MSG,
            scheme_id,
        )
        self.raise_exception()

    def raise_scheme_document_not_found_exception(self, scheme_document_id):
        """Raise a SchemeDocumentNotFoundError exception."""
        self.add_detail(
            SchemeDocumentNotFoundError.TYPE,
            SchemeDocumentNotFoundError.LOC,
            SchemeDocumentNotFoundError.MSG,
            scheme_document_id,
        )
        self.raise_exception()

    def raise_scheme_document_already_exists_exception(self, scheme_document_id):
        """Raise a SchemaDocumentAlreadyExistsError exception."""
        self.add_detail(
            SchemaDocumentAlreadyExistsError.TYPE,
            SchemaDocumentAlreadyExistsError.LOC,
            SchemaDocumentAlreadyExistsError.MSG,
            scheme_document_id,
        )
        self.raise_exception()

    def raise_scheme_document_associated_exception(self, scheme_document_id):
        """Raise a SchemeDocumentAssociatedError exception."""
        self.add_detail(
            SchemeDocumentAssociatedError.TYPE,
            SchemeDocumentAssociatedError.LOC,
            SchemeDocumentAssociatedError.MSG,
            scheme_document_id,
        )
        self.raise_exception()
