"""
This module contains custom exception classes for handling errors in the Arogya Yojana service.
"""

from utils.exception import CustomRequestValidationException


class SubCategoryNotFoundError:
    """
    Exception raised when the sub category is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "sub_category_not_found"
    LOC = ["path", "sub_category_id"]
    MSG = "sub category not found"


class SubCategoryNotFoundErrorValueError(ValueError):
    """Exception raised when the sub category is not found."""

    def __init__(self):
        self.msg = "sub category not found"
        super().__init__(self.msg)


class SubCategoryAlreadyExistsError:
    """Exception raised when the sub category already exists."""

    TYPE = "sub_category_already_exists"
    LOC = ["body", "sub_category"]
    MSG = "Sub-category already exists with this data."


class SubCategoryNameAlreadyExistsError:
    """Exception raised when the sub category name already exists."""

    TYPE = "sub_category_name_already_exists"
    LOC = ["body", "name"]
    MSG = "Sub-category name already exists."


class SubCategoryCodeAlreadyExistsError:
    """Exception raised when the sub category code already exists."""

    TYPE = "sub_category_code_already_exists"
    LOC = ["path", "code"]
    MSG = "Sub-category code already exists."


class SubCategoryAssociatedWithSubCategoryError:
    """Exception raised when the sub category is associated with a package."""

    TYPE = "sub_category_associated_with_package"
    LOC = ["path", "sub_category_id"]
    MSG = "Sub-category is associated with a procedure."


class SubCategoryNameAlreadyExistsValueError(ValueError):
    """Exception raised when the sub category name already exists."""

    def __init__(self):
        self.msg = "Sub-category name already exists"
        super().__init__(self.msg)


class SubCategoryCodeAlreadyExistsValueError(ValueError):
    """Exception raised when the sub category code already exists."""

    def __init__(self):
        self.msg = "Sub-category code already exists"
        super().__init__(self.msg)


class SubCategoryErrors(CustomRequestValidationException):
    """Class to handle sub category errors."""

    def raise_sub_category_not_exists_exception(self, sub_category_id):
        """Raise a SubCategoryNotFoundError exception."""

        self.add_validation_error(
            loc=SubCategoryNotFoundError.LOC,
            msg=SubCategoryNotFoundError.MSG,
            error_type=SubCategoryNotFoundError.TYPE,
            input_value=sub_category_id,
        )
        raise self.raise_validation_exception()

    def raise_sub_category_already_exists_exception(self, sub_category,msg=None):
        """Raise a SubCategoryAlreadyExistsError exception."""
        self.add_validation_error(
            loc=SubCategoryAlreadyExistsError.LOC,
            msg=msg if msg else SubCategoryAlreadyExistsError.MSG,
            error_type=SubCategoryAlreadyExistsError.TYPE,
            input_value=sub_category,
        )

        raise self.raise_validation_exception()

    def raise_sub_category_name_already_exists_exception(self, sub_category_name):
        """Raise a SubCategoryNameAlreadyExistsError exception."""
        self.add_validation_error(
            loc=SubCategoryNameAlreadyExistsError.LOC,
            msg=SubCategoryNameAlreadyExistsError.MSG,
            error_type=SubCategoryNameAlreadyExistsError.TYPE,
            input_value=sub_category_name,
        )

        self.raise_validation_exception()

    def raise_sub_category_code_already_exists_exception(self, sub_category_code):
        """Raise a SubCategoryCodeAlreadyExistsError exception."""

        self.add_validation_error(
            loc=SubCategoryCodeAlreadyExistsError.LOC,
            msg=SubCategoryCodeAlreadyExistsError.MSG,
            error_type=SubCategoryCodeAlreadyExistsError.TYPE,
            input_value=sub_category_code,
        )

        self.raise_validation_exception()

    def raise_sub_category_associated_with_package_exception(self, sub_category_id):
        """Raise a SubCategoryAssociatedWithPackageError exception."""

        self.add_validation_error(
            loc=SubCategoryAssociatedWithSubCategoryError.LOC,
            msg=SubCategoryAssociatedWithSubCategoryError.MSG,
            error_type=SubCategoryAssociatedWithSubCategoryError.TYPE,
            input_value=sub_category_id,
        )

        self.raise_validation_exception()
