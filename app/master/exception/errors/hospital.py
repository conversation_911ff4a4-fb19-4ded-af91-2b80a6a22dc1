"""
This module contains custom exception classes for handling errors in the Arogya Yojana service.
"""

from utils.exception import (
    CustomException,
    CustomExceptionDetailsBase,
    CustomRequestValidationException,
)


class HospitalNotFoundError:
    """
    Exception raised when the hospital is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "hospital_not_found"
    LOC = ["path", "hospital"]
    MSG = "Hospital not found"


class HospitalAlreadyExistsError:
    """
    Exception raised when the hospital already exists.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "hospital_already_exists"
    LOC = ["body", "hospital"]
    MSG = "Hospital already exists"


class HospitalNameAlreadyExistsError:
    """
    Exception raised when the hospital name already exists.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "hospital_name_already_exists"
    LOC = ["body", "name"]
    MSG = "Hospital name already exists"


class HospitalShortNameAlreadyExistsError:
    """
    Exception raised when the hospital name already exists.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "hospital_short_name_already_exists"
    LOC = ["body", "short_name"]
    MSG = "Hospital short name already exists."


class HospitalNameCannotBeUpdatedError:
    """
    Exception raised when the hospital name cannot be updated.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "hospital_name_cannot_be_updated"
    LOC = ["body", "hospital"]
    MSG = "Hospital name cannot be updated"


class HospitalHasUsersError:
    """
    Exception raised when the hospital has users.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "hospital_has_users"
    LOC = ["body", "hospital"]
    MSG = "Cannot delete hospital as it is assigned to users."


class HospitalHasSchemesError:
    """
    Exception raised when the hospital has schemes.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "hospital_has_schemes"
    LOC = ["body", "hospital"]
    MSG = "Cannot delete hospital as it is assigned to schemes."


class HospitalHasCasesError:
    """
    Exception raised when the hospital has cases.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "hospital_has_cases"
    LOC = ["body", "hospital"]
    MSG = "Cannot delete hospital as it is assigned to cases."


class HospitalAndSchemeAssociatedWithCasesError:
    """
    Exception raised when the hospital and scheme are associated with cases.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "hospital_and_scheme_associated_with_cases"
    LOC = ["body", "schemes"]
    MSG = f"Cannot remove scheme there are cases associated with it."


class HospitalsNotFoundValueError(ValueError):
    """Exception raised when the hospitals is not found."""

    def __init__(self, hospital_ids: list):
        """
        Initialize the HospitalNotFoundValueError.
        """
        msg = f"Hospitals with ID's {hospital_ids} not found"
        super().__init__(msg)


class HospitalErrors(CustomRequestValidationException):
    """Class to handle hospital errors."""

    def raise_hospital_not_exists_exception(self, hospital_id):
        """Raise a HospitalNotFoundError exception."""

        self.add_validation_error(
            loc=HospitalNotFoundError.LOC,
            msg=HospitalNotFoundError.MSG,
            error_type=HospitalNotFoundError.TYPE,
            input_value=hospital_id,
        )
        self.raise_validation_exception()

    def raise_hospital_already_exists_exception(self, hospital):
        """Raise a HospitalAlreadyExistsError exception."""
        self.add_validation_error(
            loc=HospitalAlreadyExistsError.LOC,
            msg=HospitalAlreadyExistsError.MSG,
            error_type=HospitalAlreadyExistsError.TYPE,
            input_value=hospital)
        
        self.raise_validation_exception()

    def raise_hospital_name_already_exists_exception(self, hospital_name):
        """Raise a HospitalNameAlreadyExistsError exception."""
        self.add_validation_error(
            loc=HospitalNameAlreadyExistsError.LOC,
            msg=HospitalNameAlreadyExistsError.MSG,
            error_type=HospitalNameAlreadyExistsError.TYPE,
            input_value=hospital_name,
        )
        
        self.raise_validation_exception()

    def raise_name_can_not_be_updated_exception(self, hospital_name):
        """Raise a HospitalNameCannotBeUpdatedError exception."""
        self.add_validation_error(
            loc=HospitalNameCannotBeUpdatedError.LOC,
            msg=HospitalNameCannotBeUpdatedError.MSG,
            error_type=HospitalNameCannotBeUpdatedError.TYPE,
            input_value=hospital_name,
        )
        
        self.raise_validation_exception()

    def raise_hospital_has_users_exception(self, hospital_id):
        """Raise a HospitalHasUsersError exception."""
        
        
        self.add_validation_error(
            loc=HospitalHasUsersError.LOC,
            msg=HospitalHasUsersError.MSG,
            error_type=HospitalHasUsersError.TYPE,
            input_value=hospital_id,
        )
        
        self.raise_validation_exception()

    def raise_hospital_has_schemes_exception(self, hospital_id):
        """Raise a HospitalHasSchemesError exception."""

        
        self.add_validation_error(
            loc=HospitalHasSchemesError.LOC,
            msg=HospitalHasSchemesError.MSG,
            error_type=HospitalHasSchemesError.TYPE,
            input_value=hospital_id,
        )
        
        self.raise_validation_exception()
        
        
    def raise_hospital_has_cases_exception(self, hospital_id):
        """Raise a HospitalHasCasesError exception."""
        
        self.add_validation_error(
            loc=HospitalHasCasesError.LOC,
            msg=HospitalHasCasesError.MSG,
            error_type=HospitalHasCasesError.TYPE,
            input_value=hospital_id,
        )
        
        self.raise_validation_exception()

    def raise_hospital_short_name_already_exists_exception(self, short_name):
        """Raise a HospitalNameAlreadyExistsError exception."""
        self.add_validation_error(
            loc=HospitalShortNameAlreadyExistsError.LOC,
            msg=HospitalShortNameAlreadyExistsError.MSG,
            error_type=HospitalShortNameAlreadyExistsError.TYPE,
            input_value=short_name,
        )
        
        self.raise_validation_exception()

    def raise_hospital_and_scheme_associated_with_cases_exception(self, schemes):
        """Raise a HospitalAndSchemeAssociatedWithCasesError exception."""
        
        self.add_validation_error(
            loc=HospitalAndSchemeAssociatedWithCasesError.LOC,
            msg=HospitalAndSchemeAssociatedWithCasesError.MSG,
            error_type=HospitalAndSchemeAssociatedWithCasesError.TYPE,
            input_value=schemes,
        )
        
        self.raise_validation_exception()
