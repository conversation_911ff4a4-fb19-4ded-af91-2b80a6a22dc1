"""
This module contains custom exception classes for handling errors in the Arogya Yojana service.
"""

from utils.exception import CustomExceptionDetailsBase, CustomRequestValidationException


class PackageNotFoundError:
    """
    Exception raised when the package is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "package_not_found"
    LOC = ["path", "package"]
    MSG = "package not found"


class PackageAlreadyExistsError:
    """Exception raised when the package already exists."""

    TYPE = "package_already_exists"
    LOC = ["body", "package"]
    MSG = "package already exists with this data"


class ProcedureNameAlreadyExistsError:
    """Exception raised when the procedure name already exists."""

    TYPE = "procedure_name_already_exists"
    LOC = ["body", "procedure_name"]
    MSG = "procedure name already exists"


class ProcedureCodeAlreadyExistsError:
    """Exception raised when the procedure code already exists."""

    TYPE = "procedure_code_already_exists"
    LOC = ["body", "procedure_code"]
    MSG = "procedure code already exists"


class PackageHasStepsError:
    """Exception raised when the package has steps."""

    TYPE = "package_has_steps"
    LOC = ["body", "package"]
    MSG = "Cannot delete package as it has steps."


class PackageHasCasesError:
    """Exception raised when the package has cases."""

    TYPE = "package_has_cases"
    LOC = ["body", "package"]
    MSG = "Cannot delete package as it has cases."


class MissingColumnsError:
    """Exception raised when required Excel columns are missing."""
    TYPE = "missing_columns_error"
    LOC = ["body", "excel_columns"]
    MSG = "Required columns are missing in the Excel file"


class ProcedureCodeAlreadyExistsValueError(ValueError):
    """Exception raised when the procedure code already exists."""

    def __init__(self):
        self.msg = "procedure code already exists"
        super().__init__(self.msg)


class ProcedureNameAlreadyExistsValueError(ValueError):
    """Exception raised when the procedure name already exists."""

    def __init__(self):
        self.msg = "procedure name already exists"
        super().__init__(self.msg)


class PackageNotExistsValueError(ValueError):
    """Exception raised when the package not exists."""

    def __init__(self):
        self.msg = "package not exists"
        super().__init__(self.msg)


class PackageErrors(CustomRequestValidationException):
    """Class to handle package errors."""

    def raise_package_not_found_exception(self, package_id):
        """Raise a PackageNotFoundError exception."""

        self.add_validation_error(
            loc=PackageNotFoundError.LOC,
            msg=PackageNotFoundError.MSG,
            input_value=package_id,
            error_type=PackageNotFoundError.TYPE,
        )

        self.raise_validation_exception()

    def raise_package_already_exists_exception(self, package, msg=None):
        """Raise a PackageAlreadyExistsError exception."""

        self.add_validation_error(
            loc=PackageAlreadyExistsError.LOC,
            msg=msg if msg else PackageAlreadyExistsError.MSG,
            input_value=package,
            error_type=PackageAlreadyExistsError.TYPE,
        )

        self.raise_validation_exception()

    def raise_procedure_name_already_exists_exception(self, procedure_name, msg=None):
        """Raise a ProcedureNameAlreadyExistsError exception."""

        self.add_validation_error(
            loc=ProcedureNameAlreadyExistsError.LOC,
            msg=msg if msg else ProcedureNameAlreadyExistsError.MSG,
            input_value=procedure_name,
            error_type=ProcedureNameAlreadyExistsError.TYPE,
        )

        self.raise_validation_exception()

    def raise_procedure_code_already_exists_exception(self, procedure_code, msg=None):
        """Raise a ProcedureCodeAlreadyExistsError exception."""

        self.add_validation_error(
            loc=ProcedureCodeAlreadyExistsError.LOC,
            msg=msg if msg else ProcedureCodeAlreadyExistsError.MSG,
            input_value=procedure_code,
            error_type=ProcedureCodeAlreadyExistsError.TYPE,
        )

        self.raise_validation_exception()

    def raise_package_has_steps_exception(self, package_id):
        """Raise a ProcedureCodeAlreadyExistsError exception."""

        self.add_validation_error(
            loc=PackageHasStepsError.LOC,
            msg=PackageHasStepsError.MSG,
            input_value=package_id,
            error_type=PackageHasStepsError.TYPE,
        )

        self.raise_validation_exception()

    def raise_package_has_cases_exception(self, package_id):
        """Raise a ProcedureCodeAlreadyExistsError exception."""

        self.add_validation_error(
            loc=PackageHasCasesError.LOC,
            msg=PackageHasCasesError.MSG,
            input_value=package_id,
            error_type=PackageHasCasesError.TYPE,
        )

        self.raise_validation_exception()

    def raise_missing_columns_exception(self, missing_columns):
        """Raise an exception for specific missing columns."""
        columns_str = ", ".join(missing_columns)
        error_msg = f"Required columns missing in Excel file: {columns_str}"
        
        self.add_validation_error(
            loc=MissingColumnsError.LOC,
            msg=error_msg,
            input_value=missing_columns,
            error_type=MissingColumnsError.TYPE,
        )
        
        self.raise_validation_exception()