"""
This module contains custom exception classes for handling errors in the Arogya Yojana service.
"""

from app.master.models.package import StepType

from utils.exception import CustomException, CustomExceptionDetailsBase


class StepMasterNotFoundError:
    """
    Exception raised when a step master is not found.
    """

    TYPE = "step_master_not_found"
    LOC = ["path", "step_id"]
    MSG = "Step master not found"


class StepTypeNotValidError:
    """
    Exception raised when the step type is not valid.
    """

    TYPE = "step_type_not_valid"
    LOC = ["step_type"]
    MSG = f"Step type not valid, expected one of {[StepType.PRE_AUTHORIZATION, StepType.TREATMENT,StepType.DISCHARGE, StepType.CLAIM]}"


class SchemeTypeNotFountError:
    """
    Exception raised when the scheme type is not found.
    """

    TYPE = "scheme_type_not_found"
    LOC = ["path", "scheme_type"]
    MSG = "Scheme type not found"


class StepOrderAlreadyExistsValueError(ValueError):
    """Exception raised when the step order already exists."""

    def __init__(self):
        self.msg = "step order already exists"
        super().__init__(self.msg)


class StepNotExistsValueError(ValueError):
    """Exception raised when the step not exists."""

    def __init__(self):
        self.msg = "step not exists"
        super().__init__(self.msg)


class StepMasterErrors(CustomExceptionDetailsBase):
    """
    Class to handle step master errors.
    """

    def raise_exception(self):
        """
        Raise a CustomException if there are error details.
        """
        if self.details:
            raise CustomException(self.details)

    def raise_step_master_not_found_exception(self, step_id=None, type=None):
        """
        Raise a StepMasterNotFoundError exception.
        """
        self.add_detail(
            StepMasterNotFoundError.TYPE,
            StepMasterNotFoundError.LOC,
            StepMasterNotFoundError.MSG,
            step_id if step_id else type,
        )
        self.raise_exception()

    def raise_step_type_not_valid_exception(self, step_type):
        """
        Raise a StepTypeNotValidError exception.
        """
        self.add_detail(
            StepTypeNotValidError.TYPE,
            StepTypeNotValidError.LOC,
            StepTypeNotValidError.MSG,
            step_type,
        )
        self.raise_exception()

    def raise_scheme_type_not_found_exception(self, scheme_type):
        """
        Raise a SchemeTypeNotFountError exception.
        """
        self.add_detail(
            SchemeTypeNotFountError.TYPE,
            SchemeTypeNotFountError.LOC,
            SchemeTypeNotFountError.MSG,
            scheme_type,
        )
        self.raise_exception()
