"""
This module contains custom exception classes for handling errors in the Arogya Yojana service.
"""

from utils.exception import (
    CustomException,
    CustomExceptionDetailsBase,
    CustomRequestValidationException,
)


class CategoryNotFoundError:
    """
    Exception raised when the category is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "category_not_found"
    LOC = ["path", "category"]
    MSG = "category not found"


class CategoryAlreadyExistsError:
    """
    Exception raised when the category already exists.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "category_already_exists"
    LOC = ["path", "category"]
    MSG = " category already exists"


class CategoryNameAlreadyExistsError:
    """
    Exception raised when the category name already exists.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "category_name_already_exists"
    LOC = ["body", "name"]
    MSG = "category name already exists"


class CategoryCodeAlreadyExistsError:
    """Exception raised when the  category code already exists."""

    TYPE = "category_code_already_exists"
    LOC = ["body", "code"]
    MSG = " category code already exists"


class CategoryAssociatedWithSubCategoryError:
    """Exception raised when the category is associated with a sub category."""

    TYPE = "category_associated_with_sub_category"
    LOC = ["path", "category_id"]
    MSG = "category is associated with a sub category"


class CategoryAssociatedWithPackageError:
    """Exception raised when the category is associated with a package."""

    TYPE = "category_associated_with_package"
    LOC = ["path", "category_id"]
    MSG = "category is associated with a package"


class CategoryNotFoundValueError(ValueError):
    """Exception raised when the category is not found."""

    def __init__(self):
        self.msg = "category not found"
        super().__init__(self.msg)


class CategoryErrors(CustomRequestValidationException):
    """Class to handle category errors."""

    def raise_category_not_exists_exception(self, category):
        """Raise a CategoryNotFoundError exception."""
        self.add_validation_error(
            error_type=CategoryNotFoundError.TYPE,
            loc=CategoryNotFoundError.LOC,
            msg=CategoryNotFoundError.MSG,
            input_value=category,
        )
        self.raise_validation_exception()

    def raise_category_already_exists_exception(self, category,msg=None):
        """Raise a CategoryAlreadyExistsError exception."""
        self.add_validation_error(
            error_type=CategoryAlreadyExistsError.TYPE,
            loc=CategoryAlreadyExistsError.LOC,
            msg=msg if msg else CategoryAlreadyExistsError.MSG,
            input_value=category,
        )
        self.raise_validation_exception()

    def raise_category_name_already_exists_exception(self, category_name):
        """Raise a CategoryNameAlreadyExistsError exception."""
        self.add_validation_error(
            error_type=CategoryNameAlreadyExistsError.TYPE,
            loc=CategoryNameAlreadyExistsError.LOC,
            msg=CategoryNameAlreadyExistsError.MSG,
            input_value=category_name,
        )
        self.raise_validation_exception()

    def raise_category_code_already_exists_exception(self, category_code):
        """Raise a CategoryCodeAlreadyExistsError exception."""
        self.add_validation_error(
            error_type=CategoryCodeAlreadyExistsError.TYPE,
            loc=CategoryCodeAlreadyExistsError.LOC,
            msg=CategoryCodeAlreadyExistsError.MSG,
            input_value=category_code,
        )
        self.raise_validation_exception()

    def raise_category_associated_with_sub_category_exception(self, category_id):
        """Raise a CategoryAssociatedWithSubCategoryError exception."""
        self.add_validation_error(
            error_type=CategoryAssociatedWithSubCategoryError.TYPE,
            loc=CategoryAssociatedWithSubCategoryError.LOC,
            msg=CategoryAssociatedWithSubCategoryError.MSG,
            input_value=category_id,
        )
        self.raise_validation_exception()

    def raise_category_associated_with_package_exception(self, category_id):
        """Raise a CategoryAssociatedWithPackageError exception."""
        self.add_validation_error(
            error_type=CategoryAssociatedWithPackageError.TYPE,
            loc=CategoryAssociatedWithPackageError.LOC,
            msg=CategoryAssociatedWithPackageError.MSG,
            input_value=category_id,
        )
        self.raise_validation_exception()
