"""
This module contains custom exception classes for handling errors in the Arogya Yojana service.
"""

from utils.exception import CustomException, CustomExceptionDetailsBase


class SchemeHospitalNotFoundError:
    """
    Exception raised when a scheme hospital is not found.
    """

    TYPE = "scheme_hospital_not_found"
    LOC = ["path", "scheme_hospital_id"]
    MSG = "Scheme hospital not found"


class SchemeHospitalAlreadyExistsError:
    """
    Exception raised when a scheme hospital already exists.
    """

    TYPE = "scheme_hospital_already_exists"
    LOC = ["path", "scheme_hospital_id"]
    MSG = "Scheme hospital already exists"


class SchemeHospitalAssociatedError:
    """
    Exception raised when a scheme hospital is associated with a scheme.
    """

    TYPE = "scheme_hospital_associated"
    LOC = ["path", "scheme_hospital_id"]
    MSG = "Scheme hospital is associated with a scheme"


class SchemeHospitalErrors(CustomExceptionDetailsBase):
    """Class to handle scheme hospital errors."""

    def raise_scheme_hospital_not_found_exception(self, scheme_hospital_id):
        """Raise a SchemeHospitalNotFoundError exception."""
        self.add_detail(
            SchemeHospitalNotFoundError.TYPE,
            SchemeHospitalNotFoundError.LOC,
            SchemeHospitalNotFoundError.MSG,
            scheme_hospital_id,
        )
        self.raise_exception()

    def raise_scheme_hospital_already_exists_exception(self, scheme_hospital_id):
        """Raise a SchemeHospitalAlreadyExistsError exception."""
        self.add_detail(
            SchemeHospitalAlreadyExistsError.TYPE,
            SchemeHospitalAlreadyExistsError.LOC,
            SchemeHospitalAlreadyExistsError.MSG,
            scheme_hospital_id,
        )
        self.raise_exception()

    def raise_scheme_hospital_associated_exception(self, scheme_hospital_id):
        """Raise a SchemeHospitalAssociatedError exception."""
        self.add_detail(
            SchemeHospitalAssociatedError.TYPE,
            SchemeHospitalAssociatedError.LOC,
            SchemeHospitalAssociatedError.MSG,
            scheme_hospital_id,
        )
        self.raise_exception()


class SchemeHospitalAlreadyExistsValueError(ValueError):
    """Exception raised when a scheme document already exists."""

    def __init__(self):
        self.msg = "Scheme document already exists"
