"""
This module contains custom exception classes for handling errors in the Arogya Yojana service.
"""

from utils.exception import CustomException, CustomExceptionDetailsBase


class SchemeTypeNotFountError:
    """
    Exception raised when the scheme type is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "scheme_type_not_found"
    LOC = ["path", "scheme_type"]
    MSG = "Scheme type not found"




class SchemeTypeNotExistWithIdsValueError(ValueError):
    """Exception raised when the scheme type does not exist."""

    def __init__(self, scheme_ids):
        self.msg = f"Scheme type does not exist with ids: {scheme_ids}."
        super().__init__(self.msg)


class SchemeHasDocumentsError(ValueError):
    """Exception raised when the scheme has documents."""

    TYPE = "scheme_has_documents"
    LOC = ["path", "scheme_type_id"]
    MSG = "Scheme type has documents associated with it"


class SchemesErrors(CustomExceptionDetailsBase):
    """Class to handle scheme errors."""

    def raise_exception(self):
        """Raise a CustomException if there are error details."""
        if self.details:
            raise CustomException(self.details)

    def raise_scheme_type_not_found_exception(self, scheme_type):
        """Raise a SchemeTypeNotFoundError exception."""
        self.add_detail(
            SchemeTypeNotFountError.TYPE,
            SchemeTypeNotFountError.LOC,
            SchemeTypeNotFountError.MSG,
            scheme_type,
        )
        self.raise_exception()

    def raise_scheme_type_not_exists_exception(self):
        """Raise a SchemeTypeNotExistsValueError exception."""
        raise SchemeTypeNotExistsValueError()

    def raise_scheme_has_documents_exception(self, scheme_type_id):
        """Raise a SchemeHasDocumentsError exception."""
        self.add_detail(
            SchemeHasDocumentsError.TYPE,
            SchemeHasDocumentsError.LOC,
            SchemeHasDocumentsError.MSG,
            scheme_type_id,
        )
        self.raise_exception()
