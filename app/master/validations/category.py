from typing import Any, ClassVar, Dict, List, Optional

from pydantic import BaseModel, validator

from app.master.models.category import CategoryDB
from app.master.models.scheme_type import SchemeType
from app.validations.base import CustomBaseModel
from utils.response import BaseResponse


class SchemeTypeBase(BaseModel):
    """
    Represents a scheme_type_master in the database.
    """

    id: int
    type: str

    class Config:
        from_attributes = True


class CategoryBase(CustomBaseModel):
    """Base class for category master data."""

    code: str
    name: str

    # Define field constraints as class variables for reuse
    FIELD_CONSTRAINTS: ClassVar[Dict[str, Dict[str, Any]]] = {
        "code": {"min_value": 2, "max_value": 20, "is_required": True},
        "name": {"min_value": 2, "max_value": 300, "is_required": True},
    }

    @validator("code")
    def validate_code(cls, v):
        """Validate the category code format and length."""
        cls.check_value_range(field="code", value=v, **cls.FIELD_CONSTRAINTS["code"])
        return v

    @validator("name")
    def validate_name(cls, v):
        """Validate the category name format and length."""
        cls.check_value_range(field="name", value=v, **cls.FIELD_CONSTRAINTS["name"])
        
        return v


class CategoryCreate(CategoryBase):
    """Class for creating a category."""

    scheme_type_id: int

    @validator("scheme_type_id")
    def check_scheme_type_exists(cls, v):
        """Check if the scheme type exists."""
        cls.check_foreign_key_exists(
            verbose_name="SchemeType", field="id", value=v, model=SchemeType
        )
        return v

    @validator("code")
    def validate_code(cls, v):
        """Validate the category code format and length."""
        cls.check_alphanumeric(value=v, field_name="code")
        return v


    @validator("name")
    def validate_name(cls, v):
        """Validate the category name format and length."""
        cls.check_alphanumeric(value=v, field_name="name")
        return v


class CategoryUpdate(CategoryBase):
    """Class for updating a category."""

    scheme_type_id: int

    @validator("scheme_type_id")
    def check_scheme_type_exists(cls, v):
        """Check if the scheme type exists."""
        cls.check_foreign_key_exists(
            verbose_name="SchemeType", field="id", value=v, model=SchemeType
        )
        return v

    # No need to redefine code and name validators - they're inherited from CategoryBase


class CategoryBaseReadResponse(CategoryBase):
    """Base class for category master response."""

    id: int
    # scheme_type_id: int
    scheme_type: SchemeTypeBase


class CategoryRetrievedResponse(BaseResponse):
    """
    Represents a category_master response schema.
    """

    data: Optional[CategoryBaseReadResponse] = None


class CategoryListResponse(BaseResponse):
    """
    Represents a category_master response schema.
    """

    data: Optional[List[CategoryBaseReadResponse]] = None


class CategoryFilterPaginationResponse(BaseResponse):
    data: Optional[List[CategoryBaseReadResponse]] = None
    total_count: int = 0
