from typing import Optional, List
from pydantic import BaseModel, root_validator, validator

from app.master.exception.errors.document_master import (
    DocumentValueAlreadyExistsError,
)

from app.master.exception.errors.scheme_type import SchemeTypeNotExistWithIdsValueError
from app.master.services.document_master import (
    DocumentMasterService,
)

from app.master.services.scheme_type import SchemeTypeService

from app.master.validations.scheme_type import SchemeAssociateResponse
from utils.response import BaseResponse

MJP_JAY_SCHEME = "MJP-JAY"

AB_PMJAY_SCHEME = "AB-PM-JAY"
from app.validations.base import CustomBaseModel
from app.master.models.document_master import DocumentMaster as DocuementMasterDB


class DocumentMasterBase(CustomBaseModel):
    """
    Represents a document_master in the database.
    """

    name: str

    class Config:
        from_attributes = True


class DocumentMasterBaseWithSchemeValidation(DocumentMasterBase):
    """
    Represents a document_master base schema with scheme validation.
    """

    scheme_ids: List[int]

    @validator("scheme_ids")
    def scheme_id_must_exist(cls, scheme_ids):
        """Validates if the scheme_id exists."""
        
        cls.check_foreign_key_exists(
            verbose_name="Scheme Type", field="id", value=scheme_ids, model=DocuementMasterDB
        )

        return scheme_ids
        


class DocumentMasterCreate(DocumentMasterBaseWithSchemeValidation):
    """
    Represents a document_master creation schema.
    """

    name: str
    is_geotag_required: bool

    class Config:
        from_attributes = True

    @validator("name")
    def name_must_be_unique(cls, name):
        """Validates if the name is unique."""
        document = DocumentMasterService().is_document_exists(document_name=name)
        if document:
            raise DocumentValueAlreadyExistsError()
        return name


class DocumentMasterUpdate(DocumentMasterBaseWithSchemeValidation):
    """
    Represents a document_master update schema.
    """
    name: str
    is_geotag_required: bool


class DocumentMasterResponse(DocumentMasterBase):
    """
    Represents a document_master response schema.
    """

    id: int
    is_geotag_required: bool
    schemes: Optional[List[SchemeAssociateResponse]] = None
    # is_mjp_jay: Optional[bool] = None
    # is_ab_pmjay: Optional[bool] = None

    @root_validator(pre=True)
    def set_is_mjp_jay(cls, values):
        schemes = values.schemes
        values.is_mjp_jay = (
            any(scheme.type == "MJP-JAY" for scheme in schemes) if schemes else False
        )
        return values

    @root_validator(pre=True)
    def set_is_ab_pmjay(cls, values):
        schemes = values.schemes
        values.is_ab_pmjay = (
            any(scheme.type == "AB-PM-JAY" for scheme in schemes) if schemes else False
        )
        return values


class DocumentMasterRetrievedResponse(BaseResponse):
    """
    Represents a document_master response schema.
    """

    data: Optional[DocumentMasterResponse] = None


class DocumentMasterListResponse(BaseResponse):
    """
    Represents a document_master response schema.
    """

    data: Optional[List[DocumentMasterResponse]] = None


class DocumentMasterFilterPaginationListResponse(BaseResponse):
    """
    Represents a document_master response schema.
    """

    data: Optional[List[DocumentMasterResponse]] = None
    total_count: Optional[int] = None
