from typing import Optional, List
from pydantic import BaseModel, validator

from app.master.exception.errors.package import PackageNotExistsValueError
from app.master.exception.errors.step_master import StepNotExistsValueError

from app.master.services.package import PackageService
from app.master.services.step_master import StepMasterService

from app.master.validations.package_step_document import (
    PackageStepDocumentAssociateResponse,
)
from app.master.validations.step_master import StepMasterResponse

from utils.response import BaseResponse


class PackageStepBase(BaseModel):
    """Base class for package step master data."""

    package_id: int
    step_id: int


class PackageStepCreate(PackageStepBase):
    @validator("package_id")
    def check_package_exists(cls, v):
        """Check if the package exists."""
        package_service = PackageService()
        is_exist = package_service.is_package_exists_by_id(package_id=v)
        if not is_exist:
            raise PackageNotExistsValueError()
        return v

    @validator("step_id")
    def check_step_exists(cls, v):
        """Check if the step exists."""
        step_service = StepMasterService()
        is_exist = step_service.is_step_exists_by_id(step_id=v)
        if not is_exist:
            raise StepNotExistsValueError()
        return v


class DocumentMasterBase(BaseModel):
    """
    Represents a document_master in the database.
    """
    id:  int
    name: str
    is_geotag_required: bool



class PackageStepDocument(BaseModel):
    id: int
    document_master: DocumentMasterBase


class PackageStepBaseReadResponse(BaseModel):
    """Base class for package step master data."""

    id: int
    steps: Optional[StepMasterResponse]
    package_step_document: List[PackageStepDocument]


class PackageStepAssociateResponse(BaseModel):
    id: int
    steps: StepMasterResponse
    package_step_document: List[PackageStepDocumentAssociateResponse] = []


class PackageStepRetrieveResponse(BaseResponse):
    """
    Represents a package step response schema.
    """

    data: Optional[PackageStepBaseReadResponse] = None


class PackageStepListResponse(BaseResponse):
    """
    Represents a package step response schema.
    """

    data: Optional[List[PackageStepBaseReadResponse]] = None
