from typing import List, Optional

from pydantic import BaseModel, validator

from app.master.exception.errors.document_master import DocumentNotExistsValueError
from app.master.services.document_master import DocumentMasterService
from app.master.services.scheme_type import SchemeTypeService
from utils.response import BaseResponse


class SchemeDocumentBase(BaseModel):
    """
    Represents the base schema for a scheme document.
    """

    scheme_id: int
    document_id: int


class AssignDocuments(BaseModel):
    document_ids: List[int]

    @validator("document_ids")
    def check_document_exists(cls, v):
        """
        Check if the document exists.
        """
        document_master_service = DocumentMasterService()
        missing_ids = document_master_service.check_documents_exist_by_ids(
            document_ids=v
        )
        if missing_ids:
            raise DocumentNotExistsValueError(document_ids=missing_ids)
        return v


class SchemeDocumentBaseReadResponse(SchemeDocumentBase):
    """
    Represents the base schema for a scheme document response.
    """

    id: int


class SchemeDocumentRetrievedResponse(BaseResponse):
    """
    Represents a scheme document response schema.
    """

    data: Optional[SchemeDocumentBaseReadResponse] = None


class SchemeDocumentListResponse(BaseResponse):
    """
    Represents a scheme document response schema.
    """

    data: Optional[List[SchemeDocumentBaseReadResponse]] = None
