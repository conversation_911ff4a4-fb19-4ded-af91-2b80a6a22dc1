from typing import Optional, List
from pydantic import BaseModel, validator

from app.master.exception.errors.category import CategoryNotFoundValueError

from app.master.models.category import CategoryDB

from app.master.validations.category import CategoryBaseReadResponse

from utils.response import BaseResponse
from app.validations.base import CustomBaseModel




class SubCategoryBase(CustomBaseModel):
    """Base class for subcategory master data."""

    code: str
    name: str


class SubCategoryCreate(SubCategoryBase):
    """Class for creating a subcategory."""

    category_id: int


    @validator("code")
    def check_code(cls, v):
        """Check if the code length is valid."""
        cls.check_value_range(field="code",value=v,min_value=1,max_value=20)
        return v
    
    @validator("name")
    def check_name(cls, v):
        """Check if the name length is valid."""
        cls.check_value_range(field="name",value=v,min_value=1,max_value=300)
        return v

    @validator("category_id")
    def check_category_exists(cls, v):
        """Check if the category exists."""
        
        cls.check_foreign_key_exists(verbose_name="Category",field="id", value=v, model=CategoryDB)
        return v


class SubCategoryUpdate(CustomBaseModel):
    """Class for updating a subcategory."""

    name: str
    category_id: int
    code: str

    @validator("code")
    def check_code(cls, v):
        """Check if the code length is valid."""
        cls.check_value_range(field="code",value=v,min_value=1,max_value=20)
        return v
    
    @validator("name")
    def check_name(cls, v):
        """Check if the name length is valid."""
        cls.check_value_range(field="name",value=v,min_value=1,max_value=300)
        return v

    @validator("category_id")
    def check_category_exists(cls, v):
        """Check if the category exists."""
        
        cls.check_foreign_key_exists(verbose_name="Category",field="id", value=v, model=CategoryDB)
        return v


class SubCategoryBaseReadResponse(SubCategoryBase):
    """Base class for subcategory master response."""

    id: int
    category: Optional[CategoryBaseReadResponse]


class SubCategoryRetrievedResponse(BaseResponse):
    """
    Represents a subcategory_master response schema.
    """

    data: Optional[SubCategoryBaseReadResponse] = None


class SubCategoryListResponse(BaseResponse):
    """
    Represents a subcategory_master response schema.
    """

    data: Optional[List[SubCategoryBaseReadResponse]] = None


class SubCategoryFilterPaginationResponse(BaseResponse):
    """Represents a subcategory_master response schema."""

    data: Optional[List[SubCategoryBaseReadResponse]] = None
    total_count: int = 0
