from typing import List, Optional

from fastapi import File, UploadFile
from pydantic import BaseModel, validator

from app.database.database import Session<PERSON>ocal
from app.master.exception.errors.category import CategoryNotFoundValueError
from app.master.exception.errors.sub_category import SubCategoryNotFoundErrorValueError
from app.master.models.category import CategoryDB
from app.master.models.package import StepMaster
from app.master.models.sub_category import SubCategoryDB
from app.master.validations.category import CategoryBaseReadResponse
from app.master.validations.package_step import PackageStepAssociateResponse
from app.master.validations.step_master import StepMasterResponse
from app.master.validations.sub_category import SubCategoryBaseReadResponse
from app.validations.base import CustomBaseModel
from utils.response import BaseResponse


class PackageModel(CustomBaseModel):
    """Model for package data."""

    procedure_name: str
    package_amount: int

    @validator("package_amount")
    def check_package_amount(cls, value):
        min_int_value = 0
        max_int_value = 1000000
        if value < min_int_value or value > max_int_value:
            raise ValueError(
                f"Package amount must be between {min_int_value} and {max_int_value}"
            )
        return value


class PackageBase(PackageModel):
    """Base class for package master data."""

    procedure_code: str


class PackageCreate(PackageBase):
    category_id: int
    sub_category_id: Optional[int] = None
    steps: List[int]

    @validator("procedure_code")
    def check_procedure_code(cls, v):

        cls.check_value_range(
            field="procedure_code", value=v, min_value=1, max_value=20, is_required=True
        )
        return v

    @validator("procedure_name")
    def check_procedure_name(cls, v):
        cls.check_value_range(
            field="procedure_name",
            value=v,
            min_value=1,
            max_value=500,
            is_required=True,
        )
        return v

    @validator("category_id")
    def check_category_exists(cls, v):
        """Check if the category exists."""
        cls.check_foreign_key_exists(
            verbose_name="Category", field="id", value=v, model=CategoryDB
        )
        return v

    @validator("sub_category_id")
    def check_sub_category_exists(cls, v):
        """Check if the sub category exists."""

        cls.check_foreign_key_exists(
            verbose_name="Sub Category", field="id", value=v, model=SubCategoryDB
        )
        return v

    @validator("steps")
    def validate_steps(cls, v):
        """Validate the steps."""
        if not v:
            raise ValueError("Steps list cannot be empty")
        cls.check_foreign_key_exists(
            verbose_name="Steps", field="id", value=v, model=StepMaster
        )
        return v


class PackageUpdate(PackageBase):
    category_id: int
    sub_category_id: Optional[int] = None
    steps: list

    @validator("category_id")
    def check_category_exists(cls, v):
        """Check if the category exists."""

        cls.check_foreign_key_exists(
            verbose_name="Category", field="id", value=v, model=CategoryDB
        )

        return v

    @validator("sub_category_id")
    def check_sub_category_exists(cls, v):
        """Check if the sub category exists."""

        cls.check_foreign_key_exists(
            verbose_name="Sub Category", field="id", value=v, model=SubCategoryDB
        )

        return v

    @validator("steps")
    def validate_steps(cls, v):
        """Validate the steps."""
        if not v:
            raise ValueError("Steps list cannot be empty.")

        cls.check_foreign_key_exists(
            verbose_name="Steps", field="id", value=v, model=StepMaster
        )
        return v


class PackageBulkUpload(BaseModel):
    file: UploadFile = File(...)
    is_mjp_jay: bool = True


class PackageBaseReadResponse(PackageBase):
    """Base class for package master response."""

    id: int
    categories: Optional[CategoryBaseReadResponse] = []
    sub_categories: Optional[SubCategoryBaseReadResponse] = []
    steps: Optional[List[StepMasterResponse]] = []
    package_steps: Optional[List[PackageStepAssociateResponse]] = None
    verbose_code: Optional[str] = None

    class Config:
        from_attributes = True


class PackageRetrievedResponse(BaseResponse):
    """
    Represents a package_master response schema.
    """

    data: Optional[PackageBaseReadResponse] = None


class PackageListResponse(BaseResponse):
    """
    Represents a package_master response schema.
    """

    data: Optional[List[PackageBaseReadResponse]] = None


class PackageFilterPaginationResponse(BaseResponse):
    """Represents a package_master response schema."""

    data: Optional[List[PackageBaseReadResponse]] = None
    total_count: int = 0


class SubCategoryBase(BaseModel):
    """Base class for subcategory master data."""

    code: str
    name: str


class PackageDDResponse(PackageBase):
    """Base class for package master response."""

    id: int
    categories: Optional[CategoryBaseReadResponse] = []
    sub_categories: Optional[SubCategoryBase] = []

    class Config:
        from_attributes = True

class PackageDropdownItem(BaseModel):
    id: int
    procedure_code: str
    procedure_name: str
    package_amount: int
    category_name: Optional[str] = None
    category_code: Optional[str] = None
    subcategory_name: Optional[str] = None
    subcategory_code: Optional[str] = None
    category_id: Optional[int] = None
    sub_category_id: Optional[int] = None
    scheme_type_id: Optional[int] = None
    verbose_code: Optional[str] = None  # This will map to verbose_code from the hybrid property

    class Config:
        from_attributes = True

class PackageDropdownResponse(BaseResponse):
    """
    Represents a dropdown response for packages.
    """
    data: Optional[List[PackageDropdownItem]] = None


