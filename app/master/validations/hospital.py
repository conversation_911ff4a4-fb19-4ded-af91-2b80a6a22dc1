from typing import List, Optional

from pydantic import BaseModel, condecimal, validator

from app.master.exception.errors.scheme_type import SchemeTypeNotExistWithIdsValueError
from app.master.models.hospital import HospitalDB
from app.master.models.scheme_type import SchemeType
from app.master.services.hospital import HospitalService
from app.master.services.scheme_type import SchemeTypeService
from app.master.validations.scheme_type import SchemeAssociateResponse
from app.validations.base import CustomBaseModel
from utils.response import BaseResponse


class HospitalBase(CustomBaseModel):
    """Base class for hospital master data."""

    name: str


class HospitalCreate(HospitalBase):
    """Class for creating a hospital."""

    schemes: List[int]
    short_name: Optional[str] = None
    latitude: Optional[
        condecimal(max_digits=18, decimal_places=15)
    ] = None  # type: ignore
    longitude: Optional[
        condecimal(max_digits=18, decimal_places=15)
    ] = None  # type: ignore

    @validator("name")
    def check_name_length(cls, v):
        """Check if the length of name is under 200 characters."""
        if len(v) > 200:
            raise ValueError("Name length should be under 200 characters.")
        return v

    @validator("name")
    def check_name_already_exist(cls, v):
        """Check if the name already exists."""

        cls.check_unique(field="name", value=v, model=HospitalDB)
        return v

    @validator("short_name")
    def check_short_name_already_exist(cls, v):
        """Check if the name already exists."""

        cls.check_unique(field="short_name", value=v, model=HospitalDB)
        return v

    @validator("schemes")
    def scheme_id_must_exist(cls, scheme_ids):
        """Validates if the scheme_id exists."""
        cls.check_foreign_key_exists(
            verbose_name="Scheme Type", field="id", value=scheme_ids, model=SchemeType
        )

        return scheme_ids


class HospitalUpdate(CustomBaseModel):
    """Class for updating a hospital."""

    schemes: List[int]
    name: str
    short_name: Optional[str] = None
    latitude: condecimal(max_digits=18, decimal_places=15)  # type: ignore
    longitude: condecimal(max_digits=18, decimal_places=15)  # type: ignore

    @validator("schemes")
    def scheme_id_must_exist(cls, scheme_ids):
        """Validates if the scheme_id exists."""
        cls.check_foreign_key_exists(
            verbose_name="Scheme Type", field="id", value=scheme_ids, model=SchemeType
        )

        return scheme_ids


class HospitalBaseReadResponse(HospitalBase):
    """Base class for hospital master response."""

    id: int
    short_name: Optional[str] = None
    latitude: Optional[
        condecimal(max_digits=18, decimal_places=15)  # type: ignore
    ] = None  # type: ignore
    longitude: Optional[
        condecimal(max_digits=18, decimal_places=15)  # type: ignore
    ] = None  # type: ignore
    schemes: Optional[List[SchemeAssociateResponse]] = None


class HospitalRetrievedResponse(BaseResponse):
    """
    Represents a hospital_master response schema.
    """

    data: Optional[HospitalBaseReadResponse] = None


class HospitalListResponse(BaseResponse):
    """
    Represents a hospital_master response schema.
    """

    data: Optional[List[HospitalBaseReadResponse]] = None


class HospitalFilterPaginationResponse(BaseResponse):
    data: Optional[List[HospitalBaseReadResponse]] = None
    total_count: int = 0
