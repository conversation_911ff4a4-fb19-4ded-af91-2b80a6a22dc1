from typing import List, Optional

from app.master.exception.errors.document_master import DocumentNotExistsValueError
from app.master.exception.errors.package_step import PackageStepNotExistsValueError
from app.master.models.document_master import DocumentMaster
from app.master.models.package import PackageDB
from app.master.validations.document_master import DocumentMasterResponse
from app.master.validations.step_master import StepMasterResponse
from app.validations.base import CustomBaseModel
from pydantic import BaseModel, validator
from utils.response import BaseResponse


class PackageStepDocumentBase(CustomBaseModel):
    """Base class for package step document master data."""

    package_step_id: int
    document_id: int


class PackageStepDocumentCreate(PackageStepDocumentBase):
    @validator("package_step_id")
    def check_package_step_exists(cls, v):
        """Check if the package step exists."""

        cls.check_foreign_key_exists(
            verbose_name="Package Step",
            field="id",
            value=v,
            model=PackageStepDocumentDB,
        )
        return v

    @validator("document_id")
    def check_document_exists(cls, v):
        """Check if the document exists."""

        cls.check_foreign_key_exists(
            verbose_name="Document",
            field="id",
            value=v,
            model=DocumentMaster,
        )

        return v


class PackageStepDocumentBaseReadResponse(PackageStepDocumentBase):
    """Base class for package step document"""

    id: int


class PackageStepAssociateBase(BaseModel):
    """Base class for package step master data."""

    steps: StepMasterResponse


class PackageStepDocumentAssociateResponse(BaseModel):
    """Base class for package"""

    id: int
    document_master: DocumentMasterResponse
    # package_step_master: PackageStepAssociateBase


class PackageStepDocumentRetrieveResponse(BaseResponse):
    """
    Represents a package step document response schema.
    """

    data: Optional[PackageStepDocumentBaseReadResponse] = None


class PackageStepDocumentListResponse(BaseResponse):
    """
    Represents a package step document response schema.
    """

    data: Optional[List[PackageStepDocumentBaseReadResponse]] = None


class PackageStepDocumentMapping(CustomBaseModel):
    package_step_id: int
    document_ids: List[int]

    @validator("package_step_id")
    def check_package_step_exists(cls, v):
        """Check if the package step exists."""

        cls.check_foreign_key_exists(
            verbose_name="Package Step",
            field="id",
            value=v,
            model=PackageDB,
        )
        return v

    @validator("document_ids")
    def check_document_exists(cls, v):
        """Check if the document exists."""

        cls.check_foreign_key_exists(
            verbose_name="Document",
            field="id",
            value=v,
            model=DocumentMaster,
        )

        return v
