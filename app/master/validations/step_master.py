from typing import Optional, List
from pydantic import BaseModel, validator

from app.master.exception.errors.step_master import StepOrderAlreadyExistsValueError
from app.master.models.package import StepType
from app.master.services.step_master import StepMasterService

from utils.response import BaseResponse


class StepMasterBase(BaseModel):
    """
    Represents a step_master in the database.
    """

    type: StepType
    order: int

    class Config:
        from_attributes = True


class StepMasterCreate(StepMasterBase):
    """
    Represents a step_master creation schema.
    """

    @validator("order")
    def order_must_be_positive(cls, order):
        """Validates if the order is positive."""
        if order <= 0:
            raise ValueError("Order must be a positive integer")
        return order

    @validator("order")
    def order_must_be_unique(cls, order):
        """Validates if the order is unique."""
        step = StepMasterService().is_step_exists_by_order(order=order)
        if step:
            raise StepOrderAlreadyExistsValueError()
        return order

    @validator("type")
    def type_must_be_unique(cls, type):
        """Validates if the type is unique."""
        step = StepMasterService().is_step_exists(step_type=type.value)
        if step:
            raise StepValueAlreadyExistsError()
        return type


class StepMasterUpdate(StepMasterBase):
    """
    Represents a step_master update schema.
    """

    @validator("order")
    def order_must_be_positive(cls, order):
        """Validates if the order is positive."""
        if order <= 0:
            raise ValueError("Order must be a positive integer")
        return order

    @validator("order")
    def order_must_be_unique(cls, order):
        """Validates if the order is unique."""
        step = StepMasterService().is_step_exists_by_order(order=order)
        if step:
            raise StepOrderAlreadyExistsValueError()
        return order

    @validator("type")
    def type_must_be_unique(cls, type):
        """Validates if the type is unique."""
        step = StepMasterService().is_step_exists(step_type=type)
        if step:
            raise StepValueAlreadyExistsError()
        return type


class StepMasterResponse(StepMasterBase):
    """
    Represents a step_master response schema.
    """

    id: int


class StepMasterRetrievedResponse(BaseResponse):
    """
    Represents a step_master response schema.
    """

    data: Optional[StepMasterResponse] = None


class StepMasterListResponse(BaseResponse):
    """
    Represents a step_master response schema.
    """

    data: Optional[List[StepMasterResponse]] = None


class StepValueAlreadyExistsError(ValueError):
    """Exception raised when a step value already exists."""

    def __init__(self):
        self.msg = "Step value already exists"
        super().__init__(self.msg)


