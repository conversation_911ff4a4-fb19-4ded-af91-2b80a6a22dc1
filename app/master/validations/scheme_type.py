from typing import Optional, List
from pydantic import BaseModel, validator

from app.master.services.scheme_type import SchemeTypeService

from app.master.validations.category import CategoryBaseReadResponse

from utils.response import BaseResponse


class SchemeTypeBase(BaseModel):
    """
    Represents a scheme_type_master in the database.
    """

    type: str

    class Config:
        from_attributes = True


class SchemeTypeCreate(SchemeTypeBase):
    """
    Represents a scheme_type_master creation schema.
    """

    @validator("type")
    def type_must_be_unique(cls, type):
        """Validates if the type is unique."""
        scheme_type = SchemeTypeService().is_scheme_type_exists(scheme_type=type)
        if scheme_type:
            raise SchemeTypeValueAlreadyExistsError()
        return type


class SchemeTypeUpdate(SchemeTypeBase):
    """
    Represents a scheme_type_master update schema.
    """

    @validator("type")
    def type_must_be_unique(cls, type):
        """Validates if the type is unique."""
        scheme_type = SchemeTypeService().is_scheme_type_exists(scheme_type=type)
        if scheme_type:
            raise SchemeTypeValueAlreadyExistsError()
        return type


class SchemeTypeResponse(SchemeTypeBase):
    """
    Represents a scheme_type_master response schema.
    """

    id: int
    categories: Optional[List[CategoryBaseReadResponse]] = None


class SchemeAssociateResponse(SchemeTypeBase):
    """
    Represents a scheme_type_master response schema.
    """

    id: int


class SchemeTypeRetrievedResponse(BaseResponse):
    """
    Represents a scheme_type_master response schema.
    """

    data: Optional[SchemeTypeResponse] = None


class SchemeTypeListResponse(BaseResponse):
    """
    Represents a scheme_type_master response schema.
    """

    data: Optional[List[SchemeTypeResponse]] = None


class SchemeTypeValueAlreadyExistsError(ValueError):
    """Raised when the scheme_type value already exists."""

    def __init__(self):
        self.msg = "Scheme type value already exists"
        super().__init__(self.msg)

