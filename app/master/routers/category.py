from typing import Optional, Union

from fastapi import APIRouter, Depends, File, Query, UploadFile, status
from sqlalchemy.orm import Session

# Function to get a database session
from app.database.database import SessionLocal, get_db
from app.master.services.category import CategoryService
from app.master.validations.category import (
    CategoryCreate,
    CategoryFilterPaginationResponse,
    CategoryListResponse,
    CategoryRetrievedResponse,
    CategoryUpdate,
)
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler
from utils.schema import Error

# Create an API router
router = APIRouter()

# Create an instance of the CategoryService
category_service = CategoryService()


# Endpoint to get all categories
@router.get("/categories", response_model=CategoryListResponse)
def get_categories(scheme: int = Query(None), db: Session = Depends(get_db)):
    try:
        kwargs = {"scheme": scheme}
        categories = category_service.get_all_categories(db, **kwargs)
        return generate_response(
            data=categories,
            status_code=status.HTTP_200_OK,
            message="Categories retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to filter categories
@router.get("/categories/filter", response_model=CategoryFilterPaginationResponse)
async def filter_categories(
    code: Optional[str] = Query(None, min_length=2),
    name: Optional[str] = Query(None, min_length=3),
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1),
    order_by: str = None,
    scheme: str = Query(None, min_length=3),
    db: Session = Depends(get_db),
):
    try:
        filtered_categories, total_count = category_service.filter_and_sort_categories(
            db=db,
            code=code,
            name=name,
            page=page,
            limit=limit,
            scheme=scheme,
            order_by=order_by,
        )
        return generate_response(
            total_count=total_count,
            data=filtered_categories,
            status_code=status.HTTP_200_OK,
            message="Categories filtered successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to get a specific category by ID
@router.get(
    "/categories/{category_id}", response_model=Union[CategoryRetrievedResponse, Error]
)
def get_category(category_id: int, db: Session = Depends(get_db)):
    try:
        is_exists, content = category_service.get_category_or_raise(category_id, db)
        if is_exists:
            return generate_response(
                data=content,
                status_code=status.HTTP_200_OK,
                message="Category retrieved successfully",
            )
        else:
            return generate_response(custom_response=content.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to create a new category
@router.post("/categories", response_model=CategoryRetrievedResponse)
async def create_category(category: CategoryCreate, db: Session = Depends(get_db)):
    try:
        created_category = category_service.create_category(category, db)
        return generate_response(
            data=created_category,
            status_code=status.HTTP_201_CREATED,
            message="Category created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to update a category by ID
@router.put(
    "/categories/{category_id}", response_model=Union[CategoryRetrievedResponse, Error]
)
async def update_category(
    category_id: int,
    category: CategoryUpdate,
    db: Session = Depends(get_db),
):
    try:
        is_updated, updated_category = category_service.update_category(
            category_id, category, db
        )
        if is_updated:
            return generate_response(
                data=updated_category,
                status_code=status.HTTP_200_OK,
                message="Category updated successfully",
            )
        else:
            return generate_response(custom_response=updated_category.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to delete a category by ID
@router.delete(
    "/categories/{category_id}",
)
async def delete_category(
    category_id: int, force_delete: bool = False, db: Session = Depends(get_db)
):
    try:
        is_deleted, deleted_category = category_service.delete_category(
            category_id, db, force_delete
        )
        if is_deleted:
            return generate_response(
                status_code=status.HTTP_200_OK,
                message="Category deleted successfully",
            )
        else:
            return generate_response(custom_response=deleted_category.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to upload categories from a file
@router.post("/categories/bulk-upload")
async def upload_categories(
    file: UploadFile = File(...), db: Session = Depends(get_db)
):
    try:
        contents = await file.read()
        categories_created = category_service.bulk_insert_categories(contents=contents)
        return generate_response(
            data=categories_created,
            status_code=status.HTTP_201_CREATED,
            message="Categories created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)
