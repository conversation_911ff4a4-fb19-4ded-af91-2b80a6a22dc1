from fastapi import APIRouter, Depends, status
from fastapi.responses import JSONResponse

from sqlalchemy.orm import Session

from app.master.services.scheme_document import SchemeDocumentService

from app.master.validations.scheme_document import (
    AssignDocuments,
    SchemeDocumentRetrievedResponse,
    SchemeDocumentListResponse,
)
from utils.generate_response import generate_response
from utils.common import get_db
from utils.helpers import ExceptionHandler

# Create an API router
router = APIRouter()


@router.get(
    "/scheme-documents/{scheme_id}", response_model=SchemeDocumentListResponse
)
def read_scheme_documents(scheme_id: int, db: Session = Depends(get_db)):
    try:
        scheme_documents = SchemeDocumentService().get_scheme_documents(
            scheme_id=scheme_id
        )
        if not scheme_documents:
            return generate_response(
                data=scheme_documents,
                status_code=status.HTTP_204_NO_CONTENT,
                message="No content",
            )
        return generate_response(
            data=scheme_documents,
            status_code=status.HTTP_200_OK,
            message="Scheme Documents retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.post("/scheme-documents/{scheme_id}")
def assign_documents_to_scheme(
    scheme_id: int, documents: AssignDocuments, db: Session = Depends(get_db)
):
    try:

        scheme_documents = SchemeDocumentService().assign_documents_to_scheme_type(
            scheme_id, documents.document_ids
        )
        return generate_response(
            data=scheme_documents,
            status_code=status.HTTP_201_CREATED,
            message="Documents assigned successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.delete("/scheme-documents/{scheme_id}")
def remove_scheme_documents(
    scheme_id: int, documents: AssignDocuments, db: Session = Depends(get_db)
):
    try:
        is_removed = SchemeDocumentService().remove_documents_from_scheme_type(
            scheme_id=scheme_id, document_ids=documents.document_ids
        )
        return generate_response(
            status_code=status.HTTP_200_OK,
            message="Scheme documents removed successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)
