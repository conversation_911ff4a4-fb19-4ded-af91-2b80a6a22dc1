from typing import Optional, Union

from fastapi import APIRouter, Depends, Query, status
from sqlalchemy.orm import Session

# Function to get a database session
from app.database.database import SessionLocal, get_db
from app.master.services.document_master import DocumentMasterService
from app.master.validations.document_master import (
    DocumentMasterCreate,
    DocumentMasterFilterPaginationListResponse,
    DocumentMasterListResponse,
    DocumentMasterRetrievedResponse,
    DocumentMasterUpdate,
)
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler
from utils.schema import Error

# Create an API router
router = APIRouter()


# Create an instance of the DocumentMasterService
document_service = DocumentMasterService()


# Endpoint to get all documents
@router.get("/documents-master", response_model=DocumentMasterListResponse)
def get_documents(db: Session = Depends(get_db)):
    try:
        documents = document_service.get_all_documents(db)
        return generate_response(
            data=documents,
            status_code=status.HTTP_200_OK,
            message="Documents retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.get(
    "/documents-master/filter",
    response_model=DocumentMasterFilterPaginationListResponse,
)
async def filter_documents(
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1),
    order_by: str = None,
    name: str = Query(None, min_length=3),
    scheme: str = Query(None, min_length=3),
    is_geotag_required: bool = Query(None),
    db: Session = Depends(get_db),
):
    try:
        filtered_documents, total_count = document_service.filter_and_sort_documens(
            db=db,
            name=name,
            scheme=scheme,
            is_geotag_required=is_geotag_required,
            order_by=order_by,
            page=page,
            limit=limit,
        )
        return generate_response(
            total_count=total_count,
            data=filtered_documents,
            status_code=status.HTTP_200_OK,
            message="Documents filtered successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to get a specific document by ID
@router.get(
    "/documents-master/{document_id}",
    response_model=Union[DocumentMasterRetrievedResponse, Error],
)
def get_document(document_id: int, db: Session = Depends(get_db)):
    try:
        content = document_service.get_document_master_or_raise(document_id, db)
        return generate_response(
            data=content,
            status_code=status.HTTP_200_OK,
            message="Document retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to create a new document
@router.post(
    "/documents-master", response_model=Union[DocumentMasterRetrievedResponse, Error]
)
async def create_document(
    document: DocumentMasterCreate, db: Session = Depends(get_db)
):
    try:
        is_created, created_document = document_service.create_document(document, db)
        if is_created:
            return generate_response(
                data=created_document,
                status_code=status.HTTP_201_CREATED,
                message="Document created successfully",
            )
        else:
            return generate_response(custom_response=created_document.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to update a document by ID
@router.put(
    "/documents-master/{document_id}",
    response_model=Union[DocumentMasterRetrievedResponse, Error],
)
async def update_document(
    document_id: int, document: DocumentMasterUpdate, db: Session = Depends(get_db)
):
    try:
        updated_document = document_service.update_document(document_id, document, db)
        return generate_response(
            data=updated_document,
            status_code=status.HTTP_200_OK,
            message="Document updated successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to delete a document by ID
@router.delete("/documents-master/{document_id}")
async def delete_document(document_id: int, db: Session = Depends(get_db)):
    try:
        is_deleted, deleted_document = document_service.delete_document(document_id, db)
        if is_deleted:
            return generate_response(
                status_code=status.HTTP_200_OK, message="Document deleted successfully"
            )
        else:
            return generate_response(custom_response=deleted_document.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)
