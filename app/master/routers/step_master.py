from fastapi import APIRouter, Depends, status
from sqlalchemy.orm import Session

# Function to get a database session
from app.database.database import Session<PERSON><PERSON><PERSON>, get_db
from app.master.services.step_master import StepMasterService
from app.master.validations.step_master import (
    StepMasterCreate,
    StepMasterListResponse,
    StepMasterRetrievedResponse,
    StepMasterUpdate,
)
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler

# Create an API router
router = APIRouter()


# Create an instance of the StepMasterService
step_service = StepMasterService()


# Endpoint to get all steps
@router.get("/steps", response_model=StepMasterListResponse)
def get_steps(db: Session = Depends(get_db)):
    try:
        steps = step_service.get_all_steps(db)
        return generate_response(
            data=steps,
            status_code=status.HTTP_200_OK,
            message="Steps retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to get a specific step by ID
@router.get("/steps/{step_id}")
def get_step(step_id: int, db: Session = Depends(get_db)):
    try:
        is_exists, content = step_service.get_step_by_id(step_id, db)
        if is_exists:
            return generate_response(data=content, status_code=status.HTTP_200_OK)
        else:
            return generate_response(custom_response=content.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to create a new step
@router.post("/steps", response_model=StepMasterRetrievedResponse)
async def create_step(step: StepMasterCreate, db: Session = Depends(get_db)):
    try:
        created_step = step_service.create_step(step, db)
        return generate_response(
            data=created_step,
            status_code=status.HTTP_201_CREATED,
            message="Step created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to update a step by ID
@router.put("/steps/{step_id}")
async def update_step(
    step_id: int, step: StepMasterCreate, db: Session = Depends(get_db)
):
    try:
        is_updated, updated_step = step_service.update_step(step_id, step, db)
        if is_updated:
            return generate_response(data=updated_step, status_code=status.HTTP_200_OK)
        else:
            return generate_response(custom_response=updated_step.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to delete a step by ID
@router.delete("/steps/{step_id}")
async def delete_step(step_id: int, db: Session = Depends(get_db)):
    try:
        is_deleted, deleted_step = step_service.delete_step(step_id, db)
        if is_deleted:
            return generate_response(
                status_code=status.HTTP_200_OK, message="Step deleted successfully"
            )
        else:
            return generate_response(custom_response=deleted_step.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)
