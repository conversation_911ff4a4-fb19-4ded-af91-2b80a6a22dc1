from typing import Optional, Union

from fastapi import APIRouter, Depends, File, Query, UploadFile, status
from sqlalchemy.orm import Session

# Function to get a database session
from app.database.database import SessionLocal, get_db
from app.master.services.hospital import HospitalService
from app.master.validations.hospital import (
    HospitalCreate,
    HospitalFilterPaginationResponse,
    HospitalListResponse,
    HospitalRetrievedResponse,
    HospitalUpdate,
)
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler
from utils.schema import Error

# Create an API router
router = APIRouter()


# Endpoint to get all hospitals
@router.get("/hospitals", response_model=HospitalListResponse)
def get_hospitals(
    user: int = Query(None), scheme: int = Query(None), db: Session = Depends(get_db)
):
    try:
        kwargs = {"user": user, "scheme": scheme}
        hospitals = HospitalService().get_all_hospitals(db, **kwargs)
        return generate_response(
            data=hospitals,
            status_code=status.HTTP_200_OK,
            message="Hospitals retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to filter hospitals
@router.get("/hospitals/filter", response_model=HospitalFilterPaginationResponse)
async def filter_hospitals(
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1),
    order_by: str = None,
    name: str = Query(None, min_length=3),
    scheme: str = Query(None, min_length=3),
    db: Session = Depends(get_db),
):
    try:
        filtered_hospitals, total_count = HospitalService().filter_hospitals(
            db=db,
            name=name,
            scheme=scheme,
            order_by=order_by,
            page=page,
            limit=limit,
        )
        return generate_response(
            total_count=total_count,
            data=filtered_hospitals,
            status_code=status.HTTP_200_OK,
            message="Hospitals filtered successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to get a specific hospital by ID
@router.get(
    "/hospitals/{hospital_id}", response_model=Union[HospitalRetrievedResponse, Error]
)
def get_hospital(hospital_id: int, db: Session = Depends(get_db)):
    try:
        content = HospitalService().get_hospital_or_raise(hospital_id, db)
        return generate_response(
            data=content,
            status_code=status.HTTP_200_OK,
            message="Hospital retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to create a new hospital
@router.post("/hospitals", response_model=HospitalRetrievedResponse)
async def create_hospital(hospital: HospitalCreate, db: Session = Depends(get_db)):
    try:
        hospital_service = HospitalService()
        created_hospital = hospital_service.create_hospital(db=db, hospital_data=hospital)
        return generate_response(
            data=created_hospital,
            status_code=status.HTTP_201_CREATED,
            message="Hospital created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to update a hospital by ID
@router.put(
    "/hospitals/{hospital_id}", response_model=Union[HospitalRetrievedResponse, Error]
)
async def update_hospital(
    hospital_id: int,
    hospital: HospitalUpdate,
    db: Session = Depends(get_db),
):
    try:
        updated_hospital = HospitalService().update_hospital(hospital_id, hospital, db)
        return generate_response(
            data=updated_hospital,
            status_code=status.HTTP_200_OK,
            message="Hospital updated successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to delete a hospital by ID
@router.delete(
    "/hospitals/{hospital_id}",
)
async def delete_hospital(
    hospital_id: int, force_delete: bool = False, db: Session = Depends(get_db)
):
    try:
        deleted_hospital = HospitalService().delete_hospital(
            hospital_id, db, force_delete
        )
        if deleted_hospital:
            return generate_response(
                data=deleted_hospital,
                status_code=status.HTTP_200_OK,
                message="Hospital deleted successfully",
            )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to upload hospitals from a file
@router.post("/hospitals/bulk-upload")
async def upload_hospitals(file: UploadFile = File(...), db: Session = Depends(get_db)):
    try:
        contents = await file.read()
        hospitals_created = HospitalService().bulk_insert_hospitals(contents=contents)
        return generate_response(
            data=hospitals_created,
            status_code=status.HTTP_201_CREATED,
            message="Hospitals created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)
