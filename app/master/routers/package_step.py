from typing import Optional, Union

from fastapi import APIRouter, Depends, File, Query, UploadFile, status
from sqlalchemy.orm import Session

# Function to get a database session
from app.database.database import SessionLocal, get_db
from app.master.services.package_step import PackageStepMasterService
from app.master.validations.package_step import (
    PackageStepCreate,
    PackageStepListResponse,
    PackageStepRetrieveResponse,
)
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler
from utils.schema import Error

# Create an API router
router = APIRouter()


# Create an instance of the PackageStepMasterService
package_step_service = PackageStepMasterService()


# Endpoint to get all package steps
@router.get("/package-steps", response_model=PackageStepListResponse)
def get_package_steps(package_id: int = Query(None), db: Session = Depends(get_db)):
    try:
        kwargs = {"package_id": package_id}
        package_steps = package_step_service.get_all_package_step_masters(db, **kwargs)
        return generate_response(
            data=package_steps,
            status_code=status.HTTP_200_OK,
            message="Package steps retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to create a new package step
@router.post("/package-steps", response_model=PackageStepRetrieveResponse)
async def create_package_step(
    package_step: PackageStepCreate, db: Session = Depends(get_db)
):
    try:
        created_package_step = package_step_service.create_package_step_master(
            package_step, db
        )
        return generate_response(
            data=created_package_step,
            status_code=status.HTTP_201_CREATED,
            message="Package step created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to delete a package step by ID
@router.delete(
    "/package-steps/{package_step_id}",
)
async def delete_package_step(package_step_id: int, db: Session = Depends(get_db)):
    try:
        is_deleted, deleted_package_step = (
            package_step_service.delete_package_step_master(package_step_id, db)
        )
        if is_deleted:
            return generate_response(
                status_code=status.HTTP_200_OK,
                message="Package step deleted successfully",
            )
        else:
            return generate_response(custom_response=deleted_package_step.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)
