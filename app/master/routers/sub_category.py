from typing import Optional, Union

# Function to get a database session
from app.database.database import <PERSON><PERSON><PERSON><PERSON>, get_db
from app.master.services.sub_category import SubCategoryService
from app.master.validations.sub_category import (
    SubCategoryCreate,
    SubCategoryFilterPaginationResponse,
    SubCategoryListResponse,
    SubCategoryRetrievedResponse,
    SubCategoryUpdate,
)
from fastapi import APIRouter, Depends, File, Query, UploadFile, status
from sqlalchemy.orm import Session
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler
from utils.schema import Error

# Create an API router
router = APIRouter()


# Create an instance of the SubCategoryService
subcategory_service = SubCategoryService()


# Endpoint to get all subcategories
@router.get("/sub-categories", response_model=SubCategoryListResponse)
def get_subcategories(category_id: int = Query(None), db: Session = Depends(get_db)):
    try:
        kwargs = {"category_id": category_id}
        subcategories = subcategory_service.get_all_subcategories(db, **kwargs)
        return generate_response(
            data=subcategories,
            status_code=status.HTTP_200_OK,
            message="Sub-categories retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to filter subcategories
@router.get(
    "/sub-categories/filter", response_model=SubCategoryFilterPaginationResponse
)
async def filter_subcategories(
    code: Optional[str] = Query(None, min_length=2),
    name: Optional[str] = Query(None, min_length=3),
    db: Session = Depends(get_db),
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1),
    order_by: str = None,
    category_name: str = Query(None, min_length=3),
    scheme: str = Query(None, min_length=3),
):
    try:
        filtered_subcategories, total_count = (
            subcategory_service.filter_and_sort_sub_categories(
                db=db,
                code=code,
                name=name,
                page=page,
                limit=limit,
                category_name=category_name,
                scheme=scheme,
                order_by=order_by,
            )
        )
        return generate_response(
            total_count=total_count,
            data=filtered_subcategories,
            status_code=status.HTTP_200_OK,
            message="Sub-categories filtered successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to get a specific subcategory by ID
@router.get(
    "/sub-categories/{subcategory_id}",
    response_model=Union[SubCategoryRetrievedResponse, Error],
)
def get_subcategory(subcategory_id: int, db: Session = Depends(get_db)):
    try:
        content = subcategory_service.get_subcategory_or_raise(
            subcategory_id=subcategory_id, db=db
        )
        return generate_response(
            data=content,
            status_code=status.HTTP_200_OK,
            message="Sub-category retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to create a new subcategory
@router.post("/sub-categories", response_model=SubCategoryRetrievedResponse)
async def create_subcategory(
    subcategory: SubCategoryCreate, db: Session = Depends(get_db)
):
    try:
        created_subcategory = subcategory_service.create_subcategory(subcategory, db)
        return generate_response(
            data=created_subcategory,
            status_code=status.HTTP_201_CREATED,
            message="Sub-category created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to update a subcategory by ID
@router.put(
    "/sub-categories/{subcategory_id}",
    response_model=Union[SubCategoryRetrievedResponse, Error],
)
async def update_subcategory(
    subcategory_id: int,
    subcategory: SubCategoryUpdate,
    db: Session = Depends(get_db),
):
    try:
        updated_subcategory = subcategory_service.update_subcategory(
            subcategory_id, subcategory, db
        )
        return generate_response(
            data=updated_subcategory,
            status_code=status.HTTP_200_OK,
            message="Sub-category updated successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to delete a subcategory by ID
@router.delete(
    "/sub-categories/{subcategory_id}",
)
async def delete_subcategory(
    subcategory_id: int, force_delete: bool = False, db: Session = Depends(get_db)
):
    try:
        is_deleted, deleted_subcategory = subcategory_service.delete_subcategory(
            subcategory_id, db, force_delete
        )
        if is_deleted:
            return generate_response(
                status_code=status.HTTP_200_OK,
                message="Sub-category deleted successfully",
            )
        else:
            return generate_response(custom_response=deleted_subcategory.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to upload subcategories from a file
@router.post("/sub-categories/bulk-upload")
async def upload_subcategories(
    file: UploadFile = File(...), db: Session = Depends(get_db)
):
    try:
        contents = await file.read()
        subcategories_created = subcategory_service.bulk_insert_subcategories(
            contents=contents
        )
        return generate_response(
            data=subcategories_created,
            status_code=status.HTTP_201_CREATED,
            message="Sub-categories created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)
