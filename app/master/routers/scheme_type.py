from fastapi import APIRouter, Depends, status
from sqlalchemy.orm import Session

# Function to get a database session
from app.database.database import Session<PERSON><PERSON><PERSON>, get_db
from app.master.services.scheme_type import SchemeTypeService
from app.master.validations.scheme_type import (
    SchemeTypeCreate,
    SchemeTypeListResponse,
    SchemeTypeRetrievedResponse,
    SchemeTypeUpdate,
)
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler

# Create an API router
router = APIRouter()


# Create an instance of the SchemeTypeService
scheme_type_service = SchemeTypeService()


# Endpoint to get all scheme types
@router.get("/schemes", response_model=SchemeTypeListResponse)
def get_scheme_types(db: Session = Depends(get_db)):
    try:
        scheme_types = scheme_type_service.get_all_scheme_types(db)
        return generate_response(
            data=scheme_types,
            status_code=status.HTTP_200_OK,
            message="Scheme types retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to get a specific scheme type by ID
@router.get("/schemes/{scheme_type_id}")
def get_scheme_type(scheme_type_id: int, db: Session = Depends(get_db)):
    try:
        is_exists, content = scheme_type_service.get_scheme_type_by_id(
            scheme_type_id, db
        )
        if is_exists:
            return generate_response(data=content, status_code=status.HTTP_200_OK)
        else:
            return generate_response(custom_response=content.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to create a new scheme type
@router.post("/schemes", response_model=SchemeTypeRetrievedResponse)
async def create_scheme_type(
    scheme_type: SchemeTypeCreate, db: Session = Depends(get_db)
):
    try:
        created_scheme_type = scheme_type_service.create_scheme_type(scheme_type, db)
        return generate_response(
            data=created_scheme_type,
            status_code=status.HTTP_201_CREATED,
            message="Scheme type created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to update a scheme type by ID
@router.put("/schemes/{scheme_type_id}")
async def update_scheme_type(
    scheme_type_id: int, scheme_type: SchemeTypeUpdate, db: Session = Depends(get_db)
):
    try:
        is_updated, updated_scheme_type = scheme_type_service.update_scheme_type(
            scheme_type_id, scheme_type, db
        )
        if is_updated:
            return generate_response(
                data=updated_scheme_type, status_code=status.HTTP_200_OK
            )
        else:
            return generate_response(custom_response=updated_scheme_type.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to delete a scheme type by ID
@router.delete("/schemes/{scheme_type_id}")
async def delete_scheme_type(scheme_type_id: int, db: Session = Depends(get_db)):
    try:
        is_deleted, deleted_scheme_type = scheme_type_service.delete_scheme_type(
            scheme_type_id, db
        )
        if is_deleted:
            return generate_response(
                status_code=status.HTTP_200_OK,
                message="Scheme type deleted successfully",
            )
        else:
            return generate_response(custom_response=deleted_scheme_type.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)
