from typing import List, Optional, Union

from fastapi import APIRouter, Depends, File, Query, UploadFile, status
from sqlalchemy.orm import Session

# Function to get a database session
from app.database.database import SessionLocal, get_db
from app.master.services.package import PackageExcelUtil, PackageService
from app.master.validations.package import (
    PackageCreate,
    PackageDropdownResponse,
    PackageFilterPaginationResponse,
    PackageListResponse,
    PackageRetrievedResponse,
    PackageUpdate,
)
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler
from utils.schema import Error

# Create an API router
router = APIRouter()


# Create an instance of the PackageService
package_service = PackageService()


# Endpoint to get all packages
@router.get("/packages", response_model=PackageListResponse)
def get_packages(db: Session = Depends(get_db)):
    try:
        packages = package_service.get_all_packages(db)
        return generate_response(
            data=packages,
            status_code=status.HTTP_200_OK,
            message="Packages retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to filter packages
@router.get("/packages/filter", response_model=PackageFilterPaginationResponse)
async def filter_packages(
    package_amount: Optional[int] = None,
    procedure_code: Optional[str] = Query(None, min_length=1),
    verbose_code: Optional[str] = Query(None, min_length=1),
    procedure_name: Optional[str] = Query(None, min_length=3),
    category_name: str = Query(None, min_length=3),
    sub_category_name: str = Query(None, min_length=3),
    category_code: str = Query(None),
    sub_category_code: str = Query(None),
    scheme: int = Query(None),
    db: Session = Depends(get_db),
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1),
    order_by: str = None,
):
    try:
        filtered_packages, total_count = package_service.filter_and_sort_packages(
            db=db,
            procedure_code=procedure_code,
            procedure_name=procedure_name,
            package_amount=package_amount,
            category_name=category_name,
            sub_category_name=sub_category_name,
            category_code=category_code,
            sub_category_code=sub_category_code,
            scheme=scheme,
            order_by=order_by,
            page=page,
            size=size,
            verbose_code=verbose_code,
        )
        return generate_response(
            total_count=total_count,
            data=filtered_packages,
            status_code=status.HTTP_200_OK,
            message="Packages filtered successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to get a specific package by ID
@router.get(
    "/packages/{package_id}",
    response_model=Union[PackageRetrievedResponse, Error],
)
def get_package(package_id: int, db: Session = Depends(get_db)):
    try:
        package = package_service.get_package_by_id(package_id, db)
        return generate_response(
            data=package,
            status_code=status.HTTP_200_OK,
            message="Package retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to create a new package
@router.post("/packages", response_model=PackageRetrievedResponse)
async def create_package(package: PackageCreate, db: Session = Depends(get_db)):
    try:
        created_package = package_service.create_package(package, db)
        return generate_response(
            data=created_package,
            status_code=status.HTTP_201_CREATED,
            message="Package created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to update a package by ID
@router.put(
    "/packages/{package_id}",
    response_model=Union[PackageRetrievedResponse, Error],
)
async def update_package(
    package_id: int,
    package: PackageUpdate,
    db: Session = Depends(get_db),
):
    try:
        updated_package = package_service.update_package(package_id, package, db)
        return generate_response(
            data=updated_package,
            status_code=status.HTTP_200_OK,
            message="Package updated successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to delete a package by ID
@router.delete(
    "/packages/{package_id}",
)
async def delete_package(
    package_id: int, force_delete: bool = False, db: Session = Depends(get_db)
):
    try:
        is_deleted, deleted_package = package_service.delete_package(
            package_id, force_delete, db
        )
        if is_deleted:
            return generate_response(
                status_code=status.HTTP_200_OK,
                message="Package deleted successfully",
            )
        else:
            return generate_response(custom_response=deleted_package.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to upload packages from a file
@router.post("/packages/upload")
async def upload_packages(
    file: UploadFile = File(...),
    scheme_id: int = Query(...),
    upload_type: str = Query(..., regex="^(fresh|additional)$"),
    db: Session = Depends(get_db),
):
    """
    Upload packages with Fresh or Additional logic.

    - Fresh: Marks all existing procedures as inactive and uploads new ones as active.
    - Additional: Retains existing procedures, deactivates duplicates, and uploads new ones as active.
    """
    try:
        contents = await file.read()
        package_excel_util = PackageExcelUtil(contents=contents, scheme_id=scheme_id)
        packages_created = package_excel_util.create_packages(db=db, upload_type=upload_type)

        return generate_response(
            status_code=status.HTTP_201_CREATED,
            message="Packages uploaded successfully",
        )
    except Exception as e:
        import traceback
        traceback.print_exc()
        return ExceptionHandler().handle_exception(e)


# Endpoint to upload packages with Fresh or Additional logic
@router.post("/packages/upload-bulk")
async def upload_packages_bulk(
    file: UploadFile = File(...),
    scheme_id: int = Query(...),
    upload_type: str = Query(..., regex="^(fresh|additional)$"),
    db: Session = Depends(get_db),
):
    """
    Upload packages in bulk with Fresh or Additional logic.

    - Fresh: Marks all existing procedures as inactive and uploads new ones as active.
    - Additional: Retains existing procedures, deactivates duplicates, and uploads new ones as active.
    """
    try:
        contents = await file.read()
        package_excel_util = PackageExcelUtil(contents=contents, scheme_id=scheme_id)

        # Upload new procedures and handle duplicates for "additional" logic
        packages_created = package_excel_util.create_packages(db=db, upload_type=upload_type)

        return generate_response(
            status_code=status.HTTP_201_CREATED,
            message="Packages uploaded successfully",
        )
    except Exception as e:
        import traceback
        traceback.print_exc()
        return ExceptionHandler().handle_exception(e)


# @router.post("/uploadfile/")
# async def create_upload_file(file: UploadFile = File(...)):
#     # if file.content_type != 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
#     #     raise HTTPException(status_code=400, detail="Invalid file format. Please upload an Excel file.")

#     contents = await file.read()
#     df = pd.read_excel(contents)

#     session = SessionLocal()

#     try:
#         for index, row in df.iterrows():
#             category_code = row['Category Code'].strip()
#             category_name = row['Category Name'].strip()
#             sub_category_code = row['Sub Category Code'].strip().replace(category_code, '')
#             sub_category_name = row['Sub Category Name'].strip()
#             procedure_code = row['Procedure Code'].strip()
#             package_amount = row['Package amount']
#             procedure_name = row['Procedure Name'].strip()

#             # Check or create Category
#             category = session.query(CategoryDB).filter_by(code=category_code, name=category_name).first()
#             if not category:
#                 category = CategoryDB(code=category_code, name=category_name)
#                 session.add(category)
#                 session.commit()

#             # Check or create SubCategory
#             sub_category = session.query(SubCategoryDB).filter_by(code=sub_category_code, name=sub_category_name, category_id=category.id).first()
#             if not sub_category:
#                 sub_category = SubCategoryDB(code=sub_category_code, name=sub_category_name, category_id=category.id)
#                 session.add(sub_category)
#                 session.commit()

#             # Check or create Package
#             package = session.query(PackageDB).filter_by(
#                 procedure_code=procedure_code,
#                 procedure_name=procedure_name,
#                 package_amount=package_amount,
#                 category_id=category.id,
#                 sub_category_id=sub_category.id
#             ).first()
#             if not package:
#                 package = PackageDB(
#                     procedure_code=procedure_code,
#                     procedure_name=procedure_name,
#                     package_amount=package_amount,
#                     category_id=category.id,
#                     sub_category_id=sub_category.id
#                 )
#                 session.add(package)
#                 session.commit()
#     finally:
#         session.close()

#     return {"status": "success"}


@router.get("/dropdown/packages", response_model=PackageDropdownResponse)
async def get_packages_drop_down(
    scheme_type_id: int = Query(None),
    category_id: int = Query(None),
    sub_category_id: int = Query(None),
    verbose_code: str = Query(None),
    db: Session = Depends(get_db),
):
    try:
        kwargs = {
            "scheme_type_id": scheme_type_id,
            "category_id": category_id,
            "sub_category_id": sub_category_id,
            "verbose_code": verbose_code
        }
        packages = package_service.get_all_packages_for_dropdown(db, **kwargs)
        return generate_response(
            data=packages,
            status_code=status.HTTP_200_OK,
            message="Packages retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)
