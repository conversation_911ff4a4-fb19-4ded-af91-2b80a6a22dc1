from typing import List, Optional, Union

from fastapi import APIRouter, Depends, File, UploadFile, status
from sqlalchemy.orm import Session

# Function to get a database session
from app.database.database import SessionLocal, get_db
from app.master.services.package_step_document import PackageStepDocumentService
from app.master.validations.package_step_document import (
    PackageStepDocumentCreate,
    PackageStepDocumentListResponse,
    PackageStepDocumentMapping,
    PackageStepDocumentRetrieveResponse,
)
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler
from utils.schema import Error

# Create an API router
router = APIRouter()


# Create an instance of the PackageStepDocumentService
package_step_document_service = PackageStepDocumentService()


# Endpoint to get all package step documents
@router.get("/package-step-documents", response_model=PackageStepDocumentListResponse)
def get_package_step_documents(db: Session = Depends(get_db)):
    try:
        package_step_documents = (
            package_step_document_service.get_all_package_step_documents(db)
        )
        return generate_response(
            data=package_step_documents,
            status_code=status.HTTP_200_OK,
            message="Package step documents retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to create a new package step docuemnt
@router.post(
    "/package-step-documents", response_model=PackageStepDocumentRetrieveResponse
)
async def create_package_step_document(
    package_step_document: PackageStepDocumentCreate, db: Session = Depends(get_db)
):
    try:
        created_package_step_document = (
            package_step_document_service.create_package_step_document_base(
                package_step_document.dict(), db
            )
        )
        return generate_response(
            data=created_package_step_document,
            status_code=status.HTTP_201_CREATED,
            message="Package step document created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to delete a package step document by ID
@router.delete(
    "/package-step-documents/{package_step_document_id}",
)
async def delete_package_step_document(
    package_step_document_id: int, db: Session = Depends(get_db)
):
    try:
        is_deleted = package_step_document_service.delete_package_step_document(
            package_step_document_id, db
        )
        return generate_response(
            status_code=status.HTTP_200_OK,
            message="Package step document deleted successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.post("/package-steps-documents/{package_id}")
async def create_or_update_package_step_documents(
    package_id: int,
    package_step_documents: List[PackageStepDocumentMapping],
    db: Session = Depends(get_db),
):
    try:
        package_step_document_service = PackageStepDocumentService()
        created_package_step_documents = (
            package_step_document_service.create_package_step_documents(
                package_id, package_step_documents
            )
        )
        return generate_response(
            status_code=status.HTTP_201_CREATED,
            message="Procedure step documents have been successfully updated.",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)
