from app.database.database import Base

from sqlalchemy import <PERSON>umn, Integer, ForeignKey


class SchemeHospitalDB(Base):
    """Mapping for Hospital and Scheme Table"""

    __tablename__ = "scheme_hospital_master"
    id = Column(Integer, primary_key=True, autoincrement=True)
    hospital_id = Column(Integer, ForeignKey("hospital_master.id"), nullable=False)
    scheme_id = Column(Integer, ForeignKey("scheme_type_master.id"), nullable=False)
