from sqlalchemy import Column, Integer, String
from sqlalchemy.orm import relationship

from app.database.database import Base


class SchemeType(Base):
    """
    Represents a schem_type_master in the database.
    """

    __tablename__ = "scheme_type_master"
    id = Column(Integer, primary_key=True, index=True)
    type = Column(String(255), nullable=False, index=True, unique=True)

    categories = relationship("CategoryDB", back_populates="scheme_type")

    documents = relationship(
        "DocumentMaster",
        secondary="scheme_document_master",
        back_populates="schemes",
    )

    hospitals = relationship(
        "HospitalDB",
        secondary="scheme_hospital_master",
        back_populates="schemes",
    )

    def __repr__(self):
        return f"{self.type}"
