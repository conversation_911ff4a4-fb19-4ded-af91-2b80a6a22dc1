from app.database.database import Base

from sqlalchemy import <PERSON>um<PERSON>, Inte<PERSON>, String, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy import UniqueConstraint


class CategoryDB(Base):
    """Category Master Table"""

    __tablename__ = "category_master"

    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(String(20), nullable=False, index=True)
    name = Column(String(300), nullable=False, index=True)
    scheme_type_id = Column(Integer, ForeignKey("scheme_type_master.id"))

    package = relationship("PackageDB", back_populates="categories")
    scheme_type = relationship("SchemeType", back_populates="categories")
    sub_categories = relationship("SubCategoryDB", back_populates="category")

    __table_args__ = (
        UniqueConstraint("code", "name", "scheme_type_id", name="uq_category"),
    )
