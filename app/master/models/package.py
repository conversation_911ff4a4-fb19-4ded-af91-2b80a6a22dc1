from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, ForeignKey, Table, MetaData
from sqlalchemy.orm import relationship
from sqlalchemy import UniqueConstraint
from enum import Enum
from sqlalchemy import Enum as EnumType
from sqlalchemy.ext.hybrid import hybrid_property
from app.database.database import Base
from sqlalchemy.ext.associationproxy import association_proxy


class StepType(Enum):
    """Step Type Enum with Display Names"""
    PRE_INVESTIGATION = "Pre Investigation"
    PRE_AUTHORIZATION = "Pre Authorization"
    TREATMENT = "On Treatment day"
    DISCHARGE = "Discharge"
    CLAIM = "Post Investigation and Claim"

    @property
    def display_name(self) -> str:
        return self.value


class StepMaster(Base):
    """
    Represents a step_master in the database.
    """

    __tablename__ = "step_master"
    id = Column(Integer, primary_key=True, index=True)
    type = Column(EnumType(StepType), nullable=False, index=True, unique=True)
    order = Column(Integer, nullable=False, index=True, unique=True)

    packages = relationship(
        "PackageDB",
        secondary="package_step_master",
        back_populates="steps",
        overlaps="package_steps,steps",  # Add this line
    )
    package_steps = relationship("PackageStepMasterDB", back_populates="steps")


class PackageDB(Base):
    """Package Table"""

    __tablename__ = "package_master"
    id = Column(Integer, primary_key=True, autoincrement=True)
    procedure_code = Column(String(20), nullable=False)
    procedure_name = Column(String(500), nullable=False)
    package_amount = Column(Integer, nullable=False)

    # Foreign Key
    category_id = Column(Integer, ForeignKey("category_master.id"))
    sub_category_id = Column(
        Integer, ForeignKey("sub_category_master.id"), nullable=True
    )
    is_deleted = Column(Boolean, default=False, nullable=False)
    categories = relationship("CategoryDB", back_populates="package")
    sub_categories = relationship("SubCategoryDB", back_populates="package")
    steps = relationship(
        "StepMaster",
        secondary="package_step_master",
        back_populates="packages",
        overlaps="package_steps,steps",  # Add this line
    )
    package_steps = relationship(
        "PackageStepMasterDB", back_populates="package")
    cases = relationship("Case", back_populates="package")
    __table_args__ = (
        UniqueConstraint(
            "procedure_code",
            "procedure_name",
            "package_amount",
            "category_id",
            "sub_category_id",
            name="uq_package",
        ),
    )
    
    @hybrid_property
    def verbose_code(self):
        cat_code = self.categories.code if self.categories else ""
        subcat_code = self.sub_categories.code if self.sub_categories else ""
        # Define the MJP_JAY_SCHEME_ID constant at the class level
        MJP_JAY_SCHEME_ID = 1  # Update this with the actual ID value
        # Get the scheme_type_id from the category if available
        scheme_type_id = None
        if self.categories and hasattr(self.categories, 'scheme_type_id'):
            scheme_type_id = self.categories.scheme_type_id
        
        # Apply the conditional formatting based on scheme type
        if scheme_type_id == MJP_JAY_SCHEME_ID:
            # For MJP_JAY_SCHEME_ID, use format: category_code + subcategory_code + . + procedure_code
            if subcat_code:
                return f"{cat_code}{subcat_code}.{self.procedure_code}"
            return f"{cat_code}.{self.procedure_code}"
        else:
            # For other schemes, just return the procedure_code
            return self.procedure_code


class PackageStepMasterDB(Base):
    """Package Step Master Table"""

    __tablename__ = "package_step_master"

    id = Column(Integer, primary_key=True, autoincrement=True)
    package_id = Column(Integer, ForeignKey("package_master.id"))
    step_id = Column(Integer, ForeignKey("step_master.id"))

    package_step_document = relationship(
        "PackageStepDocumentDB", back_populates="package_step_master"
    )
    steps = relationship(
        "StepMaster", back_populates="package_steps", overlaps="packages,steps"
    )  # Add this line
    package = relationship(
        "PackageDB", back_populates="package_steps", overlaps="packages,steps"
    )
    case_step = relationship(
        "CaseStep", back_populates="case_step_package_step_master")


class PackageStepDocumentDB(Base):
    __tablename__ = "package_step_document_master"

    id = Column(Integer, primary_key=True, autoincrement=True)
    package_step_id = Column(
        Integer, ForeignKey("package_step_master.id"), nullable=False
    )
    document_id = Column(Integer, ForeignKey(
        "document_master.id"), nullable=False)
    # Define relationships
    package_step_master = relationship(
        "PackageStepMasterDB", back_populates="package_step_document"
    )
    document_master = relationship(
        "DocumentMaster", back_populates="package_step_document"
    )

    __table_args__ = (
        UniqueConstraint(
            "package_step_id",
            "document_id",
            name="uq_package_step_document",
        ),
    )
