from app.database.database import Base

from sqlalchemy import <PERSON>um<PERSON>, Float, Inte<PERSON>, String, Boolean, DECIMAL
from sqlalchemy.orm import relationship
from sqlalchemy import UniqueConstraint


class HospitalDB(Base):
    """Hospital Master Table"""

    __tablename__ = "hospital_master"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(300), nullable=False, index=True)
    short_name = Column(String(100), nullable=True)
    latitude = Column(DECIMAL(18, 15), nullable=True)
    longitude = Column(DECIMAL(18, 15), nullable=True)
    is_deleted = Column(Boolean, default=False, nullable=False)

    users = relationship(
        "UserDB", secondary="user_hospital", back_populates="hospitals"
    )
    # Temporarily comment out the problematic relationship
    # schemes = relationship(
    #     "SchemeType",
    #     secondary="scheme_hospital_master",
    #     back_populates="hospitals",
    # )

    cases = relationship("Case", back_populates="hospital")
