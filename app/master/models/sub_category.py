from sqlalchemy import Column, Inte<PERSON>, String, ForeignKey
from sqlalchemy.orm import relationship

from app.database.database import Base


class SubCategoryDB(Base):
    """Sub Category Master Table"""

    __tablename__ = "sub_category_master"

    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(String(20), nullable=False, index=True)
    name = Column(String(300), nullable=False, index=True)
    category_id = Column(Integer, ForeignKey("category_master.id"))

    category = relationship("CategoryDB", back_populates="sub_categories")
    package = relationship("PackageDB", back_populates="sub_categories")
