from datetime import datetime

from sqlalchemy import Column, Integer, String, <PERSON>olean, DateTime, ForeignKey
from sqlalchemy.orm import relationship

from app.database.database import Base


class DocumentMaster(Base):
    """Represents a document in the database."""

    __tablename__ = "document_master"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(200), nullable=False, index=True)
    is_required = Column(Boolean, default=True)
    is_geotag_required = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)
    package_step_document = relationship(
        "PackageStepDocumentDB", back_populates="document_master"
    )
    case_step_documents = relationship(
        "CaseStepDocument", back_populates="document_master"
    )

    schemes = relationship(
        "SchemeType",
        secondary="scheme_document_master",
        back_populates="documents",
    )

    class Config:
        from_attributes = True
