from app.database.database import Base

from sqlalchemy import <PERSON>umn, Integer, ForeignKey


class SchemeDocumentDB(Base):
    """Mapping for Document and Scheme Table"""

    __tablename__ = "scheme_document_master"
    id = Column(Integer, primary_key=True, autoincrement=True)
    document_id = Column(Integer, ForeignKey("document_master.id"), nullable=False)
    scheme_id = Column(Integer, ForeignKey("scheme_type_master.id"), nullable=False)
