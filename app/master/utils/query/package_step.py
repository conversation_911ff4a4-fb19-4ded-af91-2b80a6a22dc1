INSERT_ALL_PACKAGE_STEPS_QUERY = """
INSERT INTO package_step_master (step_id, package_id)
SELECT sm.id, pm.id
FROM step_master sm
CROSS JOIN package_master pm
LEFT JOIN package_step_master psm
ON sm.id = psm.step_id AND pm.id = psm.package_id
WHERE psm.step_id IS NULL AND psm.package_id IS NULL
"""

CREATE_OPTIONAL_DOCUMENTS_QUERY = """
INSERT INTO document_master (name, is_required, is_geotag_required, created_at, updated_at)
SELECT CONCAT('Optional Document ', n) AS name, FALSE AS is_required, FALSE AS is_geotag_required, NOW() AS created_at, NOW() AS updated_at
FROM (SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7) numbers
WHERE NOT EXISTS (
    SELECT 1 FROM document_master WHERE name = CONCAT('Optional Document ', n)
);
"""

CREATE_OPTIONAL_DOCUMENTS_FOR_PACKAGE_STEPS_QUERY = """
INSERT INTO package_step_document_master (package_step_id, document_id)
SELECT psm.id AS package_step_id, dm.id AS document_id
FROM package_step_master psm
CROSS JOIN document_master dm
WHERE dm.name LIKE 'Optional Document %' AND NOT EXISTS (SELECT 1 FROM package_step_document_master psdm WHERE psdm.package_step_id = psm.id AND psdm.document_id = dm.id);
"""
