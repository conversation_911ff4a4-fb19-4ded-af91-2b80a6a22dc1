from sqlalchemy.ext.asyncio import AsyncSession

from ..query.package_step import INSERT_ALL_PACKAGE_STEPS_QUERY,CREATE_OPTIONAL_DOCUMENTS_FOR_PACKAGE_STEPS_QUERY,CREATE_OPTIONAL_DOCUMENTS_QUERY
from ..query.scheme_document_master import INSERT_DOCUMENT_SCHEME_MASTER_QUERY
from .base import execute_query, execute_schema_query


def insert_all_package_steps(db: AsyncSession):
    return execute_query(db, INSERT_ALL_PACKAGE_STEPS_QUERY)

def create_optional_documents(db: AsyncSession):
    """
    Creates optional documents in the document_master table if they do not already exist.

    Args:
        db (AsyncSession): The database session.
    """
    return execute_query(db, CREATE_OPTIONAL_DOCUMENTS_QUERY)


def create_optional_documents_for_package_steps(db: AsyncSession):
    """
    Creates optional documents for package steps in the package_step_document_master table.

    Args:
        db (AsyncSession): The database session.
    """
    return execute_query(db, CREATE_OPTIONAL_DOCUMENTS_FOR_PACKAGE_STEPS_QUERY)

def insert_all_documents_scheme_master(db: AsyncSession, scheme_id: int):
    """
    Inserts all documents into the scheme_document_master table for the given scheme_id.

    Args:
        db (AsyncSession): The database session.
        scheme_id (int): The ID of the scheme.
    """
    query = INSERT_DOCUMENT_SCHEME_MASTER_QUERY.replace(":scheme_id", str(scheme_id))
    return execute_schema_query(db, query=query)