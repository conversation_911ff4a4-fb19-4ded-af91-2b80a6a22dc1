from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession


def execute_query(db: AsyncSession, query: str):
    try:
        result = db.execute(text(query))
        db.commit()
        return result.rowcount
    except Exception as e:
        db.rollback()
        raise e

def execute_schema_query(db: AsyncSession, query: str):
    try:
        statements = query.strip().split(';')
        for statement in statements:
            if statement.strip():
                db.execute(text(statement))
        db.commit()
    except Exception as e:
        db.rollback()
        raise e
    
def execute_select_query(db: AsyncSession, query: str):
    try:
        result = db.execute(text(query))
        db.commit()
        return result.fetchall()
    except Exception as e:
        db.rollback()
        raise e

