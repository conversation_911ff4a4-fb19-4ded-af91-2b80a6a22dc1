import os
import time
from contextlib import closing, contextmanager
from urllib.parse import quote_plus

from dotenv import load_dotenv
from mysql import connector
from sqlalchemy import create_engine
from sqlalchemy.exc import OperationalError
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session, sessionmaker

from utils.logger import setup_logger

# Load environment variables from .env file
load_dotenv()

logger = setup_logger("database_logger", "database.log")


db_user = os.getenv("DB_USER")
db_password = os.getenv("DB_PASSWORD")
db_host = os.getenv("DB_HOST")
db_name = os.getenv("DB_NAME")
app_env = os.getenv("APP_ENV", "development")  # Default to development if not set

password = quote_plus(db_password)
# DB_URL = f"mysql+mysqlconnector://{db_user}:{password}@{db_host}/{db_name}"

# engine = create_engine(
#     DB_URL, pool_size=5, max_overflow=10, pool_recycle=1800, pool_timeout=30
# )
# engine = create_engine(
#     DB_URL,
#     pool_size=10,  # Increase connection pool size
#     max_overflow=20,  # Allow more connections to be created
#     pool_recycle=1800,  # Recycle connections every 1800 seconds
#     pool_pre_ping=True,  # Automatically check if connection is alive before using
#     pool_timeout=30,  # Raise an exception after 30 seconds if no connection is available
# )


# SSL connection - only used in production
ssl_args = {
    "ssl_ca": os.getenv("DB_SSL_CA"),  # Path to the certificate authority file
    "ssl_cert": os.getenv("DB_SSL_CERT"),  # Path to the public key file
    "ssl_key": os.getenv("DB_SSL_KEY"),  # Path to the private key file
}

DB_URL = f"mysql+mysqlconnector://{db_user}:{password}@{db_host}/{db_name}"

# Common engine configuration parameters
engine_config = {
    "pool_size": 10,  # Increase connection pool size
    "max_overflow": 20,  # Allow more connections to be created
    "pool_recycle": 1800,  # Recycle connections every 1800 seconds
    "pool_pre_ping": True,  # Automatically check if connection is alive before using
    "pool_timeout": 30,  # Raise an exception after 30 seconds if no connection is available
}

# Add MySQL-specific connection arguments for better stability
mysql_connect_args = {
    "connect_timeout": 10,  # Connection timeout
    "autocommit": False,    # Explicit transaction control
    "charset": "utf8mb4",   # Proper charset
    "use_unicode": True,    # Use unicode
}

# Only add SSL configuration in production environment
if app_env.lower() == "production":
    logger.info("Using SSL connection for database in production environment")
    mysql_connect_args.update(ssl_args)
    engine_config["connect_args"] = mysql_connect_args
else:
    logger.info(f"Using non-SSL connection for database in {app_env} environment")
    engine_config["connect_args"] = mysql_connect_args
# Create engine with appropriate configuration
engine = create_engine(DB_URL, **engine_config)

Base = declarative_base()

SessionLocal = sessionmaker(bind=engine)


def get_db():

    try:
        logger.debug("Creating new DB session")
        db = SessionLocal()
        if not db.is_active:
            logger.debug("DB session is not active")
            try:
                db = SessionLocal()
            except Exception as e:
                logger.debug("Exception occurred while creating DB session:", e)
                raise e
            logger.debug("Creating new DB session")
        yield db
    except Exception as e:
        logger.debug("Rolling back DB session due to exception:", e)
        db.rollback()
        logger.debug("This Is Test")
        raise e
    finally:
        logger.debug("Closing DB session")
        db.close()
        logger.debug("Connection Closed")


@contextmanager
def get_db_session():
    db = SessionLocal()
    try:
        yield db

    except Exception as e:
        db.rollback()
    finally:
        db.close()


@contextmanager
def transaction(db: Session):
    try:
        yield  # Run the transaction
        db.commit()  # Commit if successful
    except Exception as e:
        db.rollback()  # Rollback on error
        logger.debug("Rolling back DB session due to exception:", e)
        raise e
    finally:
        db.close()  # Ensure session is closed


class DatabaseManager:
    MAX_RETRIES = 5
    INITIAL_DELAY = 2  # Exponential backoff start delay

    @staticmethod
    @contextmanager
    def get_session():
        retries = 0
        delay = DatabaseManager.INITIAL_DELAY

        while retries < DatabaseManager.MAX_RETRIES:
            session = SessionLocal()
            try:
                yield session
                session.commit()
                return
            except OperationalError:
                session.rollback()
                retries += 1
                time.sleep(delay)
                delay *= 2  # Exponential backoff
            finally:
                session.close()  # Ensure session is closed after each retry

        raise Exception("Database connection failed after multiple attempts.")
