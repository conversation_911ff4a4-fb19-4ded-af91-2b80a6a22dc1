import os
import time
from contextlib import closing, contextmanager
from urllib.parse import quote_plus

from dotenv import load_dotenv
from fastapi import HTTPException
from mysql import connector
from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session, sessionmaker

from utils.logger import setup_logger

# Load environment variables from .env file
load_dotenv()

logger = setup_logger("database_logger", "database.log")


db_user = os.getenv("DB_USER")
db_password = os.getenv("DB_PASSWORD")
db_host = os.getenv("DB_HOST")
db_port = os.getenv("DB_PORT", "3306")  # Default to 3306 if not set
db_name = os.getenv("DB_NAME")
app_env = os.getenv("APP_ENV", "development")  # Default to development if not set

password = quote_plus(db_password)
# DB_URL = f"mysql+mysqlconnector://{db_user}:{password}@{db_host}/{db_name}"

# engine = create_engine(
#     DB_URL, pool_size=5, max_overflow=10, pool_recycle=1800, pool_timeout=30
# )
# engine = create_engine(
#     DB_URL,
#     pool_size=10,  # Increase connection pool size
#     max_overflow=20,  # Allow more connections to be created
#     pool_recycle=1800,  # Recycle connections every 1800 seconds
#     pool_pre_ping=True,  # Automatically check if connection is alive before using
#     pool_timeout=30,  # Raise an exception after 30 seconds if no connection is available
# )


# SSL connection - only used in production
ssl_args = {
    "ssl_ca": os.getenv("DB_SSL_CA"),  # Path to the certificate authority file
    "ssl_cert": os.getenv("DB_SSL_CERT"),  # Path to the public key file
    "ssl_key": os.getenv("DB_SSL_KEY"),  # Path to the private key file
}

DB_URL = f"mysql+mysqlconnector://{db_user}:{password}@{db_host}:{db_port}/{db_name}"

# Common engine configuration parameters
engine_config = {
    "pool_size": 5,  # Reduce connection pool size for better management
    "max_overflow": 10,  # Allow fewer overflow connections
    "pool_recycle": 3600,  # Recycle connections every hour
    "pool_pre_ping": True,  # Automatically check if connection is alive before using
    "pool_timeout": 20,  # Reduce timeout to fail faster
    "echo": False,  # Set to True for debugging SQL queries
    "echo_pool": False,  # Set to True for debugging connection pool
}

# Only add SSL configuration in production environment
if app_env.lower() == "production":
    logger.info("Using SSL connection for database in production environment")
    engine_config["connect_args"] = ssl_args
else:
    logger.info(f"Using non-SSL connection for database in {app_env} environment")
# Create engine with appropriate configuration
engine = create_engine(DB_URL, **engine_config)

Base = declarative_base()

SessionLocal = sessionmaker(bind=engine)


def get_db():
    """
    Dependency function to get database session with improved error handling.
    """
    db = None
    try:
        logger.debug("Creating new DB session")
        db = SessionLocal()

        # Test the connection by executing a simple query
        try:
            db.execute(text("SELECT 1"))
            logger.debug("DB session connection verified")
        except OperationalError as e:
            logger.error("Database connection test failed: %s", str(e))
            if db:
                db.close()
            # Try to create a new session
            db = SessionLocal()
            db.execute(text("SELECT 1"))
            logger.debug("DB session recreated successfully")

        yield db

    except OperationalError as e:
        logger.error("Database operational error: %s", str(e))
        if db:
            try:
                db.rollback()
            except:
                pass
        raise HTTPException(
            status_code=503,
            detail="Database connection unavailable. Please try again later."
        )
    except Exception as e:
        logger.error("Database session error: %s", str(e), exc_info=True)
        if db:
            try:
                db.rollback()
            except:
                pass
        raise e
    finally:
        if db:
            try:
                logger.debug("Closing DB session")
                db.close()
                logger.debug("DB session closed successfully")
            except Exception as e:
                logger.error("Error closing DB session: %s", str(e))


@contextmanager
def get_db_session():
    """
    Context manager for database sessions with improved error handling.
    """
    db = None
    try:
        db = SessionLocal()

        # Test the connection
        try:
            db.execute(text("SELECT 1"))
        except OperationalError as e:
            logger.error("Database connection test failed in context manager: %s", str(e))
            if db:
                db.close()
            # Try to create a new session
            db = SessionLocal()
            db.execute(text("SELECT 1"))

        yield db

    except OperationalError as e:
        logger.error("Database operational error in context manager: %s", str(e))
        if db:
            try:
                db.rollback()
            except:
                pass
        raise
    except Exception as e:
        logger.error("Database session error in context manager: %s", str(e), exc_info=True)
        if db:
            try:
                db.rollback()
            except:
                pass
        raise
    finally:
        if db:
            try:
                db.close()
            except Exception as e:
                logger.error("Error closing DB session in context manager: %s", str(e))


@contextmanager
def transaction(db: Session):
    try:
        yield  # Run the transaction
        db.commit()  # Commit if successful
    except Exception as e:
        db.rollback()  # Rollback on error
        logger.debug("Rolling back DB session due to exception:", e)
        raise e
    finally:
        db.close()  # Ensure session is closed


class DatabaseManager:
    MAX_RETRIES = 3
    INITIAL_DELAY = 1  # Exponential backoff start delay

    @staticmethod
    @contextmanager
    def get_session():
        """
        Context manager with retry logic for database sessions.
        """
        retries = 0
        delay = DatabaseManager.INITIAL_DELAY
        session = None

        while retries < DatabaseManager.MAX_RETRIES:
            try:
                session = SessionLocal()

                # Test the connection
                session.execute(text("SELECT 1"))
                logger.debug("Database connection established successfully")

                yield session
                session.commit()
                return

            except OperationalError as e:
                logger.warning("Database connection attempt %d failed: %s", retries + 1, str(e))
                if session:
                    try:
                        session.rollback()
                    except:
                        pass
                    try:
                        session.close()
                    except:
                        pass
                    session = None

                retries += 1
                if retries < DatabaseManager.MAX_RETRIES:
                    logger.info("Retrying database connection in %d seconds...", delay)
                    time.sleep(delay)
                    delay *= 2  # Exponential backoff

            except Exception as e:
                logger.error("Unexpected database error: %s", str(e), exc_info=True)
                if session:
                    try:
                        session.rollback()
                    except:
                        pass
                raise
            finally:
                if session:
                    try:
                        session.close()
                    except Exception as e:
                        logger.error("Error closing session in DatabaseManager: %s", str(e))

        logger.error("Database connection failed after %d attempts", DatabaseManager.MAX_RETRIES)
        raise HTTPException(
            status_code=503,
            detail="Database service unavailable after multiple retry attempts"
        )


def check_database_health():
    """
    Check if the database connection is healthy.
    Returns True if healthy, False otherwise.
    """
    try:
        with get_db_session() as db:
            db.execute(text("SELECT 1"))
            logger.info("Database health check passed")
            return True
    except Exception as e:
        logger.error("Database health check failed: %s", str(e))
        return False


def create_tables():
    """
    Create all tables defined in the models.
    """
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error("Failed to create database tables: %s", str(e))
        raise
