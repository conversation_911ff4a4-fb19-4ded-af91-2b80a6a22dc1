"""
This module contains classes for generating authorization-related error messages.
"""

from utils.generator import ErrorGenerator
from app.exception.errors.authorisation import (
    GroupNotFoundError,
    GroupNameAlreadyExistsError,
    GroupAssociatedWithPermissionsError,
    GroupAssociatedWithRolesError,
    GroupPermissionAlreadyExistsError,
    GroupPermissionNotFoundError,
    GroupRoleAlreadyExistsError,
    GroupRoleNotFoundError,
    PermsNameAlreadyExistsError,
    PermsNotFoundError,
    PermissionAssociatedWithGroupError,
    OneOrMorePermissionsNotFoundError,
    RoleNameAlreadyExistsError,
    RoleNotFoundError,
    RoleNameAlreadyExistsError,
    RoleAssociatedWithGroupError,
    RoleAssociatedWithUsersError,
    UserRoleNotFoundError,
    UserRoleAlreadyExistsError,
    OneOrMoreRolesNotFoundError,
    EndpointNotFoundError,
    EndpointUrlAlreadyExistsError,
    EndpointUrlNotFoundError
    
)


class GroupExceptions:
    """A class that generates group-related error messages."""

    @staticmethod
    def generate_group_not_found_error(group_id: int):
        """Generates an error message for a group not found error."""
        return ErrorGenerator.generate_error_detail(
            type=GroupNotFoundError.TYPE,
            loc=GroupNotFoundError.LOC,
            msg=GroupNotFoundError.MSG,
            input=f"{group_id}",
        )

    @staticmethod
    def generate_group_name_already_exists_error(group_name: str):
        """Generates an error message for a group name already exists error."""
        return ErrorGenerator.generate_error_detail(
            type=GroupNameAlreadyExistsError.TYPE,
            loc=GroupNameAlreadyExistsError.LOC,
            msg=GroupNameAlreadyExistsError.MSG,
            input=f"{group_name}",
        )

    @staticmethod
    def generate_group_associated_with_permissions_error(group_id: int):
        """Generates an error message for a group associated with permissions error."""
        return ErrorGenerator.generate_error_detail(
            type=GroupAssociatedWithPermissionsError.TYPE,
            loc=GroupAssociatedWithPermissionsError.LOC,
            msg=GroupAssociatedWithPermissionsError.MSG,
            input=f"{group_id}",
        )


    @staticmethod
    def generate_group_associated_with_roles_error(group_id: int):
        """Generates an error message for a group associated with roles error."""
        return ErrorGenerator.generate_error_detail(
            type=GroupAssociatedWithRolesError.TYPE,
            loc=GroupAssociatedWithRolesError.LOC,
            msg=GroupAssociatedWithRolesError.MSG,
            input=f"{group_id}",
        )

class PermissionsExceptions:
    """A class that generates permissions-related error messages."""

    @staticmethod
    def generate_permission_not_found_error(permission_id: int):
        """Generates an error message for a role not found error."""
        return ErrorGenerator.generate_error_detail(
            type=PermsNotFoundError.TYPE,
            loc=PermsNotFoundError.LOC,
            msg=PermsNotFoundError.MSG,
            input=f"{permission_id}",
        )

    @staticmethod
    def generate_perms_name_already_exists_error(permission_name: str):
        """Generates an error message for a role name already exists error."""
        return ErrorGenerator.generate_error_detail(
            type=PermsNameAlreadyExistsError.TYPE,
            loc=PermsNameAlreadyExistsError.LOC,
            msg=PermsNameAlreadyExistsError.MSG,
            input=f"{permission_name}",
        )

    @staticmethod
    def generate_one_or_more_perms_not_found_error(permission_ids: list):
        """Generates an error message for one or more permissions not found error."""
        return ErrorGenerator.generate_error_detail(
            type=OneOrMorePermissionsNotFoundError.TYPE,
            loc=OneOrMorePermissionsNotFoundError.LOC,
            msg=OneOrMorePermissionsNotFoundError.MSG,
            input=f"{permission_ids}",
        )

    @staticmethod
    def generate_permission_associated_with_group_error(permission_id: int):
        """Generates an error message for a permission associated with a group error."""
        return ErrorGenerator.generate_error_detail(
            type=PermissionAssociatedWithGroupError.TYPE,
            loc=PermissionAssociatedWithGroupError.LOC,
            msg=PermissionAssociatedWithGroupError.MSG,
            input=f"{permission_id}",
        )

class GroupPermissionsExceptions:
    """A class that generates group permissions-related error messages."""

    @staticmethod
    def generate_group_perms_not_found_error(group_id: int, perm_id: int):
        """Generates an error message for a group permissions not found error."""
        return ErrorGenerator.generate_error_detail(
            type=GroupPermissionNotFoundError.TYPE,
            loc=GroupPermissionNotFoundError.LOC,
            msg=GroupPermissionNotFoundError.MSG,
            input=f"{group_id}, {perm_id}",
        )

    @staticmethod
    def generate_group_perms_already_exists_error(group_id: int, perm_id: int):
        """Generates an error message for a group permissions already exists error."""
        return ErrorGenerator.generate_error_detail(
            type=GroupPermissionAlreadyExistsError.TYPE,
            loc=GroupPermissionAlreadyExistsError.LOC,
            msg=GroupPermissionAlreadyExistsError.MSG,
            input=f"{group_id}, {perm_id}",
        )


class RoleExceptions:
    """A class that generates role-related error messages."""

    @staticmethod
    def generate_role_not_found_error(role_id: int):
        """Generates an error message for a role not found error."""
        return ErrorGenerator.generate_error_detail(
            type=RoleNotFoundError.TYPE,
            loc=RoleNotFoundError.LOC,
            msg=RoleNotFoundError.MSG,
            input=f"{role_id}",
        )

    @staticmethod
    def generate_role_name_already_exists_error(role_name: str):
        """Generates an error message for a role name already exists error."""
        return ErrorGenerator.generate_error_detail(
            type=RoleNameAlreadyExistsError.TYPE,
            loc=RoleNameAlreadyExistsError.LOC,
            msg=RoleNameAlreadyExistsError.MSG,
            input=f"{role_name}",
        )

    @staticmethod
    def generate_one_or_more_roles_not_found_error(role_ids: list):
        """Generates an error message for a role name already exists error."""
        return ErrorGenerator.generate_error_detail(
            type=OneOrMoreRolesNotFoundError.TYPE,
            loc=OneOrMoreRolesNotFoundError.LOC,
            msg=OneOrMoreRolesNotFoundError.MSG,
            input=f"{role_ids}",
        )

    @staticmethod
    def generate_role_associated_with_group_error(role_id: int):
        """Generates an error message for a role associated with a group error."""
        return ErrorGenerator.generate_error_detail(
            type=RoleAssociatedWithGroupError.TYPE,
            loc=RoleAssociatedWithGroupError.LOC,
            msg=RoleAssociatedWithGroupError.MSG,
            input=f"{role_id}",
        )
        
    @staticmethod
    def generate_role_associated_with_users_error(role_id: int):
        """Generates an error message for a role associated with users error."""
        return ErrorGenerator.generate_error_detail(
            type=RoleAssociatedWithUsersError.TYPE,
            loc=RoleAssociatedWithUsersError.LOC,
            msg=RoleAssociatedWithUsersError.MSG,
            input=f"{role_id}",
        )

class GroupRolesExceptions:
    """A class that generates group role-related error messages."""

    @staticmethod
    def generate_group_role_not_found_error(group_id: int, role_id: int):
        """Generates an error message for a group role not found error."""
        return ErrorGenerator.generate_error_detail(
            type=GroupRoleNotFoundError.TYPE,
            loc=GroupRoleNotFoundError.LOC,
            msg=GroupRoleNotFoundError.MSG,
            input=f"{group_id}, {role_id}",
        )

    @staticmethod
    def generate_group_role_already_exists_error(group_id: int, role_id: int):
        """Generates an error message for a group role already exists error."""
        return ErrorGenerator.generate_error_detail(
            type=GroupRoleAlreadyExistsError.TYPE,
            loc=GroupRoleAlreadyExistsError.LOC,
            msg=GroupRoleAlreadyExistsError.MSG,
            input=f"{group_id}, {role_id}",
        )


class UserRoleExceptions:
    """A class that generates user role-related error messages."""

    @staticmethod
    def generate_user_role_not_found_error(user_id:int,role_id: int):
        """Generates an error message for a user role not found error."""
        return ErrorGenerator.generate_error_detail(
            type=UserRoleNotFoundError.TYPE,
            loc=UserRoleNotFoundError.LOC,
            msg=UserRoleNotFoundError.MSG,
            input=f"{user_id}, {role_id}",
        )

    @staticmethod
    def generate_user_role_already_exists_error():
        """Generates an error message for a group name already exists error."""
        return ErrorGenerator.generate_error_detail(
            type=UserRoleAlreadyExistsError.TYPE,
            loc=UserRoleAlreadyExistsError.LOC,
            msg=UserRoleAlreadyExistsError.MSG,
        )



class EndpointExceptions:
    """A class that generates endpoint-related error messages."""

    @staticmethod
    def generate_endpoint_not_found_error(endpoint_id: int):
        """Generates an error message for an endpoint not found error."""
        return ErrorGenerator.generate_error_detail(
            type=EndpointNotFoundError.TYPE,
            loc=EndpointNotFoundError.LOC,
            msg=EndpointNotFoundError.MSG,
            input=f"{endpoint_id}",
        )

    @staticmethod
    def generate_endpoint_url_already_exists_error(endpoint_url: str):
        """Generates an error message for an endpoint URL already exists error."""
        return ErrorGenerator.generate_error_detail(
            type=EndpointUrlAlreadyExistsError.TYPE,
            loc=EndpointUrlAlreadyExistsError.LOC,
            msg=EndpointUrlAlreadyExistsError.MSG,
            input=f"{endpoint_url}",
        )

    @staticmethod
    def generate_endpoint_url_not_found_error(endpoint_url: str):
        """Generates an error message for an endpoint URL not found error."""
        return ErrorGenerator.generate_error_detail(
            type=EndpointUrlNotFoundError.TYPE,
            loc=EndpointUrlNotFoundError.LOC,
            msg=EndpointUrlNotFoundError.MSG,
            input=f"{endpoint_url}",
        )