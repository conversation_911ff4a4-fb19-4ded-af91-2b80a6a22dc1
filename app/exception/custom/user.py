
"""
This module contains classes for generating user-related error messages.
"""

from utils.generator import <PERSON>rrorGenerator
from ..errors.user import UserNotFound<PERSON>rror,UserAssociatedWithRolesError,PasswordDoesNotMatchError,TokenExpiredError,PasswordMismatchError

class UserExceptions:
    """A class that generates user-related error messages."""

    @staticmethod
    def generate_user_not_found_error(user_id: int):
        """
        Generates an error message for a user not found error.

        Args:
            user_id (int): The ID of the user.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=UserNotFoundError.TYPE,
            loc=UserNotFoundError.LOC,
            msg=UserNotFoundError.MSG,
            input=f"{user_id}"
        )
        
        
    @staticmethod
    def generate_user_associated_with_roles_error(user_id: int):
        """
        Generates an error message for a user associated with roles error.

        Args:
            user_id (int): The ID of the user.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=UserAssociatedWithRolesError.TYPE,
            loc=UserAssociatedWithRolesError.LOC,
            msg=UserAssociatedWithRolesError.MSG,
            input=f"{user_id}"
        )
        
        
class AuthenticationExceptions:
    """A class that generates authentication-related error messages."""

    @staticmethod
    def generate_password_does_not_match_error():
        """
        Generates an error message for a password does not match error.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=PasswordDoesNotMatchError.TYPE,
            loc=PasswordDoesNotMatchError.LOC,
            msg=PasswordDoesNotMatchError.MSG,
        )
        
        
class PasswordExceptions:
    """A class that generates password-related error messages."""

    @staticmethod
    def generate_token_expired_error(token: str):
        """
        Generates an error message for a token expired error.

        Args:
            token (str): The token.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=TokenExpiredError.TYPE,
            loc=TokenExpiredError.LOC,
            msg=TokenExpiredError.MSG,
            input=token
        )
        
    @staticmethod
    def generate_password_mismatch_error():
        """
        Generates an error message for a password mismatch error.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=PasswordMismatchError.TYPE,
            loc=PasswordMismatchError.LOC,
            msg=PasswordMismatchError.MSG,
        )