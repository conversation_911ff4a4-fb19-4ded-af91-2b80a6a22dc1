"""
This module contains custom exception classes for handling errors in the Arogya Yojana service.
"""

from utils.exception import CustomException, CustomExceptionDetailsBase


class UserHospitalsNotFoundError:
    """
    Exception raised when a user-hospital is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "user_hospital_not_found"
    LOC = ["path", "user_hospital_id"]
    MSG = "User hospital not found"


class UserHospitalMappingNotFoundError:
    """
    Exception raised when a user-hospital mapping is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "user_hospital_mapping_not_found"
    LOC = ["body", "user_id", "hospital_id"]
    MSG = "User hospital mapping not found"


class UserAlreadyAssociatedWithHospitalError:
    """
    Exception raised when a user is already associated with a hospital.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "user_already_associated_with_hospital"
    LOC = ["path", "user_id"]
    MSG = "User already associated with hospital"


class UserHospitalErrors(CustomExceptionDetailsBase):
    """Class to handle user-hospital errors."""

    def raise_exception(self):
        """Raise a CustomException if there are error details."""
        if self.details:
            raise CustomException(self.details)

    def raise_user_hospital_not_found_exception(self, user_hospital_id):
        """Raise a UserHospitalsNotFoundError exception."""
        self.add_detail(
            UserHospitalsNotFoundError.TYPE,
            UserHospitalsNotFoundError.LOC,
            UserHospitalsNotFoundError.MSG,
            user_hospital_id,
        )
        self.raise_exception()

    def raise_user_already_associated_with_hospital_exception(self, user_hospital):
        """Raise a UserAlreadyAssociatedWithHospitalError exception."""
        self.add_detail(
            UserAlreadyAssociatedWithHospitalError.TYPE,
            UserAlreadyAssociatedWithHospitalError.LOC,
            UserAlreadyAssociatedWithHospitalError.MSG,
            user_hospital,
        )
        self.raise_exception()

    def raise_user_hospital_mapping_not_found_exception(self, user_id, hospital_id):
        """Raise a UserHospitalMappingNotFoundError exception."""
        self.add_detail(
            UserHospitalMappingNotFoundError.TYPE,
            UserHospitalMappingNotFoundError.LOC,
            UserHospitalMappingNotFoundError.MSG,
            user_id,
            hospital_id,
        )
        self.raise_exception()
