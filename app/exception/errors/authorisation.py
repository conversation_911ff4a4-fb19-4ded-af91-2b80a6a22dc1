"""
This module contains custom exception classes for handling errors in the Arogya Yojana service.
"""


class GroupNotFoundError:
    """A class that defines an error for a group not found."""

    TYPE = "group_not_found"
    LOC = ["path", "group_id"]
    MSG = "Group not found"


class GroupNameAlreadyExistsError:
    """A class that defines an error for a group name already exists."""

    TYPE = "group_name_already_exists"
    LOC = ["path", "group_name"]
    MSG = "Group name already exists"

class GroupAssociatedWithPermissionsError:
    """A class that defines an error for a group associated with permissions."""

    TYPE = "group_associated_with_perms"
    LOC = ["path", "group_id"]
    MSG = "Group associated with permissions"

class GroupAssociatedWithRolesError:
    """A class that defines an error for a group associated with roles."""

    TYPE = "group_associated_with_roles"
    LOC = ["path", "group_id"]
    MSG = "Group associated with roles"

class PermsNotFoundError:
    """A class that defines an error for a permissions not found."""

    TYPE = "perms_not_found"
    LOC = ["path", "perms_id"]
    MSG = "Permissions not found"


class PermsNameAlreadyExistsError:
    """A class that defines an error for a permissions name already exists."""

    TYPE = "perms_name_already_exists"
    LOC = ["path", "perms_name"]
    MSG = "Permissions name already exists"

class PermissionAssociatedWithGroupError:
    """A class that defines an error for a permission associated with a group."""

    TYPE = "perms_associated_with_group"
    LOC = ["path", "perms_id"]
    MSG = "Permissions associated with group"

class GroupPermissionNotFoundError:
    """A class that defines an error for a group permissions not found."""

    TYPE = "group_perms_not_found"
    LOC = ["body", "group_perms_id"]
    MSG = "Group Permission not found"


class GroupPermissionAlreadyExistsError:
    """A class that defines an error for a group permissions already exists."""

    TYPE = "group_perms_already_exists"
    LOC = ["body", "group_perms_id"]
    MSG = "Group Permission already exists"


class OneOrMorePermissionsNotFoundError:
    """A class that defines an error for one or more permissions not found."""

    TYPE = "one_or_more_perms_not_found"
    LOC = ["body", "permissions_ids"]
    MSG = "One or more permissions not found"


class RoleNotFoundError:
    """ A class that defines an error for a role not found. """
    TYPE = "role_not_found"
    LOC = ["path", "role_id"]
    MSG = "Role not found"


class RoleNameAlreadyExistsError:
    """ A class that defines an error for a role name already exists. """
    TYPE = "role_name_already_exists"
    LOC = ["path", "role_name"]
    MSG = "Role name already exists"

class RoleAssociatedWithGroupError:
    """ A class that defines an error for a role associated with a group. """
    TYPE = "role_associated_with_group"
    LOC = ["path", "role_id"]
    MSG = "Role associated with group"
    
class RoleAssociatedWithUsersError:
    """ A class that defines an error for a role associated with users. """
    TYPE = "role_associated_with_users"
    LOC = ["path", "role_id"]
    MSG = "Role associated with users"

class UserRoleNotFoundError:
    """ A class that defines an error for a user-role not found. """
    TYPE = "user_role_not_found"
    LOC = ["path", "user_id", "role_id"]
    MSG = "User-role not found"


class UserRoleAlreadyExistsError:
    """ A class that defines an error for a user-role already exists. """
    TYPE = "user_role_already_exists"
    LOC = ["path", "user_id", "role_id"]
    MSG = "User Role already exists"


class GroupRoleNotFoundError:
    """ A class that defines an error for a group role not found. """
    TYPE = "group_role_not_found"
    LOC = ["path", "group_role_id"]
    MSG = "Group Role not found"


class GroupRoleAlreadyExistsError:
    """ A class that defines an error for a group role already exists. """
    TYPE = "group_role_already_exists"
    LOC = ["path", "group_role_id"]
    MSG = "Group Role already exists"


class OneOrMoreRolesNotFoundError:
    """ A class that defines an error for one or more roles not found. """
    TYPE = "one_or_more_roles_not_found"
    LOC = ["path", "role_ids"]
    MSG = "One or more roles not found"
    
    
class EndpointNotFoundError:
    """ A class that defines an error for an endpoint not found. """
    TYPE = "endpoint_not_found"
    LOC = ["path", "endpoint"]
    MSG = "Endpoint not found"


class EndpointUrlAlreadyExistsError:
    """ A class that defines an error for an endpoint URL already exists. """
    TYPE = "endpoint_url_already_exists"
    LOC = ["path", "endpoint_url"]
    MSG = "Endpoint URL already exists"


class EndpointUrlNotFoundError:
    """ A class that defines an error for an endpoint URL not found. """
    TYPE = "endpoint_url_not_found"
    LOC = ["path", "endpoint_url"]
    MSG = "Endpoint URL not found"
    
    

class RolesNotFoundValueError(ValueError):
    """Exception raised when the roles is not found."""

    def __init__(self, role_ids: list):
        """
        Initialize the RolesNotFoundValueError.
        """
        msg = f"Roles with ID's {role_ids} not found"
        super().__init__(msg)