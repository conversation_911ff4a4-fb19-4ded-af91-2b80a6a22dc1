"""
This module contains custom exception classes for handling errors in the Arogya Yojana service.
"""

from fastapi.exceptions import RequestValidationError

from utils.exception import (
    CustomException,
    CustomExceptionDetailsBase,
    CustomRequestValidationException,
    NotAuthorizedException,
)

from .authorisation import OneOrMoreRolesNotFoundError, RoleNotFoundError


class UserNotFoundError:
    """
    Exception raised when a user is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "user_not_found"
    LOC = ["path", "user_id"]
    MSG = "User not found"


class UserNotActiveError:
    """
    Exception raised when a user is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "user_is_not_active"
    LOC = ["body", "email"]
    MSG = "Currently you are not an active user please contact administrator."


class UserWithEmailNotFound:
    TYPE = "user_not_found"
    LOC = ["body", "email"]
    MSG = "User not found"


class UserAssociatedWithRolesError:
    """
    Exception raised when a user is associated with roles.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "user_associated_with_roles"
    LOC = ["path", "user_id"]
    MSG = "User associated with roles"


class PasswordDoesNotMatchError:
    """
    Exception raised when the password does not match.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "password_does_not_match"
    LOC = ["body", "password"]
    MSG = "Password does not match"


class TokenExpiredError:
    """
    Exception raised when the token has expired.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "token_expired"
    LOC = ["path", "token"]
    MSG = "The token is expired"


class PasswordMismatchError:
    """
    Exception raised when the password does not match the confirm password.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "password_mismatch"
    LOC = ["body", "password"]
    MSG = "Password and confirm password do not match"


class UserNotFoundValueError(ValueError):
    """Exception raised when a user is not found."""

    def __init__(self, user_id):
        """
        Initialize the UserNotFoundValueError.
        """
        msg = f"User with ID {user_id} not found"
        super().__init__(msg)


class InvalidCreadentialsError:

    TYPE = "invalid_creadentials"
    LOC = ["body"]
    MSG = "Invalid Creadentials"


class MobileNoAlreadyExistsError:

    TYPE = "mobile_no_already_exists"
    LOC = ["body", "mobile_number"]
    MSG = "Mobile number already exists"


class UserHasPatientsError:
    TYPE = "user_has_patients"
    LOC = ["body", "user_id"]
    MSG = "User has already created Patients - Cannot Delete"

class UserHasCasesError:
    TYPE = "user_has_cases"
    LOC = ["body", "user_id"]
    MSG = "User has already created Cases - Cannot Delete"

class UserErrors(CustomExceptionDetailsBase):
    """Class to handle user errors."""

    def raise_exception(self):
        """Raise a CustomException if there are error details."""
        if self.details:
            raise RequestValidationError(self.details)

    def raise_user_not_found_exception(self, user_id):
        """Raise a UserNotFoundError exception."""
        self.add_detail(
            UserNotFoundError.TYPE,
            UserNotFoundError.LOC,
            UserNotFoundError.MSG,
            user_id,
        )
        self.raise_exception()

    def raise_user_not_found_with_email_exception(self, email):
        self.add_detail(
            UserWithEmailNotFound.TYPE,
            UserWithEmailNotFound.LOC,
            UserWithEmailNotFound.MSG,
            email,
        )
        self.raise_exception()

    # def generate_user_not_active_error(self, user_id):
    #     """Generate a UserNotFoundError exception."""
    #     self.add_detail(
    #         UserNotFoundError.TYPE,
    #         UserNotFoundError.LOC,
    #         UserNotFoundError.MSG,
    #         user_id,
    #     )
    #     return self.details

    def raise_user_not_active_exception(self, email):
        self.add_detail(
            UserNotActiveError.TYPE,
            UserNotActiveError.LOC,
            UserNotActiveError.MSG,
            email,
        )
        self.raise_exception()

    def raise_user_associated_with_roles_exception(self, user_id):
        """Raise a UserAssociatedWithRolesError exception."""
        self.add_detail(
            UserAssociatedWithRolesError.TYPE,
            UserAssociatedWithRolesError.LOC,
            UserAssociatedWithRolesError.MSG,
            user_id,
        )
        self.raise_exception()

    def raise_password_does_not_match_exception(self):
        """Raise a PasswordDoesNotMatchError exception."""
        self.add_detail(
            PasswordDoesNotMatchError.TYPE,
            PasswordDoesNotMatchError.LOC,
            PasswordDoesNotMatchError.MSG,
        )
        self.raise_exception()

    def raise_token_expired_exception(self, token):
        """Raise a TokenExpiredError exception."""
        self.add_detail(
            TokenExpiredError.TYPE,
            TokenExpiredError.LOC,
            TokenExpiredError.MSG,
            token,
        )
        self.raise_exception()

    def raise_password_mismatch_exception(self):
        """Raise a PasswordMismatchError exception."""
        self.add_detail(
            PasswordMismatchError.TYPE,
            PasswordMismatchError.LOC,
            PasswordMismatchError.MSG,
        )
        self.raise_exception()

    def raise_mobile_no_already_exists_exception(self, mobile_no):
        """Raise a MobileNoAlreadyExistsError exception."""
        self.add_detail(
            MobileNoAlreadyExistsError.TYPE,
            MobileNoAlreadyExistsError.LOC,
            MobileNoAlreadyExistsError.MSG,
            mobile_no,
        )
        self.raise_exception()


class UsersErrors(CustomRequestValidationException):
    """Class to handle user errors."""

    def raise_mobile_no_already_exists_exception(self, mobile_no):
        """Raise a MobileNoAlreadyExistsError exception."""
        self.add_validation_error(
            loc=MobileNoAlreadyExistsError.LOC,
            msg=MobileNoAlreadyExistsError.MSG,
            error_type=MobileNoAlreadyExistsError.TYPE,
            input_value=mobile_no,
        )
        self.raise_validation_exception()

    def raise_token_expired_exception(self, token):
        """Raise a TokenExpiredError exception."""
        self.add_validation_error(
            loc=TokenExpiredError.LOC,
            msg=TokenExpiredError.MSG,
            error_type=TokenExpiredError.TYPE,
            input_value=token,
        )
        self.raise_validation_exception()

    def raise_user_not_found_exception(self, user_id):
        """Raise a UserNotFoundError exception."""
        self.add_validation_error(
            loc=UserNotFoundError.LOC,
            msg=UserNotFoundError.MSG,
            error_type=UserNotFoundError.TYPE,
            input_value=user_id,
        )
        self.raise_validation_exception()

    def raise_user_associated_with_roles_exception(self, user_id):
        """Raise a UserAssociatedWithRolesError exception."""
        self.add_validation_error(
            loc=UserAssociatedWithRolesError.LOC,
            msg=UserAssociatedWithRolesError.MSG,
            input_value=user_id,
            error_type=UserAssociatedWithRolesError.TYPE,
        )
        self.raise_validation_exception()()

    def raise_user_not_active_exception(self, email):
        self.add_validation_error(
            loc=UserNotActiveError.LOC,
            msg=UserNotActiveError.MSG,
            error_type=UserNotActiveError.TYPE,
            input_value=email,
        )
        self.raise_validation_exception()

    def raise_password_mismatch_exception(self):
        """Raise a PasswordMismatchError exception."""
        self.add_validation_error(
            
            loc=PasswordMismatchError.LOC,
            msg=PasswordMismatchError.MSG,
            error_type=PasswordMismatchError.TYPE,
        )
        self.raise_validation_exception()

    def raise_user_not_found_with_email_exception(self, email):
        self.add_validation_error(
            loc=UserWithEmailNotFound.LOC,
            msg=UserWithEmailNotFound.MSG,
            error_type=UserWithEmailNotFound.TYPE,
            input_value=email,
        )
        self.raise_validation_exception()

    def raise_user_has_patients_error(self, user_id):
        self.add_validation_error(
            loc=UserHasPatientsError.LOC,
            msg=UserHasPatientsError.MSG,
            error_type=UserHasPatientsError.TYPE,
            input_value=user_id,
        )
        self.raise_validation_exception()

    def raise_user_has_cases_error(self, user_id):
        self.add_validation_error(
            loc=UserHasCasesError.LOC,
            msg=UserHasCasesError.MSG,
            error_type=UserHasCasesError.TYPE,
            input_value=user_id,
        )
        self.raise_validation_exception()


class AuthErrors(CustomExceptionDetailsBase):
    """Class to handle authorization errors."""

    def raise_exception(self):
        """Raise a NotAuthorizedException if there are error details."""
        if self.details:
            raise NotAuthorizedException(self.details)

    def raise_invalid_creadentials_exception(self, creadentials):
        """Raise a NotAuthorizedException exception."""
        self.add_detail(
            InvalidCreadentialsError.TYPE,
            InvalidCreadentialsError.LOC,
            InvalidCreadentialsError.MSG,
            creadentials,
        )
        self.raise_exception()

    def raise_password_not_match_error(self):
        self.add_detail(
            PasswordDoesNotMatchError.TYPE,
            PasswordDoesNotMatchError.LOC,
            PasswordDoesNotMatchError.MSG,
        )
        self.raise_exception()

    def raise_one_or_more_roles_not_found_error(self, roles):
        self.add_detail(
            OneOrMoreRolesNotFoundError.TYPE,
            OneOrMoreRolesNotFoundError.LOC,
            OneOrMoreRolesNotFoundError.MSG,
            roles,
        )
        self.raise_exception()

    def raise_role_not_found_error(self, role_id):
        self.add_detail(
            RoleNotFoundError.TYPE,
            RoleNotFoundError.LOC,
            RoleNotFoundError.MSG,
            role_id,
        )
        self.raise_exception()
