from typing import Union

from fastapi import APIRouter, Depends, File, UploadFile, status
from sqlalchemy.orm import Session

from app.address.services.district import DistrictService
from app.address.validations.district import (
    DistrictCreate,
    DistrictListResponse,
    DistrictRetrievedResponse,
    DistrictUpdate,
)

# Function to get a database session
from app.database.database import SessionLocal, get_db
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler
from utils.panda import PandaUtils
from utils.schema import Error

# Create an API router
router = APIRouter()

# Create an instance of the DistrictService
district_service = DistrictService()


# Endpoint to get all districts
@router.get("/districts", response_model=DistrictListResponse)
def get_all_districts(db: Session = Depends(get_db)):
    try:
        districts = district_service.get_all_districts(db)
        return generate_response(
            data=districts,
            status_code=status.HTTP_200_OK,
            message="Districts retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to get a specific district by ID
@router.get(
    "/districts/{district_id}", response_model=Union[Error, DistrictRetrievedResponse]
)
def get_district(district_id: int, db: Session = Depends(get_db)):
    try:
        is_exists, content = district_service.get_district_by_id(district_id, db)
        if is_exists:
            return generate_response(
                data=content,
                status_code=status.HTTP_200_OK,
                message="District retrieved successfully",
            )
        else:
            return generate_response(custom_response=content.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to create a new district
@router.post("/districts", response_model=DistrictRetrievedResponse)
async def create_district(district: DistrictCreate, db: Session = Depends(get_db)):
    try:
        created_district = district_service.create_district(district, db)
        return generate_response(
            data=created_district,
            status_code=status.HTTP_201_CREATED,
            message="District created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to update a district by ID
@router.put(
    "/districts/{district_id}", response_model=Union[Error, DistrictRetrievedResponse]
)
async def update_district(
    district_id: int, district: DistrictUpdate, db: Session = Depends(get_db)
):
    try:
        is_updated, updated_district = district_service.update_district(
            district_id, district, db
        )
        if is_updated:
            return generate_response(
                data=updated_district,
                status_code=status.HTTP_200_OK,
                message="District updated successfully",
            )
        else:
            return generate_response(custom_response=updated_district.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to delete a district by ID
@router.delete("/districts/{district_id}")
async def delete_district(district_id: int, db: Session = Depends(get_db)):
    try:
        is_deleted, deleted_district = district_service.delete_district(district_id, db)
        if is_deleted:
            return generate_response(
                status_code=status.HTTP_200_OK, message="District deleted successfully"
            )
        else:
            return generate_response(custom_response=deleted_district.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to upload districts from a file
@router.post("/districts/upload")
async def upload_districts(file: UploadFile = File(...), db: Session = Depends(get_db)):
    try:
        contents = await file.read()
        records = PandaUtils().convert_contents_to_records(contents=contents)
        districts_created = district_service.bulk_create_districts(
            districts_data=records, db=db
        )
        return generate_response(
            data=districts_created,
            status_code=status.HTTP_201_CREATED,
            message="Districts created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to upload districts from a file
@router.post("/districts/bulk-upload")
async def upload_districts(file: UploadFile = File(...), db: Session = Depends(get_db)):
    try:
        contents = await file.read()
        districts_created = district_service.bulk_insert_districts(
            contents=contents, db=db
        )
        return generate_response(
            data=districts_created,
            status_code=status.HTTP_201_CREATED,
            message="Districts created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)
