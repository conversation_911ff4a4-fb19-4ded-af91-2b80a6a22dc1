from typing import Union

from fastapi import APIRouter, Depends, File, UploadFile, status
from sqlalchemy.orm import Session

from app.address.services.sub_district import SubDistrictService
from app.address.validations.sub_district import (
    SubDistrictCreate,
    SubDistrictListResponse,
    SubDistrictRetrievedResponse,
    SubDistrictUpdate,
)

# Function to get a database session
from app.database.database import SessionLocal, get_db
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler
from utils.panda import PandaUtils
from utils.schema import Error

# Create an API router
router = APIRouter()


# Create an instance of the SubDistrictService
sub_district_service = SubDistrictService()


# Endpoint to get all sub-districts
@router.get("/sub-districts", response_model=SubDistrictListResponse)
def get_all_sub_districts(db: Session = Depends(get_db)):
    try:
        sub_districts = sub_district_service.get_all_sub_districts(db)
        return generate_response(
            data=sub_districts,
            status_code=status.HTTP_200_OK,
            message="Sub-districts retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to get a specific sub-district by ID
@router.get(
    "/sub-districts/{sub_district_id}",
    response_model=Union[Error, SubDistrictRetrievedResponse],
)
def get_sub_district(sub_district_id: int, db: Session = Depends(get_db)):
    try:
        is_exists, content = sub_district_service.get_sub_district_by_id(
            sub_district_id, db
        )
        if is_exists:
            return generate_response(
                data=content,
                status_code=status.HTTP_200_OK,
                message="Sub-district retrieved successfully",
            )
        else:
            return generate_response(custom_response=content.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to create a new sub-district
@router.post("/sub-districts", response_model=SubDistrictRetrievedResponse)
async def create_sub_district(
    sub_district: SubDistrictCreate, db: Session = Depends(get_db)
):
    try:
        created_sub_district = sub_district_service.create_sub_district(
            sub_district, db
        )
        return generate_response(
            data=created_sub_district,
            status_code=status.HTTP_201_CREATED,
            message="Sub-district created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to update a sub-district by ID
@router.put(
    "/sub-districts/{sub_district_id}",
    response_model=Union[Error, SubDistrictRetrievedResponse],
)
async def update_sub_district(
    sub_district_id: int, sub_district: SubDistrictUpdate, db: Session = Depends(get_db)
):
    try:
        is_updated, updated_sub_district = sub_district_service.update_sub_district(
            sub_district_id, sub_district, db
        )
        if is_updated:
            return generate_response(
                data=updated_sub_district,
                status_code=status.HTTP_200_OK,
                message="Sub-district updated successfully",
            )
        else:
            return generate_response(custom_response=updated_sub_district.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to delete a sub-district by ID
@router.delete("/sub-districts/{sub_district_id}")
async def delete_sub_district(sub_district_id: int, db: Session = Depends(get_db)):
    try:
        is_deleted, deleted_sub_district = sub_district_service.delete_sub_district(
            sub_district_id, db
        )
        if is_deleted:
            return generate_response(
                status_code=status.HTTP_200_OK,
                message="Sub-district deleted successfully",
            )
        else:
            return generate_response(custom_response=deleted_sub_district.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to upload sub-districts from a file
@router.post("/sub-districts/upload")
async def upload_sub_districts(
    file: UploadFile = File(...), db: Session = Depends(get_db)
):
    try:
        contents = await file.read()
        records = PandaUtils().convert_contents_to_records(contents=contents)
        sub_districts_created = sub_district_service.bulk_create_sub_districts(
            sub_districts_data=records, db=db
        )
        return generate_response(
            data=sub_districts_created,
            status_code=status.HTTP_201_CREATED,
            message="Sub-districts created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to bulk upload sub-districts from a file
@router.post("/sub-districts/bulk-upload")
async def bulk_upload_sub_districts(
    file: UploadFile = File(...), db: Session = Depends(get_db)
):
    try:
        contents = await file.read()
        sub_districts_created = sub_district_service.bulk_insert_sub_districts(
            contents=contents, db=db
        )
        return generate_response(
            data=sub_districts_created,
            status_code=status.HTTP_201_CREATED,
            message="Sub-districts created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)
