from typing import Union

from fastapi import APIRouter, Depends, File, UploadFile, status, Response
from sqlalchemy.orm import Session

from app.address.services.state import StateService
from app.address.validations.state import (
    StateCreate,
    StateListResponse,
    StateRetrievedResponse,
    StateUpdate,
)

# Function to get a database session
from app.database.database import SessionLocal, get_db
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler
from utils.panda import PandaUtils
from utils.schema import Error

# Create an API router
router = APIRouter()


# Create an instance of the StateService
state_service = StateService()


# Endpoint to get all states
@router.get("/states", response_model=StateListResponse)
def get_all_states(db: Session = Depends(get_db), response: Response = None):
    try:
        # Add strong cache control headers for better performance
        if response:
            # Cache for 1 day (86400 seconds) with validation
            response.headers["Cache-Control"] = "public, max-age=86400, must-revalidate"
            response.headers["ETag"] = "states-v1"  # Simple versioning for cache validation
        
        # Get states from the cached service
        states = state_service.get_all_states(db)
        
        return generate_response(
            data=states,
            status_code=status.HTTP_200_OK,
            message="States retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to get a specific state by ID
@router.get("/states/{state_id}", response_model=Union[Error, StateRetrievedResponse])
def get_state(state_id: int, db: Session = Depends(get_db)):
    try:
        is_exists, content = state_service.get_state_by_id(state_id, db)
        if is_exists:
            return generate_response(
                data=content,
                status_code=status.HTTP_200_OK,
                message="State retrieved successfully",
            )
        else:
            return generate_response(custom_response=content.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to create a new state
@router.post("/states", response_model=StateRetrievedResponse)
async def create_state(state: StateCreate, db: Session = Depends(get_db)):
    try:
        created_state = state_service.create_state(state, db)
        return generate_response(
            data=created_state,
            status_code=status.HTTP_201_CREATED,
            message="State created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to update a state by ID
@router.put("/states/{state_id}", response_model=Union[Error, StateRetrievedResponse])
async def update_state(
    state_id: int, state: StateUpdate, db: Session = Depends(get_db)
):
    try:
        is_updated, updated_state = state_service.update_state(state_id, state, db)
        if is_updated:
            return generate_response(
                data=updated_state,
                status_code=status.HTTP_200_OK,
                message="State updated successfully",
            )
        else:
            return generate_response(custom_response=updated_state.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to delete a state by ID
@router.delete("/states/{state_id}")
async def delete_state(state_id: int, db: Session = Depends(get_db)):
    try:
        is_deleted, deleted_state = state_service.delete_state(state_id, db)
        if is_deleted:
            return generate_response(
                status_code=status.HTTP_200_OK, message="State deleted successfully"
            )
        else:
            return generate_response(custom_response=deleted_state.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.post("/states/upload")
async def upload_file(file: UploadFile = File(...), db: Session = Depends(get_db)):
    try:
        contents = await file.read()
        records = PandaUtils().convert_contents_to_records(contents=contents)
        talukas_created = state_service.bulk_create_states(states_data=records, db=db)
        return generate_response(
            data=talukas_created,
            status_code=status.HTTP_201_CREATED,
            message="States created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)
