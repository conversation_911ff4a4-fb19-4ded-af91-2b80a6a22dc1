from sqlalchemy import Column, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.orm import relationship
from sqlalchemy.schema import ForeignKeyConstraint

from app.database.database import Base


class DistrictDB(Base):
    """District model."""

    __tablename__ = "district"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), index=True)
    state_id = Column(Integer, ForeignKey("state.id"))

    state = relationship("StateDB", back_populates="district")
    sub_district = relationship("SubDistrictDB", back_populates="district")
    address = relationship("AddressDB", back_populates="districts")
