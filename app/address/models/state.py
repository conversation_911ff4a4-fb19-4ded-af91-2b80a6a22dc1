from sqlalchemy import Column, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.orm import relationship

from app.database.database import Base
from app.patients.models.patient import AddressDB


class StateDB(Base):
    """Create a data model for the database to store states"""

    __tablename__ = "state"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), index=True)

    district = relationship("DistrictDB", back_populates="state")
    address = relationship(
        "AddressDB", back_populates="states")
