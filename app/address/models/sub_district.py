from sqlalchemy import Column, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.orm import relationship

from app.database.database import Base


class SubDistrictDB(Base):
    """Create a data model for the database to store sub_district"""

    __tablename__ = "sub_district"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), index=True)
    district_id = Column(Integer, ForeignKey("district.id"), index=True)

    district = relationship("DistrictDB", back_populates="sub_district")
    address = relationship("AddressDB", back_populates="cities")
