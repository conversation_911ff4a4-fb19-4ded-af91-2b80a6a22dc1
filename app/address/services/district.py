from sqlalchemy.orm import Session

from app.address.models.state import StateDB
from app.database.database import engine, Base, SessionLocal
from app.address.models.district import DistrictDB
from app.address.exception.custom.district import DistrictExceptions
from utils.db import BaseService, BulkCreateService
from app.address.helpers.logger import address_logger

from utils.panda import PandaUtils


class DistrictService(BaseService, BulkCreateService):
    """
    Service class for managing district-related operations.
    """

    def __init__(self) -> None:
        self.db = SessionLocal()
        self.logger = address_logger

    def get_district_by_attribute(self, value: str, db: Session, attribute: str = None):
        """Retrieve a district by a specific attribute."""
        try:
            if not attribute:
                attribute = DistrictDB.id
            # Query the district from the database
            district = self.get_by_attribute(db, DistrictDB, attribute, value)
            if not district:
                self.logger.debug("District not found with %s=%s", attribute, value)
                error = DistrictExceptions.generate_district_not_found_error(
                    district_id=value
                )
                return False, error
            return True, district
        except Exception as e:
            self.logger.error("Error retrieving district by attribute %s=%s: %s", attribute, value, str(e), exc_info=True)
            raise

    def get_district_by_id(self, district_id: int, db: Session):
        """Retrieve a district by ID."""
        try:
            self.logger.debug("Retrieving district with id=%s", district_id)
            is_exists, district = self.get_district_by_attribute(
                value=district_id, db=db
            )
            return is_exists, district
        except Exception as e:
            self.logger.error("Error retrieving district with id=%s: %s", district_id, str(e), exc_info=True)
            raise

    def get_district_by_name(self, district_name: str):
        """Retrieve a district by name."""
        try:
            self.logger.debug("Retrieving district with name=%s", district_name)
            is_exists, district = self.get_district_by_attribute(
                district_name, self.db, DistrictDB.name
            )
            return is_exists, district
        except Exception as e:
            self.logger.error("Error retrieving district with name=%s: %s", district_name, str(e), exc_info=True)
            raise

    def get_all_districts(self, db: Session):
        """Retrieve all districts."""
        try:
            self.logger.debug("Retrieving all districts")
            districts = self.get_all(db, DistrictDB)
            self.logger.debug("Retrieved %s districts", len(districts))
            return districts
        except Exception as e:
            self.logger.error("Error retrieving all districts: %s", str(e), exc_info=True)
            raise

    def create_district(self, district_data, db: Session):
        """Create a new district."""
        try:
            self.logger.info("Creating new district with data: %s", district_data.dict())
            district = self.create(db, DistrictDB, **district_data.dict())
            self.logger.info("Created district with id=%s", district.id)
            return district
        except Exception as e:
            self.logger.error("Error creating district: %s", str(e), exc_info=True)
            raise

    def bulk_create_districts(self, districts_data: list, db: Session):
        """Create multiple new districts."""
        try:
            self.logger.info("Creating %s new districts", len(districts_data))
            districts = self.create_instances(db, DistrictDB, districts_data)
            self.logger.info("Created %s districts", len(districts))
            return districts
        except Exception as e:
            self.logger.error("Error creating districts in bulk: %s", str(e), exc_info=True)
            raise

    def update_district(self, district_id: int, district_data: dict, db: Session):
        """Update a district."""
        try:
            self.logger.info("Updating district with id=%s", district_id)
            is_exist, district = self.get_district_by_id(district_id, db=db)
            if not is_exist:
                self.logger.warning("District not found with id=%s", district_id)
                error = DistrictExceptions.generate_district_not_found_error(
                    district_id=district_id
                )
                return False, error
            # Check if the new district name already exists
            new_district_name = district_data.name
            is_exist, existing_district = self.get_district_by_name(
                new_district_name)
            if is_exist:
                if existing_district.id != district_id:
                    self.logger.warning("District already exists with name=%s", new_district_name)
                    error = DistrictExceptions.generate_district_already_exists_error()
                    return False, error
            district_data_dict = district_data.dict()
            updated_district = self.update(
                db, DistrictDB, district_id, **district_data_dict
            )
            self.logger.info("Updated district with id=%s", district_id)
            return True, updated_district
        except Exception as e:
            self.logger.error("Error updating district with id=%s: %s", district_id, str(e), exc_info=True)
            raise

    def delete_district(self, district_id: int, db: Session):
        """Delete a district."""
        try:
            self.logger.info("Deleting district with id=%s", district_id)
            is_exist, district = self.get_district_by_id(district_id, db=db)
            if not is_exist:
                self.logger.warning("District not found with id=%s", district_id)
                error = DistrictExceptions.generate_district_not_found_error(
                    district_id=district_id
                )
                return False, error
            self.delete(db, DistrictDB, district_id)
            self.logger.info("Deleted district with id=%s", district_id)
            return True, None
        except Exception as e:
            self.logger.error("Error deleting district with id=%s: %s", district_id, str(e), exc_info=True)
            raise

    def is_district_exists(self, district_id: int):
        """Check if a district exists."""
        try:
            self.logger.debug("Checking if district exists with id=%s", district_id)
            is_exist, _ = self.get_district_by_id(district_id, db=self.db)
            return is_exist
        except Exception as e:
            self.logger.error("Error checking if district exists with id=%s: %s", district_id, str(e), exc_info=True)
            raise

    def is_district_exists_by_name(self, district_name: str):
        """Check if a district exists by name."""
        try:
            self.logger.debug("Checking if district exists with name=%s", district_name)
            is_exists, _ = self.get_district_by_name(district_name)
            return is_exists
        except Exception as e:
            self.logger.error("Error checking if district exists with name=%s: %s", district_name, str(e), exc_info=True)
            raise

    def bulk_insert_districts(self, contents, db: Session):
        try:
            self.logger.info("Bulk inserting districts from uploaded file")
            # Read the Excel file
            df = PandaUtils().convert_to_dataframe(contents=contents)
            self.logger.debug("Converted file to dataframe with %s rows", len(df))
            
            # Create a list to store the district mappings
            districts = []

            # Get all state names from the DataFrame
            state_names = df["STATE"].unique()
            self.logger.debug("Found %s unique states in the file", len(state_names))

            # Query all existing states from the database
            existing_states = db.query(StateDB).filter(
                StateDB.name.in_(state_names)).all()
            self.logger.debug("Found %s existing states in the database", len(existing_states))

            # Create a dictionary to map state names to state objects
            state_mapping = {state.name: state for state in existing_states}

            # Iterate over the DataFrame
            for index, row in df.iterrows():
                state_name = row["STATE"]
                district_name = row["DISTRICT_NAME"]

                # Get the state or create a new one
                state = state_mapping.get(state_name)
                if state is None:
                    self.logger.info("Creating new state: %s", state_name)
                    state = StateDB(name=state_name)
                    db.add(state)
                    db.commit()
                    state_mapping[state_name] = state

                # Add the district mapping
                districts.append({"name": district_name, "state_id": state.id})

            # Bulk insert districts
            self.logger.info("Bulk inserting %s districts", len(districts))
            db.bulk_insert_mappings(DistrictDB, districts)
            db.commit()
            self.logger.info("Successfully inserted %s districts", len(districts))
            return districts
        except Exception as e:
            self.logger.error("Error bulk inserting districts: %s", str(e), exc_info=True)
            raise
