import re
from sqlalchemy.orm import Session

from app.database.database import engine, Base, SessionLocal
from app.address.models.state import StateDB

from app.address.exception.custom.state import StateExceptions

from utils.db import BaseService, BulkCreateService
from app.address.models.state import StateDB
from app.address.exception.custom.state import StateExceptions
from app.address.helpers.logger import address_logger
import time


class StateService(BaseService, BulkCreateService):
    """
    Service class for managing state-related operations.
    """

    # Class-level cache for states
    _states_cache = None
    _cache_timestamp = None

    def __init__(self) -> None:
        self.db = SessionLocal()
        self.logger = address_logger
        
        # Pre-load states on initialization if cache is empty
        if StateService._states_cache is None:
            try:
                StateService._states_cache = self.get_all(self.db, StateDB)
                StateService._cache_timestamp = time.time()
                self.logger.info("Pre-loaded %s states into cache", len(StateService._states_cache))
            except Exception as e:
                self.logger.error("Failed to pre-load states: %s", str(e))

    def get_state_by_attribute(self, value: str, db: Session, attribute: str = None):
        """Retrieve a state by a specific attribute."""
        try:
            if not attribute:
                attribute = StateDB.id
            # Query the state from the database
            state = self.get_by_attribute(db, StateDB, attribute, value)
            if not state:
                self.logger.debug("State not found with %s=%s", attribute, value)
                error = StateExceptions.generate_state_not_found_error(
                    state_id=value
                )
                return False, error
            return True, state
        except Exception as e:
            self.logger.error("Error retrieving state by attribute %s=%s: %s", attribute, value, str(e), exc_info=True)
            raise

    def get_state_by_id(self, state_id: int, db: Session):
        """Retrieve a state by ID."""
        try:
            self.logger.debug("Retrieving state with id=%s", state_id)
            is_exists, state = self.get_state_by_attribute(
                value=state_id, db=db)
            return is_exists, state
        except Exception as e:
            self.logger.error("Error retrieving state with id=%s: %s", state_id, str(e), exc_info=True)
            raise

    def get_state_by_name(self, state_name: str):
        """Retrieve a state by name."""
        try:
            self.logger.debug("Retrieving state with name=%s", state_name)
            is_exists, state = self.get_state_by_attribute(
                state_name, self.db, StateDB.name
            )
            return is_exists, state
        except Exception as e:
            self.logger.error("Error retrieving state with name=%s: %s", state_name, str(e), exc_info=True)
            raise

    def get_all_states(self, db: Session):
        """Retrieve all states with efficient server-side caching."""
        import time
        
        try:
            # Check if we have a valid cache (cache for 1 hour = 3600 seconds)
            current_time = time.time()
            cache_valid = (
                StateService._states_cache is not None and 
                StateService._cache_timestamp is not None and
                current_time - StateService._cache_timestamp < 3600
            )
            
            if cache_valid:
                self.logger.debug("Returning states from cache")
                return StateService._states_cache
            
            # Cache miss or expired, fetch from database
            self.logger.debug("Cache miss, fetching states from database")
            states = self.get_all(db, StateDB)
            
            # Update cache
            StateService._states_cache = states
            StateService._cache_timestamp = current_time
            
            return states
        except Exception as e:
            self.logger.error("Error retrieving all states: %s", str(e), exc_info=True)
            raise

    def create_state(self, state_data, db: Session):
        """Create a new state."""
        try:
            self.logger.info("Creating new state with data: %s", state_data.dict())
            state = self.create(db, StateDB, **state_data.dict())
            self.logger.info("Created state with id=%s", state.id)
            return state
        except Exception as e:
            self.logger.error("Error creating state: %s", str(e), exc_info=True)
            raise

    def bulk_create_states(self, states_data: list, db: Session):
        """Create multiple new states."""
        try:
            self.logger.info("Creating %s new states", len(states_data))
            states = self.create_instances(db, StateDB, states_data)
            self.logger.info("Created %s states", len(states))
            return states
        except Exception as e:
            self.logger.error("Error creating states in bulk: %s", str(e), exc_info=True)
            raise

    def update_state(self, state_id: int, state_data: dict, db: Session):
        """Update a state."""
        try:
            self.logger.info("Updating state with id=%s", state_id)
            is_exist, state = self.get_state_by_id(state_id, db=db)
            if not is_exist:
                self.logger.warning("State not found with id=%s", state_id)
                error = StateExceptions.generate_state_not_found_error(
                    state_id=state_id
                )
                return False, error
            # Check if the new state name already exists
            new_state_name = state_data.name
            is_exist, existing_state = self.get_state_by_name(new_state_name)
            if is_exist:
                if existing_state.id != state_id:
                    self.logger.warning("State already exists with name=%s", new_state_name)
                    error = StateExceptions.generate_state_already_exists_error(
                        value=new_state_name
                    )
                    return False, error
            state_data_dict = state_data.dict()
            updated_state = self.update(
                db, StateDB, state_id, **state_data_dict)
            self.logger.info("Updated state with id=%s", state_id)
            return True, updated_state
        except Exception as e:
            self.logger.error("Error updating state with id=%s: %s", state_id, str(e), exc_info=True)
            raise

    def delete_state(self, state_id: int, db: Session):
        """Delete a state."""
        try:
            self.logger.info("Deleting state with id=%s", state_id)
            is_exist, state = self.get_state_by_id(state_id, db=db)
            if not is_exist:
                self.logger.warning("State not found with id=%s", state_id)
                error = StateExceptions.generate_state_not_found_error(
                    state_id=state_id
                )
                return False, error
            self.delete(db, StateDB, state_id)
            self.logger.info("Deleted state with id=%s", state_id)
            return True, None
        except Exception as e:
            self.logger.error("Error deleting state with id=%s: %s", state_id, str(e), exc_info=True)
            raise

    def is_state_exists(self, state_id: int):
        """Check if a state exists."""
        try:
            self.logger.debug("Checking if state exists with id=%s", state_id)
            is_exist, _ = self.get_state_by_id(state_id, db=self.db)
            return is_exist
        except Exception as e:
            self.logger.error("Error checking if state exists with id=%s: %s", state_id, str(e), exc_info=True)
            raise

    def is_state_exists_by_name(self, state_name: str):
        """Check if a state exists by name."""
        try:
            self.logger.debug("Checking if state exists with name=%s", state_name)
            is_exists, _ = self.get_state_by_name(state_name)
            return is_exists
        except Exception as e:
            self.logger.error("Error checking if state exists with name=%s: %s", state_name, str(e), exc_info=True)
            raise
