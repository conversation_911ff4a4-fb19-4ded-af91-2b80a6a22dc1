from sqlalchemy.orm import Session

from app.address.models.state import StateDB
from app.database.database import engine, Base, SessionLocal
from app.address.models.sub_district import SubDistrictDB
from app.address.models.district import DistrictDB

from app.address.exception.custom.sub_district import SubDistrictExceptions

from utils.db import BaseService, BulkCreateService
from utils.panda import PandaUtils
from app.address.helpers.logger import address_logger


class SubDistrictService(BaseService, BulkCreateService):
    """
    Service class for managing sub-district-related operations.
    """

    def __init__(self) -> None:
        self.db = SessionLocal()
        self.logger = address_logger

    def get_sub_district_by_attribute(
        self, value: str, db: Session, attribute: str = None
    ):
        """Retrieve a sub-district by a specific attribute."""
        try:
            if not attribute:
                attribute = SubDistrictDB.id
            # Query the sub-district from the database
            sub_district = self.get_by_attribute(
                db, SubDistrictDB, attribute, value)
            if not sub_district:
                self.logger.debug("Sub-district not found with %s=%s", attribute, value)
                error = SubDistrictExceptions.generate_sub_district_not_found_error(
                    sub_district_id=value
                )
                return False, error
            return True, sub_district
        except Exception as e:
            self.logger.error("Error retrieving sub-district by attribute %s=%s: %s", attribute, value, str(e), exc_info=True)
            raise

    def get_sub_district_by_id(self, sub_district_id: int, db: Session):
        """Retrieve a sub-district by ID."""
        try:
            self.logger.debug("Retrieving sub-district with id=%s", sub_district_id)
            is_exists, sub_district = self.get_sub_district_by_attribute(
                value=sub_district_id, db=db
            )
            return is_exists, sub_district
        except Exception as e:
            self.logger.error("Error retrieving sub-district with id=%s: %s", sub_district_id, str(e), exc_info=True)
            raise

    def get_sub_district_by_name(self, sub_district_name: str):
        """Retrieve a sub-district by name."""
        try:
            self.logger.debug("Retrieving sub-district with name=%s", sub_district_name)
            is_exists, sub_district = self.get_sub_district_by_attribute(
                sub_district_name, self.db, SubDistrictDB.name
            )
            return is_exists, sub_district
        except Exception as e:
            self.logger.error("Error retrieving sub-district with name=%s: %s", sub_district_name, str(e), exc_info=True)
            raise

    def get_all_sub_districts(self, db: Session):
        """Retrieve all sub-districts."""
        try:
            self.logger.debug("Retrieving all sub-districts")
            sub_districts = self.get_all(db, SubDistrictDB)
            self.logger.debug("Retrieved %s sub-districts", len(sub_districts))
            return sub_districts
        except Exception as e:
            self.logger.error("Error retrieving all sub-districts: %s", str(e), exc_info=True)
            raise

    def create_sub_district(self, sub_district_data, db: Session):
        """Create a new sub-district."""
        try:
            self.logger.info("Creating new sub-district with data: %s", sub_district_data.dict())
            sub_district = self.create(
                db, SubDistrictDB, **sub_district_data.dict())
            self.logger.info("Created sub-district with id=%s", sub_district.id)
            return sub_district
        except Exception as e:
            self.logger.error("Error creating sub-district: %s", str(e), exc_info=True)
            raise

    def bulk_create_sub_districts(self, sub_districts_data: list, db: Session):
        """Create multiple new sub-districts."""
        try:
            self.logger.info("Creating %s new sub-districts", len(sub_districts_data))
            sub_districts = self.create_instances(
                db, SubDistrictDB, sub_districts_data
            )
            self.logger.info("Created %s sub-districts", len(sub_districts))
            return sub_districts
        except Exception as e:
            self.logger.error("Error creating sub-districts in bulk: %s", str(e), exc_info=True)
            raise

    def update_sub_district(
        self, sub_district_id: int, sub_district_data: dict, db: Session
    ):
        """Update a sub-district."""
        try:
            self.logger.info("Updating sub-district with id=%s", sub_district_id)
            is_exist, sub_district = self.get_sub_district_by_id(
                sub_district_id, db=db
            )
            if not is_exist:
                self.logger.warning("Sub-district not found with id=%s", sub_district_id)
                error = SubDistrictExceptions.generate_sub_district_not_found_error(
                    sub_district_id=sub_district_id
                )
                return False, error
            # Check if the new sub_district name already exists
            new_sub_district_name = sub_district_data.name
            is_exist, existing_sub_district = self.get_sub_district_by_name(
                new_sub_district_name
            )
            if is_exist:
                if existing_sub_district.id != sub_district_id:
                    self.logger.warning("Sub-district already exists with name=%s", new_sub_district_name)
                    error = SubDistrictExceptions.generate_sub_district_already_exists_error()
                    return False, error
            sub_district_data_dict = sub_district_data.dict()
            updated_sub_district = self.update(
                db, SubDistrictDB, sub_district_id, **sub_district_data_dict
            )
            self.logger.info("Updated sub-district with id=%s", sub_district_id)
            return True, updated_sub_district
        except Exception as e:
            self.logger.error("Error updating sub-district with id=%s: %s", sub_district_id, str(e), exc_info=True)
            raise

    def delete_sub_district(self, sub_district_id: int, db: Session):
        """Delete a sub-district."""
        try:
            self.logger.info("Deleting sub-district with id=%s", sub_district_id)
            is_exist, sub_district = self.get_sub_district_by_id(
                sub_district_id, db=db
            )
            if not is_exist:
                self.logger.warning("Sub-district not found with id=%s", sub_district_id)
                error = SubDistrictExceptions.generate_sub_district_not_found_error(
                    sub_district_id=sub_district_id
                )
                return False, error
            self.delete(db, SubDistrictDB, sub_district_id)
            self.logger.info("Deleted sub-district with id=%s", sub_district_id)
            return True, None
        except Exception as e:
            self.logger.error("Error deleting sub-district with id=%s: %s", sub_district_id, str(e), exc_info=True)
            raise

    def is_sub_district_exists(self, sub_district_id: int, db: Session):
        """Check if a sub-district exists."""
        try:
            self.logger.debug("Checking if sub-district exists with id=%s", sub_district_id)
            is_exist, _ = self.get_sub_district_by_id(sub_district_id, db=self.db)
            return is_exist
        except Exception as e:
            self.logger.error("Error checking if sub-district exists with id=%s: %s", sub_district_id, str(e), exc_info=True)
            raise

    def is_sub_district_exists_by_name(self, sub_district_name: str):
        """Check if a sub-district exists by name."""
        try:
            self.logger.debug("Checking if sub-district exists with name=%s", sub_district_name)
            is_exists, _ = self.get_sub_district_by_name(sub_district_name)
            return is_exists
        except Exception as e:
            self.logger.error("Error checking if sub-district exists with name=%s: %s", sub_district_name, str(e), exc_info=True)
            raise

    def bulk_insert_sub_districts(self, contents, db: Session):
        try:
            self.logger.info("Bulk inserting sub-districts from uploaded file")
            # Read the Excel file
            df = PandaUtils().convert_to_dataframe(contents=contents)
            self.logger.debug("Converted file to dataframe with %s rows", len(df))
            
            # Create a list to store the sub-district mappings
            sub_districts = []

            # Get all district names from the DataFrame
            district_names = df["DISTRICT_NAME"].unique()
            self.logger.debug("Found %s unique districts in the file", len(district_names))

            # Query all existing districts from the database
            existing_districts = db.query(DistrictDB).filter(
                DistrictDB.name.in_(district_names)).all()
            self.logger.debug("Found %s existing districts in the database", len(existing_districts))

            # Create a dictionary to map district names to district objects
            district_mapping = {district.name: district for district in existing_districts}

            # Iterate over the DataFrame
            for index, row in df.iterrows():
                district_name = row["DISTRICT_NAME"]
                sub_district_name = row["SUB_DISTRICT_NAME"]

                # Get the district or create a new one
                district = district_mapping.get(district_name)
                if district is None:
                    self.logger.warning("District not found: %s, skipping sub-district: %s", 
                                       district_name, sub_district_name)
                    continue

                # Add the sub-district mapping
                sub_districts.append({"name": sub_district_name, "district_id": district.id})

            # Bulk insert sub-districts
            if sub_districts:
                self.logger.info("Bulk inserting %s sub-districts", len(sub_districts))
                db.bulk_insert_mappings(SubDistrictDB, sub_districts)
                db.commit()
                self.logger.info("Successfully inserted %s sub-districts", len(sub_districts))
            else:
                self.logger.warning("No valid sub-districts found to insert")
                
            return sub_districts
        except Exception as e:
            self.logger.error("Error bulk inserting sub-districts: %s", str(e), exc_info=True)
            raise
