from pickle import APPEND
from typing import Optional, List
from pydantic import BaseModel, validator

from app.address.services.sub_district import SubDistrictService
from app.address.exception.errors.sub_district import SubDistrictAlreadExistsValueError
from app.address.services.district import DistrictService
from app.address.exception.errors.district import DistrictNotExistsValueError

from utils.response import BaseResponse




class SubDistrictBase(BaseModel):
    """
    Base class for creating a district.
    """

    name: str
    district_id: int
    

class SubDistrictCreate(SubDistrictBase):
    """
    Represents a schema for creating a sub-district.
    """

    @validator("name")
    def name_must_be_unique(cls, v):
        """Ensure that the sub-district name is unique."""
        sub_district_service = SubDistrictService()
        sub_district = sub_district_service.is_sub_district_exists_by_name(sub_district_name=v)
        if sub_district:
            raise SubDistrictAlreadExistsValueError()
        return v
    
    
    @validator("district_id")
    def district_must_exist(cls, v):
        """Ensure that the district exists."""
        district_service = DistrictService()
        district = district_service.is_district_exists(district_id=v)
        if not district:
            raise DistrictNotExistsValueError()
        return v

class SubDistrictUpdate(SubDistrictBase):
    """
    Represents a schema for updating a district.
    """

    @validator("district_id")
    def district_must_exist(cls, v):
        """Ensure that the district exists."""
        district_service = DistrictService()
        district = district_service.is_district_exists(district_id=v)
        if not district:
            raise DistrictNotExistsValueError()
        return v


class SubDistrictResponse(BaseModel):
    """Represents a schema for a sub-district response."""

    id: int
    name: str

class SubDistrictRetrievedResponse(BaseResponse):
    """Represents a sub-district retrieved response schema."""

    data: SubDistrictResponse
    

class SubDistrictListResponse(BaseResponse):
    """Represents a sub-district list response schema."""

    data: Optional[List[SubDistrictResponse]] = None
