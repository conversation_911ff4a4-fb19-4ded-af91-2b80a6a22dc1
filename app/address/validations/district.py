from typing import Optional, List
from pydantic import BaseModel, validator

from app.address.services.district import DistrictService
from app.address.services.state import StateService
from app.address.exception.errors.district import DistrictAlreadExistsValueError
from app.address.exception.errors.state import StateNotExistsValueError
from app.address.validations.sub_district import SubDistrictResponse

from utils.response import BaseResponse


class DistrictBase(BaseModel):
    """
    Base class for creating a district.
    """

    name: str
    state_id: int


class DistrictCreate(DistrictBase):
    """
    Represents a schema for creating a district.
    """

    @validator("name")
    def name_must_be_unique(cls, v):
        """Ensure that the district name is unique."""
        district_service = DistrictService()
        district = district_service.is_district_exists_by_name(district_name=v)
        if district:
            raise DistrictAlreadExistsValueError()
        return v

    @validator("state_id")
    def state_must_exist(cls, v):
        """Ensure that the state exists."""
        state_service = StateService()
        state = state_service.is_state_exists(state_id=v)
        if not state:
            raise StateNotExistsValueError()
        return v


class DistrictUpdate(DistrictBase):
    """
    Represents a schema for updating a district.
    """

    @validator("state_id")
    def state_must_exist(cls, v):
        """Ensure that the state exists."""
        state_service = StateService()
        state = state_service.is_state_exists(state_id=v)
        if not state:
            raise StateNotExistsValueError()
        return v


class DistrictResponse(BaseModel):
    """Represents a schema for a district response."""

    id: int
    name: str
    sub_district: Optional[List[SubDistrictResponse]] = None


class DistrictRetrievedResponse(BaseResponse):
    """Represents a district retrieved response schema."""

    data: DistrictResponse


class DistrictListResponse(BaseResponse):
    """Represents a district list response schema."""

    data: Optional[List[DistrictResponse]] = None
