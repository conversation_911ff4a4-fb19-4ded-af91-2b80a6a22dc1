from typing import Optional, List
from pydantic import BaseModel, validator

from app.address.services.state import StateService
from app.address.exception.errors.state import StateAlreadExistsValueError

from app.address.validations.district import DistrictResponse

from utils.response import BaseResponse


class StateBase(BaseModel):
    """
    Base class for creating a state.
    """

    name: str


class StateCreate(StateBase):
    """
    Represents a schema for creating a state.
    """

    @validator("name")
    def name_must_be_unique(cls, v):
        """Ensure that the state name is unique."""
        state_service = StateService()
        state = state_service.is_state_exists_by_name(state_name=v)
        if state:
            raise StateAlreadExistsValueError()
        return v


class StateUpdate(StateBase):
    """
    Represents a schema for updating a state.
    """

    pass


class StateResponse(StateBase):
    """Represents a schema for a state response."""

    id: int
    district: Optional[List[DistrictResponse]] = None


class StateRetrievedResponse(BaseResponse):
    """Represents a state retrieved response schema."""

    data: StateResponse


class StateListResponse(BaseResponse):
    """Represents a state list response schema."""

    data: Optional[List[StateResponse]] = None
    