from utils.generator import ErrorGenerator
from ..errors.district import DistrictAlreadyExistsError,DistrictNotFoundError

class DistrictExceptions:
    """A class that generates district-related error messages."""

    @staticmethod
    def generate_district_not_found_error(district_id: int):
        """
        Generates an error message for a district not found error.

        Args:
            district_id (int): The ID of the district.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=DistrictNotFoundError.TYPE,
            loc=DistrictNotFoundError.LOC,
            msg=DistrictNotFoundError.MSG,
            input=f"{district_id}",
        )

    @staticmethod
    def generate_district_already_exists_error():
        """
        Generates an error message for a district already exists error.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=DistrictAlreadyExistsError.TYPE,
            loc=DistrictAlreadyExistsError.LOC,
            msg=DistrictAlreadyExistsError.MSG,
        )
