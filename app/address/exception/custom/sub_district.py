from utils.generator import ErrorGenerator
from ..errors.sub_district import SubDistrictNotFoundError, SubDistrictAlreadyExistsError


class SubDistrictExceptions:
    """A class that generates sub-district-related error messages."""

    @staticmethod
    def generate_sub_district_not_found_error(sub_district_id: int):
        """
        Generates an error message for a sub-district not found error.

        Args:
            sub_district_id (int): The ID of the sub-district.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=SubDistrictNotFoundError.TYPE,
            loc=SubDistrictNotFoundError.LOC,
            msg=SubDistrictNotFoundError.MSG,
            input=f"{sub_district_id}",
        )

    @staticmethod
    def generate_sub_district_already_exists_error(value):
        """
        Generates an error message for a sub-district already exists error.

        Args:
            sub_district_id (int): The ID of the sub-district.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=SubDistrictAlreadyExistsError.TYPE,
            loc=SubDistrictAlreadyExistsError.LOC,
            msg=SubDistrictAlreadyExistsError.MSG,
            input=f"{value}",
        )
