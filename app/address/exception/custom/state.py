from utils.generator import ErrorGenerator
from ..errors.state import StateAlreadyExistsError, StateNotFoundError


class StateExceptions:
    """A class that generates state-related error messages."""

    @staticmethod
    def generate_state_not_found_error(state_id: int):
        """
        Generates an error message for a state not found error.

        Args:
            state_id (int): The ID of the state.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=StateNotFoundError.TYPE,
            loc=StateNotFoundError.LOC,
            msg=StateNotFoundError.MSG,
            input=f"{state_id}",
        )

    @staticmethod
    def generate_state_already_exists_error(value):
        """
        Generates an error message for a state already exists error.

        Args:
            state_id (int): The ID of the state.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=StateAlreadyExistsError.TYPE,
            loc=StateAlreadyExistsError.LOC,
            msg=StateAlreadyExistsError.MSG,
            input=f"{value}",
        )
