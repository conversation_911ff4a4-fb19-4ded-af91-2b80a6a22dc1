""" This module contains the exceptions for the sub-district. """


class SubDistrictNotFoundError:
    """
    Exception raised when the sub-district is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "sub_district_not_found"
    LOC = ["path", "sub_district"]
    MSG = "Sub-district not found"


class SubDistrictAlreadyExistsError:
    """
    Exception raised when the sub-district already exists.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "sub_district_already_exists"
    LOC = ["path", "sub_district"]
    MSG = "Sub-district already exists"


class SubDistrictAlreadExistsValueError(ValueError):
    """Exception raised when a sub-district already exists."""

    def __init__(self):
        self.msg = "Sub-district already exists"
        super().__init__(self.msg)


class SubDistrictNotExistsValueError(ValueError):
    """Exception raised when a sub-district does not exist."""

    def __init__(self):
        self.msg = "Sub-district does not exist"
        super().__init__(self.msg)
