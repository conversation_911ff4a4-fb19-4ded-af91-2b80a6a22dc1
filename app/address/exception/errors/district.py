""" This module contains the exceptions for the taluka. """

class DistrictNotFoundError:
    """
    Exception raised when the district is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "district_not_found"
    LOC = ["path", "district"]
    MSG = "District not found"
    

class DistrictAlreadyExistsError:
    """
    Exception raised when the district already exists.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "district_already_exists"
    LOC = ["path", "district"]
    MSG = "District already exists"


class DistrictAlreadExistsValueError(ValueError):
    """Exception raised when a district already exists."""

    def __init__(self):
        self.msg = "District already exists"
        super().__init__(self.msg)
        

class DistrictNotExistsValueError(ValueError):
    """Exception raised when a district does not exist."""

    def __init__(self):
        self.msg = "District does not exist"
        super().__init__(self.msg)
