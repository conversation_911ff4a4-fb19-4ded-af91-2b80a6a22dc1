""" This module contains the exceptions for the taluka. """


class StateNotFoundError:
    """
    Exception raised when the state is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "state_not_found"
    LOC = ["path", "state"]
    MSG = "State not found"


class StateAlreadyExistsError:
    """
    Exception raised when the state already exists.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "state_already_exists"
    LOC = ["path", "state"]
    MSG = "State already exists"


class StateAlreadExistsValueError(ValueError):
    """Exception raised when a state already exists."""

    def __init__(self):
        self.msg = "State already exists"
        super().__init__(self.msg)


class StateNotExistsValueError(ValueError):
    """Exception raised when a state does not exist."""

    def __init__(self):
        self.msg = "State does not exist"
        super().__init__(self.msg)
