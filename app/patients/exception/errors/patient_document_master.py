"""
This module contains custom exception classes for handling errors in the Arogya Yojana service.
"""


class PatientDocumentMasterNotFoundError:
    """
    Exception raised when a user is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """
    TYPE = "patient_document_master_not_found"
    LOC = ["path", "dcoument_master_id"]
    MSG = "Patient Document master for given id not found."


class PatientDocumentMasterAssociatedWithPatientDocumentError:
    """
    Exception raised when a document type is associated with a patient document master.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """
    TYPE = "patient document_master_associated_with_patient_document"
    LOC = ["path", "patient_document_master_id"]
    MSG = "Patient Document master associated with patient document."


class DuplicateGUIDError:
    """
    Exception raised when a duplicate GUID is found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """
    TYPE = "duplicate_guid"
    LOC = ["body", "guid"]
    MSG = "Duplicate GUID found."
