"""
This module contains custom exception classes for handling errors in the Arogya Yojana service.
"""

class DocumentTypeNotFoundError:
    """
    Exception raised when a user is not found.
    
    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """
    TYPE = "document_type_not_found"
    LOC = ["path", "dcoument_type_id"]
    MSG = "Document type for given id not found."
    
    
class DocumentTypeAssociatedWithPatientDocumentMaster:
    """
    Exception raised when a document type is associated with a patient document master.
    
    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """
    TYPE = "document_type_associated_with_patient_document_master"
    LOC = ["path", "document_type_id"]
    MSG = "Document type associated with patient document master"