"""
This module contains custom exception classes for handling errors in the Arogya Yojana service.
"""


class PatientNotFoundError:
    """
    Exception raised when a user is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """
    TYPE = "patient_not_found"
    LOC = ["path", "patient_id"]
    MSG = "No Patient Found."


class PatientsNotFoundError:
    """
    Exception raised when a user is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """
    TYPE = "patients_not_found"
    LOC = ["", ""]
    MSG = "No Patients Found."


class RationCardNumberAlreadyExistsError():
    """
    Exception raised when a ration card number already exists.
    """
    TYPE = "ration_card_number_already_exists"
    LOC = ["body", "ration_card_number"]
    MSG = "Ration card number already exists."


class ArogyaCardNumberAlreadyExistsError():
    """
    Exception raised when an arogya card number already exists.
    """
    TYPE = "arogya_card_no_already_exists"
    LOC = ["body", "arogya_card_no"]
    MSG = "Aarogya card number already exists."


class AadharCardAlreadyExistsError:
    TYPE = "aadhar_no_already_exists"
    LOC = ["body", "aadhar_number"]
    MSG = "Aadhaar number already exists."
