"""
This module contains custom exception classes for handling errors in the Arogya Yojana service.
"""

class DocumentMasterNotFoundError:
    """
    Exception raised when a user is not found.
    
    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """
    TYPE = "document_master_not_found"
    LOC = ["path", "dcoument_master_id"]
    MSG = "Document master for given id not found."
    
    
class DocumentMasterAssociatedWithPatientDocumentMaster:
    """
    Exception raised when a document type is associated with a patient document master.
    
    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """
    TYPE = "document_master_associated_with_patient_document_master"
    LOC = ["path", "document_master_id"]
    MSG = "Document master associated with patient document master"