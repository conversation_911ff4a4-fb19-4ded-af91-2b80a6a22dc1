from utils.exception import (
    CustomException,
    CustomExceptionDetailsBase,
    CustomRequestValidationException,
)
from utils.generator import ErrorGenerator

from ..errors.patient import (
    AadharCardAlreadyExistsError,
    ArogyaCardNumberAlreadyExistsError,
    <PERSON>ient<PERSON>ot<PERSON>oundError,
    PatientsNotFoundError,
    RationCardNumberAlreadyExistsError,
)

"""
This module contains classes for generating patient-related error messages.
"""


class PatientExceptions:
    """A class that generates patient-related error messages."""

    @staticmethod
    def generate_patient_not_found_error(patient_id: int):
        """
        Generates an error message for a patient not found error.

        Args:
            patient_id (int): The ID of the patient.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=PatientNotFoundError.TYPE,
            loc=PatientNotFoundError.LOC,
            msg=PatientNotFoundError.MSG,
            input=f"{patient_id}",
        )

    @staticmethod
    def generate_patients_not_found_error():
        """
        Generates an error message for a patients not found error.

        Returns:
            dict: A dictionary containing the error details.
        """
        return ErrorGenerator.generate_error_detail(
            type=PatientsNotFoundError.TYPE,
            loc=PatientsNotFoundError.LOC,
            msg=PatientsNotFoundError.MSG,
        )


class PatientNotFoundValueError(ValueError):
    """Exception raised when the patient is not found."""

    def __init__(self, patient_id: int):
        """
        Initialize the PatientNotFoundValueError.
        """
        msg = f"Patient with ID {patient_id} not exist."
        super().__init__(msg)


class PatientErrors(CustomRequestValidationException):
    """Class to handle patient create and update errors."""

    def raise_patients_not_found_exception(self):
        """
        Raise patients not found exception.
        Raises:
            NotFoundException: If patients not found.
        """
        self.add_validation_error(
            loc=PatientsNotFoundError.LOC,
            msg=PatientsNotFoundError.MSG,
            input_value=None,
        )
        self.raise_validation_exception()

    def raise_patient_not_found_exception(self, patient_id):
        """
        Raise patient not found exception.

        Args:
            patient_id (int): The ID of the patient.

        Raises:
            CustomException: If patient not found.
        """
        self.add_validation_error(
            loc=PatientNotFoundError.LOC,
            msg=PatientNotFoundError.MSG,
            input_value=patient_id,
        )
        self.raise_validation_exception()

    def raise_arogyacard_already_exist_exception(self, arogya_card_number):
        """
        Raise arogya_card_number already exists exception.

        Args:
            arogya_card_number (str): Arogya card number.

        Raises:
            CustomException: If arogya_card_number already exists.
        """
        self.add_validation_error(
            loc=ArogyaCardNumberAlreadyExistsError.LOC,
            msg=ArogyaCardNumberAlreadyExistsError.MSG,
            input_value=arogya_card_number,
        )
        self.raise_validation_exception()

    def raise_rationcard_already_exist_exception(self, ration_card_number):
        """
        Raise ration_card_number already exists exception.

        Args:
            ration_card_number (str): Ration card number.

        Raises:
            CustomException: If ration_card_number already exists.
        """
        self.add_validation_error(
            loc=RationCardNumberAlreadyExistsError.LOC,
            msg=RationCardNumberAlreadyExistsError.MSG,
            input_value=ration_card_number,
        )
        self.raise_validation_exception()

    def raise_aadhar_already_exist_exception(self, aadhar_card_number):
        """
        Raise aadhar_card_number already exists exception.

        Args:
            aadhar_card_number (str): Aadhar card number.

        Raises:
            CustomException: If aadhar_card_number already exists.
        """
        self.add_validation_error(
            loc=AadharCardAlreadyExistsError.LOC,
            msg=AadharCardAlreadyExistsError.MSG,
            input_value=aadhar_card_number,
        )
        self.raise_validation_exception()
