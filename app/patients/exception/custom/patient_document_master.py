from ..errors.patient_document_master import PatientDocumentMasterNotFoundError, PatientDocumentMasterAssociatedWithPatientDocumentError, DuplicateGUIDError
from utils.exception import CustomRequestValidationException


"""
This module contains classes for generating patient-related error messages.
"""


class PatientDocumentMasterErrors(CustomRequestValidationException):
    
    def raise_patient_document_master_not_exists_exception(self, document_id):
        self.add_validation_error(
            loc=PatientDocumentMasterNotFoundError.LOC,
            msg=PatientDocumentMasterNotFoundError.MSG,
            input_value=document_id
        )
        self.raise_validation_exception()
        
    def raise_patient_document_master_associated_with_patient_document_error(self, document_id):
        self.add_validation_error(
            loc=PatientDocumentMasterAssociatedWithPatientDocumentError.LOC,
            msg=PatientDocumentMasterAssociatedWithPatientDocumentError.MSG,
            input_value=document_id
        )
        self.raise_validation_exception()
        
    
    def raise_duplicate_guid_error(self, guid):
        self.add_validation_error(
            loc=DuplicateGUIDError.LOC,
            msg=DuplicateGUIDError.MSG,
            input_value=guid
        )
        self.raise_validation_exception()