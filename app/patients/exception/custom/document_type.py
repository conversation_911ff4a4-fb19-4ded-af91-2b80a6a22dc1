from ..errors.document_type import DocumentTypeNotFound<PERSON><PERSON><PERSON>, DocumentTypeAssociatedWithPatientDocumentMaster
from utils.generator import ErrorGenerator

"""
This module contains classes for generating patient-related error messages.
"""


class DcoumentTypeExceptions:
    """
    A class that defines custom exceptions related to document types.
    """

    @staticmethod
    def generate_dcoument_type_not_found_error(document_type_id: int):
        """
        Generates an error detail for document type not found error.

        Args:
            document_type_id (int): The ID of the document type.

        Returns:
            dict: The error detail containing the error type, location, message, and input.
        """
        return ErrorGenerator.generate_error_detail(
            type=DocumentTypeNotFoundError.TYPE,
            loc=DocumentTypeNotFoundError.LOC,
            msg=DocumentTypeNotFoundError.MSG,
            input=f"{document_type_id}"
        )
        
    @staticmethod
    def generate_document_type_associated_with_patient_document_master_error(document_type_id: int):
        """Generates an error message for a group associated with permissions error."""
        return ErrorGenerator.generate_error_detail(
            type=DocumentTypeAssociatedWithPatientDocumentMaster.TYPE,
            loc=DocumentTypeAssociatedWithPatientDocumentMaster.LOC,
            msg=DocumentTypeAssociatedWithPatientDocumentMaster.MSG,
            input=f"{document_type_id}",
        )