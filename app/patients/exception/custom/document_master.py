from ..errors.document_master import DocumentMasterNotFoundE<PERSON>r, DocumentMasterAssociatedWithPatientDocumentMaster
from utils.generator import ErrorGenerator

"""
This module contains classes for generating patient-related error messages.
"""


class DcoumentMasterExceptions:
    """
    A class that defines custom exceptions related to document types.
    """

    @staticmethod
    def generate_document_master_not_found_error(document_master_id: int):
        """
        Generates an error detail for document type not found error.

        Args:
            document_master_id (int): The ID of the document type.

        Returns:
            dict: The error detail containing the error type, location, message, and input.
        """
        return ErrorGenerator.generate_error_detail(
            type=DocumentMasterNotFoundError.TYPE,
            loc=DocumentMasterNotFoundError.LOC,
            msg=DocumentMasterNotFoundError.MSG,
            input=f"{document_master_id}"
        )
        
    @staticmethod
    def generate_document_master_associated_with_patient_document_master_error(document_master_id: int):
        """Generates an error message for a group associated with permissions error."""
        return ErrorGenerator.generate_error_detail(
            type=DocumentMasterAssociatedWithPatientDocumentMaster.TYPE,
            loc=DocumentMasterAssociatedWithPatientDocumentMaster.LOC,
            msg=DocumentMasterAssociatedWithPatientDocumentMaster.MSG,
            input=f"{document_master_id}",
        )