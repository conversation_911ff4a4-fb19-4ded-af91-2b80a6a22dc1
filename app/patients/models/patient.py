from datetime import datetime, <PERSON><PERSON>ta

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Foreign<PERSON>ey, Integer, String
from sqlalchemy.orm import relationship

from app.database.database import Base


class PatientDB(Base):
    """
    Represents a patient in the database.
    """

    __tablename__ = "patient"
    id = Column(Integer, primary_key=True, index=True)
    first_name = Column(String(25), index=True)
    middle_name = Column(String(25), index=True, nullable=True)
    last_name = Column(String(25), index=True)
    gender = Column(String(6), index=True)
    date_of_birth = Column(DateTime, index=True)
    phone_number = Column(String(10), index=True)
    alternate_phone_number = Column(String(10), index=True, nullable=True)
    relative_name = Column(String(25), index=True, nullable=True)
    relative_phone_number = Column(String(10), index=True, nullable=True)
    ration_card_number = Column(String(25), index=True, nullable=True)
    arogya_card_no = Column(String(25), index=True, nullable=True)
    aadhar_number = Column(String(25), index=True, unique=True)
    is_active = Column(Boolean, default=True)
    timestamp_id = Column(Integer, index=True)
    date_of_enrollement = Column(DateTime, default=datetime.utcnow)
    created_by = Column(Integer, ForeignKey("users.id"))
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    updated_by = Column(Integer, ForeignKey("users.id"))
    cases = relationship("Case", back_populates="patient")

    address = relationship("AddressDB", back_populates="patient")
    documents = relationship("PatientDocumentDB", back_populates="patient")
    audit_trails = relationship("PatientAuditTrailDB", back_populates="patient")


class AddressDB(Base):
    """
    Represents an address associated with a patient.
    """

    __tablename__ = "address"
    id = Column(Integer, primary_key=True, index=True)
    patient_id = Column(Integer, ForeignKey("patient.id"))
    address_line1 = Column(String(200), index=True)
    address_line2 = Column(String(100), index=True, nullable=True)
    address_line3 = Column(String(100), index=True, nullable=True)
    state = Column(Integer, ForeignKey("state.id"))
    district = Column(Integer, ForeignKey("district.id"))
    city = Column(Integer, ForeignKey("sub_district.id"))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(String(25), index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    updated_by = Column(String(25), index=True)

    patient = relationship("PatientDB", back_populates="address")
    states = relationship("StateDB", back_populates="address")
    districts = relationship("DistrictDB", back_populates="address")
    cities = relationship("SubDistrictDB", back_populates="address")


class PatientDocumentDB(Base):
    """
    Represents a document associated with a patient.
    """

    __tablename__ = "patient_document"
    id = Column(Integer, primary_key=True, index=True)
    document_no = Column(String(25), index=True)
    patient_id = Column(Integer, ForeignKey("patient.id"))
    guid = Column(String(36), unique=True, index=True, nullable=True)
    document_master_id = Column(Integer, ForeignKey("document_master.id"), index=True)

    patient = relationship("PatientDB", back_populates="documents")


class PatientAuditTrailDB(Base):
    """
    Represents an audit trail for patient actions.
    """

    __tablename__ = "patient_audit_trail"
    id = Column(Integer, primary_key=True, index=True)
    patient_id = Column(Integer, ForeignKey("patient.id"))
    action = Column(String(25), index=True)
    data = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(String(25), index=True)
    patient = relationship("PatientDB", back_populates="audit_trails")


class ModuleDocumentMasterDB(Base):
    """
    Represents a document master for a module.
    """

    __tablename__ = "module_document_master"
    id = Column(Integer, primary_key=True, index=True)
    module_key = Column(String(25), index=True)
    document_master_id = Column(Integer, ForeignKey("document_master.id"), index=True)
    is_required = Column(Boolean, default=True)
    is_active = Column(Boolean, default=True)
