from sqlalchemy.orm import Session
from app.database.database import engine, Base, SessionLocal
# from app.patients.models.patient_document_master import PatientDocumentMasterDB
from app.patients.models.patient_document_master import PatientDocumentMasterDB
from utils.db import BaseService
from typing import List, Optional
from fastapi import HTTPException
from sqlalchemy.orm import Session
from app.master.models.document_master import DocumentMaster
from app.patients.exception.custom.document_master import DcoumentMasterExceptions
from utils.db import BaseService
from app.patients.helpers.logger import patients_logger


class DocumentMasterService(BaseService):

    def __init__(self) -> None:
        self.db = SessionLocal()
        self.logger = patients_logger

    def get_document_master_by_attribute(self, value: str, db: Session, attribute: str = None):
        """Retrieve a DocumentType by a specific attribute."""
        try:
            if not attribute:
                attribute = DocumentMaster.id
            # Query the document_master from the database
            self.logger.debug("Retrieving document master with %s=%s", attribute, value)
            document_master = self.get_by_attribute(
                db, DocumentMaster, attribute, value)
            if not document_master:
                self.logger.debug("Document master not found with %s=%s", attribute, value)
                error = DcoumentMasterExceptions.generate_document_master_not_found_error(
                    document_master_id=value)
                return False, error
            return True, document_master
        except Exception as e:
            self.logger.error("Error retrieving document master with %s=%s: %s", attribute, value, str(e), exc_info=True)
            raise

    def create_document_master(self, document_master_data, db: Session):
        """
        Create a new document type.

        Args:
            document_master_data (DocumentTypeCreate): The data for creating the document type.
            db (Session): The database session.

        Returns:
            DocumentMaster: The created document type.

        Raises:
            Exception: If an error occurs while creating the document type.
        """
        try:
            # Create a new document type instance and add it to the database
            self.logger.info("Creating new document master with data: %s", document_master_data.model_dump())
            document_master = self.create(
                db, DocumentMaster, **document_master_data.model_dump())
            self.logger.info("Created document master with id=%s", document_master.id)
            return document_master
        except Exception as e:
            self.logger.error("Error creating document master: %s", str(e), exc_info=True)
            raise

    def get_document_master_by_id(self, document_master_id: int, db: Session):
        """
        Get a document type by ID.

        Args:
            document_master_id (int): The ID of the document type.
            db (Session): The database session.

        Returns:
            Optional[DocumentMaster]: The document type if found, None otherwise.
        """
        try:
            self.logger.debug("Retrieving document master with id=%s", document_master_id)
            document_master = self.get_document_master_by_attribute(
                value=document_master_id, db=db)
            return document_master
        except Exception as e:
            self.logger.error("Error retrieving document master with id=%s: %s", document_master_id, str(e), exc_info=True)
            raise

    def get_all_document_master(self, db: Session):
        """
        Get all document types.

        Args:
            db (Session): The database session.

        Returns:
            List[DocumentMaster]: List of all document types.
        """
        try:
            self.logger.debug("Retrieving all document masters")
            document_masters = self.get_all(db, DocumentMaster)
            self.logger.debug("Retrieved %s document masters", len(document_masters))
            return document_masters
        except Exception as e:
            self.logger.error("Error retrieving all document masters: %s", str(e), exc_info=True)
            raise

    def update_document_master(self, document_master_id: int, document_master_data, db: Session):
        """
        Update a document type.

        Args:
            document_master_id (int): The ID of the document type to update.
            document_master_data (DocumentTypeUpdate): The updated data for the document type.
            db (Session): The database session.

        Returns:
            DocumentMaster: The updated document type.

        Raises:
            Exception: If an error occurs while updating the document type.
        """
        try:
            self.logger.info("Updating document master with id=%s", document_master_id)
            is_exist, document_master = self.get_document_master_by_id(
                document_master_id, db=db)
            if not is_exist:
                self.logger.warning("Document master not found with id=%s", document_master_id)
                error = DcoumentMasterExceptions.generate_document_master_not_found_error(
                    document_master_id=document_master_id
                )
                return False, error
            else:
                document_master_data_dict = document_master_data.model_dump()
                document_master_data_dict["id"] = document_master.id
                document_master = self.update(
                    db, DocumentMaster, document_master_id, **document_master_data_dict)
                self.logger.info("Updated document master with id=%s", document_master_id)
                return True, document_master
        except Exception as e:
            self.logger.error("Error updating document master with id=%s: %s", document_master_id, str(e), exc_info=True)
            raise

    def delete_document_master(self, document_master_id: int, db: Session):
        """
        Delete a document type.

        Args:
            document_master_id (int): The ID of the document type to delete.
            db (Session): The database session.

        Raises:
            Exception: If an error occurs while deleting the document type.
        """
        try:
            self.logger.info("Deleting document master with id=%s", document_master_id)
            is_exist, document_master = self.get_document_master_by_id(
                document_master_id, db=db)
            if not is_exist:
                self.logger.warning("Document master not found with id=%s", document_master_id)
                error = DcoumentMasterExceptions.generate_document_master_not_found_error(
                    document_master_id=document_master_id
                )
                return False, error

            # Check if there are any associations with the document type
            error = self.check_document_master_associations(
                document_master_id, db)
            if error:
                self.logger.warning("Document master with id=%s has associations and cannot be deleted", document_master_id)
                return False, error

            # Delete the document type
            is_deleted = self.delete(db, DocumentMaster, document_master_id)
            self.logger.info("Deleted document master with id=%s", document_master_id)
            return is_deleted, document_master_id
        except Exception as e:
            self.logger.error("Error deleting document master with id=%s: %s", document_master_id, str(e), exc_info=True)
            raise

    def is_document_master_in_patient_document_master(self, document_master_id: int, db: Session):
        """
        Check if a group is associated with any permissions.

        Args:
            group_id (int): The ID of the group.
            db (Session): The database session.

        Returns:
            bool: True if the group is associated with any permissions, False otherwise.
        """
        self.logger.debug("Checking if document master with id=%s is in patient document master", document_master_id)
        is_document_master = (
            db.query(PatientDocumentMasterDB)
            .filter(PatientDocumentMasterDB.document_master_id == document_master_id)
            .all()
        )
        return bool(is_document_master)

    def check_document_master_associations(self, document_master_id: int, db: Session):
        """
        Check if a document type is associated with any other entities.

        Args:
            document_master_id (int): The ID of the document type.
            db (Session): The database session.

        Returns:
            DcoumentMasterExceptions or None: The error if the document type is associated with other entities, None otherwise.
        """
        try:
            self.logger.debug("Checking document master associations for id=%s", document_master_id)
            error = None
            is_document_master_in_patient_document_master_check = self.is_document_master_in_patient_document_master(
                document_master_id, db)
            if is_document_master_in_patient_document_master_check:
                self.logger.warning("Document master with id=%s is associated with patient document master", document_master_id)
                error = DcoumentMasterExceptions.generate_document_master_associated_with_patient_document_master_error(
                    document_master_id=document_master_id
                )

            return error
        except Exception as e:
            self.logger.error("Error checking document master associations for id=%s: %s", document_master_id, str(e), exc_info=True)
            raise

    def is_document_master_exists(self, document_name: str):
        """Check if a document exists."""

        try:
            self.logger.debug("Checking if document master exists with name=%s", document_name)
            is_exist, document_master = self.get_document_master(
                document_name, self.db)
            return is_exist
        except Exception as e:
            self.logger.error("Error checking if document master exists with name=%s: %s", document_name, str(e), exc_info=True)
            raise

    def get_document_master(self, document_name: str, db: Session):
        """Retrieve a document name ."""

        try:
            self.logger.debug("Retrieving document master with name=%s", document_name)
            is_exist, document_master = self.get_document_master_by_attribute(
                value=document_name, db=db, attribute=DocumentMaster.document_name
            )
            return is_exist, document_master
        except Exception as e:
            self.logger.error("Error retrieving document master with name=%s: %s", document_name, str(e), exc_info=True)
            raise
