from typing import List, Optional, Tuple
import traceback
from fastapi.encoders import jsonable_encoder
from sqlalchemy import and_, func, or_
from sqlalchemy.orm import Session, aliased,joinedload

from app.cases.models.case import Case
from app.database.database import SessionLocal, transaction
from app.master.models.document_master import DocumentMaster
from app.models.user import UserDB
from app.patients.models.patient import (
    AddressDB,
    ModuleDocumentMasterDB,
    PatientAuditTrailDB,
    PatientDB,
    PatientDocumentDB,
)
from app.services.user import UserService
from utils.db import BaseService
from utils.logger import setup_logger

from ..exception.custom.patient import PatientErrors, PatientExceptions
from ..exception.custom.patient_document_master import PatientDocumentMasterErrors


class PatientService(BaseService):
    """
    Service class for managing patient-related operations.
    """

    def __init__(self) -> None:
        self.logger = setup_logger(log_file="patient.log")
        self.db = SessionLocal()

    def get_patient_by_attribute(self, value: str, db: Session, attribute: str = None):
        """Retrieve a patient by a specific attribute."""
        try:
            if not attribute:
                attribute = PatientDB.id
            # Query the patient from the database
            patient = self.get_by_attribute(db, PatientDB, attribute, value)
            if not patient:
                self.logger.warning("Patient not found with %s=%s", 
                                   attribute.key if hasattr(attribute, 'key') else 'id', value)
                error = PatientExceptions.generate_patient_not_found_error(
                    patient_id=value
                )
                return False, error

            self.logger.debug("Patient found with %s=%s", 
                             attribute.key if hasattr(attribute, 'key') else 'id', value)
            return True, patient
        except Exception as e:
            self.logger.error("Error retrieving patient by attribute %s=%s: %s", 
                             attribute.key if hasattr(attribute, 'key') else 'id', value, str(e), 
                             exc_info=True)
            raise

    def get_patient_or_raise(self, patient_id: int, db: Session):
        try:
            patient = self.get_by_attribute(db, PatientDB, PatientDB.id, patient_id)
            self.logger.debug("Retrieved patient with id=%s: %s", patient_id, patient is not None)
            if not patient:
                self.logger.warning("Patient not found with id=%s", patient_id)
                PatientErrors().raise_patient_not_found_exception(patient_id)
            return patient
        except Exception as e:
            self.logger.error("Error retrieving patient with id=%s: %s", patient_id, str(e), exc_info=True)
            raise

    def get_patient_by_id(self, patient_id: int, db: Session):
        """Retrieve a patient by ID."""

        try:
            self.logger.debug("Retrieving patient with id=%s", patient_id)
            is_exist, patient = self.get_patient_by_attribute(value=patient_id, db=db)
            return is_exist, patient
        except Exception as e:
            self.logger.error("Error retrieving patient with id=%s: %s", patient_id, str(e), exc_info=True)
            raise

    def check_patient_exist_with_attribute(self, value, attribute: str):
        """Check if a patient exists with the given Aadhar number."""
        try:
            self.logger.debug("Checking if patient exists with %s=%s", attribute, value)
            is_exist = self.get_patient_by_attribute(
                value=value, db=self.db, attribute=attribute
            )
            return is_exist
        except Exception as e:
            self.logger.error("Error checking if patient exists with %s=%s: %s", 
                             attribute, value, str(e), exc_info=True)
            raise

    def check_unique_field(
        self, field: str, value: str, patient_id: Optional[int] = None
    ) -> bool:
        try:
            self.logger.debug("Checking if %s=%s is unique%s", 
                             field, value, f" for patient_id!={patient_id}" if patient_id else "")
            query = self.db.query(PatientDB).filter(getattr(PatientDB, field) == f"{value}")
            if patient_id:
                query = query.filter(PatientDB.id != patient_id)
            is_exist = self.db.query(query.exists()).scalar()
            self.logger.debug("Field %s=%s is %sunique", field, value, "" if not is_exist else "not ")
            return is_exist
        except Exception as e:
            self.logger.error("Error checking unique field %s=%s: %s", field, value, str(e), exc_info=True)
            raise

    def validate_document_master_id(self, document_master_id: int, db: Session) -> bool:
        """
        Validate if a document master ID exists in the database.
        """
        self.logger.debug("Validating document master ID: %s", document_master_id)

        if not document_master_id:
            self.logger.warning("Empty document master ID provided")
            return True  # Return True for None/empty values as they're optional

        master_id_exists = (
            db.query(DocumentMaster)
            .filter(DocumentMaster.id == document_master_id)
            .first()
        )
        if not master_id_exists:
            self.logger.error("Invalid document master ID: %s", document_master_id)
            PatientDocumentMasterErrors().raise_patient_document_master_not_exists_exception(
                document_master_id
            )

        self.logger.debug("Document master ID %s is valid", document_master_id)
        return True

    def create_patient(self, patient_data, db: Session):
        """
        Create a new patient.
        
        Args:
            patient_data (PatientCreate): The data of the patient to be created.
            db (Session): The database session.
        
        Returns:
            PatientDB: The newly created patient.
        
        Raises:
            Exception: If an error occurs while creating the patient.
        """
        try:
            self.logger.info("Creating new patient with data: %s", patient_data)
            
            # Extract address data
            address_data = patient_data.pop("address", None)
            
            # Extract document data
            documents_data = patient_data.pop("documents", None)
            
            # Create the patient
            patient = self.create(db, PatientDB, **patient_data)
            self.logger.info("Created patient with id=%s", patient.id)
            
            # Create the address
            if address_data:
                self.logger.debug("Creating address for patient_id=%s", patient.id)
                address_data["patient_id"] = patient.id
                address = self.create(db, AddressDB, **address_data)
                self.logger.debug("Created address with id=%s for patient_id=%s", 
                                 address.id, patient.id)
            
            # Create the documents
            if documents_data:
                self.logger.debug("Creating %s documents for patient_id=%s", 
                                 len(documents_data), patient.id)
                for document_data in documents_data:
                    document_data["patient_id"] = patient.id
                    document = self.create(db, PatientDocumentDB, **document_data)
                    self.logger.debug("Created document with id=%s for patient_id=%s", 
                                     document.id, patient.id)
            
            # Create the audit trail
            self.logger.debug("Creating audit trail for patient_id=%s", patient.id)
            audit_trail = PatientAuditTrailService().create_audit_trail(
                patient_id=patient.id, action="CREATE", db=db
            )
            self.logger.debug("Created audit trail with id=%s for patient_id=%s", 
                             audit_trail.id if audit_trail else None, patient.id)
            
            return patient
        except Exception as e:
            self.logger.error("Error creating patient: %s", str(e), exc_info=True)
            db.rollback()
            raise

    def update_patient(self, patient_id: int, patient_data: dict, db: Session):
        """
        Update a patient.
        
        Args:
            patient_id (int): The ID of the patient to be updated.
            patient_data (dict): The updated data for the patient.
        
        Returns:
            tuple: (bool, Updated patient object OR error message)
        
        Raises:
            Exception: If an error occurs during the update operation.
        """
        try:
            self.logger.info("Updating patient with id=%s", patient_id)
            
            # Check if the patient exists
            is_exist, patient = self.get_patient_by_id(patient_id, db)
            if not is_exist:
                self.logger.warning("Patient not found with id=%s", patient_id)
                return False, patient
            
            # Extract address data
            address_data = patient_data.pop("address", None)
            
            # Extract document data
            documents_data = patient_data.pop("documents", None)
            
            # Update the patient
            self.logger.debug("Updating patient data for patient_id=%s", patient_id)
            updated_patient = self._update_patient(db, patient_id, patient_data)
            
            # Update the address
            if address_data:
                self.logger.debug("Updating address for patient_id=%s", patient_id)
                address = (
                    db.query(AddressDB)
                    .filter(AddressDB.patient_id == patient_id)
                    .first()
                )
                if address:
                    self._update_address(db, address.id, address_data)
                    self.logger.debug("Updated address with id=%s for patient_id=%s", 
                                     address.id, patient_id)
                else:
                    self.logger.debug("Creating new address for patient_id=%s", patient_id)
                    address_data["patient_id"] = patient_id
                    address = self.create(db, AddressDB, **address_data)
                    self.logger.debug("Created address with id=%s for patient_id=%s", 
                                     address.id, patient_id)
            
            # Update the documents
            if documents_data:
                self.logger.debug("Updating %s documents for patient_id=%s", 
                                 len(documents_data), patient_id)
                for document_data in documents_data:
                    document_id = document_data.pop("id", None)
                    if document_id:
                        self.logger.debug("Updating document with id=%s for patient_id=%s", 
                                         document_id, patient_id)
                        self._update_patient_document(db, document_id, document_data)
                    else:
                        # Check if document with this GUID already exists
                        guid = document_data.get("guid")
                        existing_document = None
                        if guid:
                            self.logger.debug("Checking for existing document with GUID=%s", guid)
                            existing_document = db.query(PatientDocumentDB).filter(
                                PatientDocumentDB.guid == guid
                            ).first()
                        
                        if existing_document:
                            self.logger.debug("Found existing document with id=%s for GUID=%s, updating", 
                                             existing_document.id, guid)
                            self._update_patient_document(db, existing_document.id, document_data)
                        else:
                            self.logger.debug("Creating new document for patient_id=%s", patient_id)
                            document_data["patient_id"] = patient_id
                            document = self.create(db, PatientDocumentDB, **document_data)
                            self.logger.debug("Created document with id=%s for patient_id=%s", 
                                             document.id, patient_id)
            
            # Create the audit trail
            self.logger.debug("Creating audit trail for patient_id=%s", patient_id)
            audit_trail = PatientAuditTrailService().create_audit_trail(
                patient_id=patient_id, action="UPDATE", db=db
            )
            self.logger.debug("Created audit trail with id=%s for patient_id=%s", 
                             audit_trail.id if audit_trail else None, patient_id)
            
            self.logger.info("Updated patient with id=%s", patient_id)
            return True, updated_patient
        except Exception as e:
            self.logger.error("Error updating patient with id=%s: %s", patient_id, str(e), exc_info=True)
            db.rollback()
            raise

    def _update_patient(self, db: Session, patient_id: int, patient_data: dict):
        self.logger.debug("Updating patient attributes for patient_id=%s", patient_id)
        updated_patient = self.update(db, PatientDB, patient_id, **patient_data)
        return updated_patient

    def _update_patient_document(
        self, db: Session, patient_document_id: int, document_data: dict
    ):
        self.logger.debug("Updating patient document with id=%s", patient_document_id)
        try:
            updated_document = self.update(
                db, PatientDocumentDB, patient_document_id, **document_data
            )
            return updated_document
        except Exception as e:
            self.logger.error("Error updating patient document with id=%s: %s", 
                             patient_document_id, str(e), exc_info=True)
            raise

    def _update_address(self, db: Session, address_id: int, address_data: dict):
        self.logger.debug("Updating address with id=%s", address_id)
        updated_address = self.update(db, AddressDB, address_id, **address_data)
        return updated_address

    def delete_patient(self, patient_id: int, db: Session):
        """
        Delete a patient.
        
        Args:
            patient_id (int): The ID of the patient to be deleted.
            db (Session): The database session.
        
        Returns:
            Tuple[bool, Union[str, PatientExceptions]]: A tuple containing a boolean indicating the success of the deletion
            operation and a message or an error object.
        
        Raises:
            None
        
        """
        try:
            self.logger.info("Deleting patient with id=%s", patient_id)
            
            # Check if the patient exists
            is_exist, patient = self.get_patient_by_id(patient_id, db)
            if not is_exist:
                self.logger.warning("Patient not found with id=%s", patient_id)
                return False, patient
            
            # Check if the patient has any cases
            cases = (
                db.query(Case)
                .filter(Case.patient_id == patient_id)
                .all()
            )
            if cases:
                self.logger.warning("Cannot delete patient with id=%s as it has associated cases", patient_id)
                return False, "Patient has associated cases and cannot be deleted."
            
            # Delete the patient
            self.logger.debug("Deleting patient with id=%s", patient_id)
            db.delete(patient)
            db.commit()
            
            self.logger.info("Deleted patient with id=%s", patient_id)
            return True, "Patient deleted successfully."
        except Exception as e:
            self.logger.error("Error deleting patient with id=%s: %s", patient_id, str(e), exc_info=True)
            db.rollback()
            return False, str(e)

    def search_patients(
        self, query_params: dict, skip_count: int, limit: int, db: Session
    ):
        """
        Search for active patients based on query parameters.
        
        Args:
            query_params (dict): The search query parameters.
            skip_count (int): The number of records to skip.
            limit (int): The maximum number of records to return.
            db (Session): The database session.
        
        Returns:
            Tuple[List[PatientDB], int]: A tuple containing a list of patients and the total count.
        """
        try:
            self.logger.info("Searching for patients with params: %s", query_params)
            
            # Base query with distinct to avoid duplicates
            base_query = db.query(PatientDB).distinct().filter(PatientDB.is_active == True)
            
            # Apply filters
            if query_params:
                self.logger.debug("Applying filters to patient search")
                for key, value in query_params.items():
                    if value:
                        if key == "first_name":
                            base_query = base_query.filter(
                                PatientDB.first_name.ilike(f"%{value}%")
                            )
                        elif key == "last_name":
                            base_query = base_query.filter(
                                PatientDB.last_name.ilike(f"%{value}%")
                            )
                        elif key == "aadhar_number":
                            base_query = base_query.filter(
                                PatientDB.aadhar_number.ilike(f"%{value}%")
                            )
                        elif key == "phone_number":
                            base_query = base_query.filter(
                                PatientDB.phone_number.ilike(f"%{value}%")
                            )
                        elif key == "ration_card_number":
                            if value:
                                base_query = base_query.filter(
                                    PatientDB.ration_card_number.isnot(None),
                                    func.trim(func.lower(PatientDB.ration_card_number)) == 
                                    value.strip().lower()
                                )
                        elif key == "arogya_card_no":
                            # Handle None values and ensure string comparison
                            if value:
                                base_query = base_query.filter(
                                    PatientDB.arogya_card_no.isnot(None),
                                    func.trim(func.lower(PatientDB.arogya_card_no)) == 
                                    value.strip().lower()
                                )
                        elif key == "gender":
                            base_query = base_query.filter(func.lower(PatientDB.gender) == value.strip().lower())
                        elif key == "age":
                            base_query = base_query.filter(PatientDB.age == value)
                        elif key == "combined_name":
                            # Search in both first_name and last_name
                            base_query = base_query.filter(
                                or_(
                                    PatientDB.first_name.ilike(f"%{value}%"),
                                    PatientDB.last_name.ilike(f"%{value}%"),
                                )
                            )
            
            # Apply ordering to the query - ensure consistent ordering with a unique field (id)
            ordered_query = base_query.order_by(PatientDB.date_of_enrollement.desc(), PatientDB.id.desc())
            
            # Count total records - using a separate query without ordering to avoid performance issues
            total_count = base_query.count()
            self.logger.debug("Total patients found: %s", total_count)
            
            # Fix: Adjust skip_count if it would skip all results
            if skip_count >= total_count and total_count > 0:
                self.logger.warning("skip_count (%s) >= total_count (%s), resetting to 0", 
                                skip_count, total_count)
                skip_count = 0
            
            # Apply pagination
            self.logger.info("Applying pagination with skip_count=%s and limit=%s", skip_count, limit)
            patients = ordered_query.offset(skip_count).limit(limit).all()
            self.logger.debug("Returning %s patients", len(patients))
            
            return patients, total_count
        except Exception as e:
            self.logger.error("Error searching patients: %s", str(e), exc_info=True)
            raise

    def is_patient_exists_by_id(self, patient_id: int):
        """Check if a Patient ID exists."""
        try:
            self.logger.debug("Checking if patient exists with id=%s", patient_id)
            is_exist, _ = self.get_patient_by_id(patient_id=patient_id, db=self.db)
            return is_exist
        except Exception as e:
            self.logger.error("Error checking if patient exists with id=%s: %s", 
                             patient_id, str(e), exc_info=True)
            raise


class PatientAuditTrailService(BaseService):
    """
    Service class for managing patient audit trail-related operations.
    """

    def __init__(self) -> None:
        self.db = SessionLocal()
        self.logger = setup_logger(log_file="patient_audit.log")

    def create_audit_trail(self, patient_id: int, action: str, db: Session):
        """
        Create a new audit trail entry for a patient.
        
        Args:
            patient_id (int): The ID of the patient.
            action (str): The action performed on the patient.
            db (Session): The database session.
        
        Returns:
            PatientAuditTrailDB: The newly created audit trail entry.
        
        Raises:
            Exception: If an error occurs while creating the audit trail entry.
        """
        try:
            self.logger.info("Creating audit trail for patient_id=%s, action=%s", patient_id, action)
            
            # Create the audit trail entry
            audit_trail_data = {
                "patient_id": patient_id,
                "action": action,
            }
            audit_trail = self.create(db, PatientAuditTrailDB, **audit_trail_data)
            
            self.logger.info("Created audit trail with id=%s for patient_id=%s", 
                            audit_trail.id, patient_id)
            return audit_trail
        except Exception as e:
            self.logger.error("Error creating audit trail for patient_id=%s: %s", 
                             patient_id, str(e), exc_info=True)
            raise

    def get_audit_trail_by_patient_id(self, patient_id: int, db: Session):
        """Retrieve the audit trail entries for a patient by patient ID.

        Args:
            patient_id (int): The ID of the patient.
            db (Session): The database session.

        Returns:
            List[PatientAuditTrailDB]: A list of audit trail entries for the patient.

        Raises:
            Exception: If an error occurs while retrieving the audit trail entries.
        """
        try:
            self.logger.info("Retrieving audit trail for patient_id=%s", patient_id)
            
            # Query the audit trail entries for the patient from the database
            audit_trail_entries = (
                db.query(PatientAuditTrailDB)
                .filter(PatientAuditTrailDB.patient_id == patient_id)
                .all()
            )
            
            self.logger.debug("Retrieved %s audit trail entries for patient_id=%s", 
                             len(audit_trail_entries), patient_id)
            return audit_trail_entries
        except Exception as e:
            self.logger.error("Error retrieving audit trail for patient_id=%s: %s", 
                             patient_id, str(e), exc_info=True)
            raise e

    def delete_audit_trail_by_patient_id(self, patient_id: int, db: Session):
        """Delete the audit trail entries for a patient by patient ID.

        Args:
            patient_id (int): The ID of the patient.
            db (Session): The database session.

        Returns:
            bool: True if the audit trail entries are deleted successfully, False otherwise.

        Raises:
            Exception: If an error occurs while deleting the audit trail entries.
        """
        try:
            self.logger.info("Deleting audit trail for patient_id=%s", patient_id)
            
            # Delete the audit trail entries for the patient from the database
            count = self.delete_all(
                db=db,
                model=PatientAuditTrailDB,
                attribute=PatientAuditTrailDB.patient_id,
                value=patient_id,
            )
            
            if count == 0:
                self.logger.warning("No audit trail entries found for patient_id=%s", patient_id)
                return True, "Entry not found for given patient id"
                
            self.logger.info("Deleted %s audit trail entries for patient_id=%s", count, patient_id)
            return True, "Audit trail entries deleted successfully."
        except Exception as e:
            self.logger.error("Error deleting audit trail for patient_id=%s: %s", 
                             patient_id, str(e), exc_info=True)
            return False, "Failed to delete audit trail entries."


class ModuleDocumentMasterService(BaseService):
    """
    Service class for managing document master-related operations.
    """

    def __init__(self) -> None:
        self.db = SessionLocal()
        self.logger = setup_logger(log_file="module_document.log")

    def get_document_master_by_key(self, key: str, db: Session):
        """Retrieve a document master by key.

        Args:
            key (str): The key of the document master.
            db (Session): The database session.

        Returns:
            DocumentMasterDB: The document master with the given key.

        Raises:
            Exception: If an error occurs while retrieving the document master.
        """
        try:
            self.logger.info("Retrieving document master by key=%s", key)
            
            # Query the document master from the database
            kwargs = {"module_key": key, "is_active": True}
            document_master = self.get_patients_attributes(
                db, ModuleDocumentMasterDB, **kwargs
            )
            
            self.logger.debug("Retrieved %s document masters for key=%s", 
                             len(document_master) if document_master else 0, key)
            return document_master
        except Exception as e:
            self.logger.error("Error retrieving document master by key=%s: %s", 
                             key, str(e), exc_info=True)
            raise


class PatientUserHelper:

    @staticmethod
    def create_user_for_patient(patient_id, hospital_file_no, db: Session):
        logger = setup_logger(log_file="patient_user.log")
        try:
            logger.info("Creating user for patient_id=%s with hospital_file_no=%s", 
                       patient_id, hospital_file_no)
            
            is_exist, patient = PatientService().get_patient_by_id(
                patient_id=patient_id, db=db
            )
            
            if not is_exist:
                logger.warning("Patient not found with id=%s", patient_id)
                return None
                
            payload = {
                "first_name": patient.first_name,
                "last_name": patient.last_name,
                "password": hospital_file_no,
                "mobile_number": patient.phone_number,
                "email": PatientUserHelper.generate_unique_email(
                    first_name=patient.first_name, last_name=patient.last_name, db=db
                ),
            }
            
            logger.debug("Creating user with data: %s", payload)
            user = UserService().create_patient_user(user_data=payload, db=db)
            
            logger.info("Created user with id=%s for patient_id=%s", user.id, patient_id)
            return user
        except Exception as e:
            logger.error("Error creating user for patient_id=%s: %s", 
                        patient_id, str(e), exc_info=True)
            raise

    @staticmethod
    def generate_unique_email(first_name, last_name, db: Session, suffix=""):
        logger = setup_logger(log_file="patient_user.log")
        try:
            base_email = f"{first_name.lower()}.{last_name.lower()}{suffix}@arogyadoot.com"
            email = base_email
            counter = 1
            
            logger.debug("Generating unique email for %s %s", first_name, last_name)
            
            while db.query(UserDB).filter(UserDB.email == email).count() > 0:
                logger.debug("Email %s already exists, trying with counter=%s", email, counter)
                email = f"{first_name.lower()}.{last_name.lower()}{suffix}{counter}@arogyadoot.com"
                counter += 1
            
            logger.debug("Generated unique email: %s", email)
            return email
        except Exception as e:
            logger.error("Error generating unique email for %s %s: %s", 
                        first_name, last_name, str(e), exc_info=True)
            raise
