# from pydantic import BaseModel, validators, constr, validator

# from app.patients.services.document_type import DocumentTypeService

# class DocumentType(BaseModel):
#     type: str

# class DocumentTypeCreateSchema(DocumentType):
#     """Represents a document type creation schema."""

#     @validator("type")
#     def type_must_be_unique(cls, type):
#         """Validates if the document type is unique."""
#         document_type = DocumentTypeService().is_document_type_exists(type=type)
#         if document_type:
#             raise DocumentTypeAlreadyExistsError()
#         return type


# class DocumentTypeAlreadyExistsError(ValueError):
#     """Exception raised when a document type already exists."""

#     def __init__(self):
#         self.msg = "Document type already exists"
#         super().__init__(self.msg)

