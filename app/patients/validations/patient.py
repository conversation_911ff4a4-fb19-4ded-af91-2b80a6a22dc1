from datetime import datetime, <PERSON><PERSON>ta
from enum import Enum
from typing import List, Optional

from fastapi import Depends
from pydantic import (
    BaseModel,
    Field,
    constr,
    field_validator,
    model_validator,
    root_validator,
    validator,
)

from app.patients.models.patient import PatientDB
from app.validations.base import CustomBaseModel
from utils.response import BaseResponse


class Document(BaseModel):
    """
    Represents a document associated with a patient.

    Attributes:
        document_no (str): The document number.
        patient_id (int): The ID of the patient.
        guid (int): The GUID of the document.
        patient_document_master_id (int): The ID of the patient document master.
    """

    document_no: Optional[str] = None
    guid: str
    document_master_id: int


class Address(BaseModel):
    """
    Represents an address associated with a patient.

    Attributes:
        street (str): The street name of the address.
        city (str): The city of the address.
        state (str): The state of the address.
        country (str): The country of the address.
        postal_code (str): The postal code of the address.
    """

    address_line1: str
    address_line2: Optional[str] = None
    address_line3: Optional[str] = None
    city: int
    district: int
    state: int


class GenderEnum(str, Enum):
    Male = "Male"
    Female = "Female"
    Others = "Others"


class Patient(CustomBaseModel):
    """
    Represents a patient in the system.

    Attributes:
        first_name (str): The first name of the patient.
        last_name (str): The last name of the patient.
        gender (str): The gender of the patient.
        date_of_birth (datetime): The date of birth of the patient.
        date_of_death (Optional[datetime], optional): The date of death of the patient. Defaults to None.
        created_by (str): The user who created the patient record.
        updated_by (str): The user who last updated the patient record.
        documents (List[Document]): The list of documents associated with the patient.
    """

    first_name: str
    middle_name: str
    last_name: str
    gender: GenderEnum
    date_of_birth: datetime
    phone_number: str
    alternate_phone_number: Optional[str] = None
    relative_name: Optional[str] = None
    relative_phone_number: Optional[str] = None
    ration_card_number: Optional[str] = None
    date_of_enrollement: Optional[datetime] = None
    arogya_card_no: Optional[str] = None
    aadhar_number: Optional[str] = None
    documents: List[Document] = Field(...)
    address: Address = Field(...)

    @validator("first_name")
    def validate_first_name(cls, value):
        if not value.strip():  # Check if the value is empty or contains only whitespace
            raise ValueError("First name cannot be empty.")
        if value is not None and len(value) > 25:
            raise ValueError("First name cannot exceed 25 characters.")
        return value

    @validator("middle_name")
    def validate_middle_name(cls, value):
        if value is not None and len(value) > 25:
            raise ValueError("Middle name cannot exceed 25 characters.")
        return value

    @validator("last_name")
    def validate_last_name(cls, value):
        if not value.strip():  # Check if the value is empty or contains only whitespace
            raise ValueError("Last name cannot be empty.")
        if value is not None and len(value) > 25:
            raise ValueError("Last name cannot exceed 25 characters.")
        return value

    @validator("relative_name")
    def validate_relative_name(cls, value):
        if (
            value and not value.strip()
        ):  # Check if the value is empty or contains only whitespace
            raise ValueError("Relative name cannot be empty.")
        if value is not None and len(value) > 25:
            raise ValueError("Relative name cannot exceed 25 characters.")
        return value

    @validator("date_of_birth")
    def validate_date_of_birth(cls, v):
        tomorrow = datetime.now(v.tzinfo) + timedelta(days=1)
        if v is not None and v > tomorrow:
            raise ValueError("Date of birth cannot be in the future.")
        return v

    @validator("date_of_enrollement")
    def validate_date_of_enrollement(cls, v):
        tomorrow = datetime.now(v.tzinfo) + timedelta(days=1)
        if v is not None and v > tomorrow:
            raise ValueError("Enrollment date cannot be in the future.")
        return v

    @model_validator(mode="before")
    def check_card_numbers(cls, values):
        ration_card_number = values.get("ration_card_number")
        arogya_card_no = values.get("arogya_card_no")
        aadhar_number = values.get("aadhar_number")

        if not (ration_card_number or arogya_card_no or aadhar_number):
            raise ValueError(
                "At least one of ration_card_number, arogya_card_no, or aadhar_number must be provided."
            )

        return values

    @validator("aadhar_number")
    def validate_aadhar_number(cls, v):
        if v is not None:
            if not v.isnumeric() or len(v) != 12:
                raise ValueError("Aadhaar number must be a 12-digit numeric value.")
        return v

    @validator("arogya_card_no")
    def validate_arogya_card_no(cls, v):
        if v is not None:
            if len(v) > 15 or not v.strip():
                raise ValueError(
                    "Aarogya card number must not exceed 15 characters and cannot be empty."
                )
        return v

    @validator("ration_card_number")
    def validate_ration_card_number(cls, v):
        if v is not None:
            if len(v) > 15 or not v.strip():
                raise ValueError(
                    "Ration card number must not exceed 15 characters and cannot be empty."
                )
        return v

    @model_validator(mode="before")
    def check_phone_numbers(cls, values):
        phone_number = values.get("phone_number")
        alternate_phone_number = values.get("alternate_phone_number")
        relative_phone_number = values.get("relative_phone_number")
        if not (phone_number or alternate_phone_number or relative_phone_number):
            raise ValueError(
                "At least one of phone_number, alternate_phone_number, or relative_phone_number must be provided."
            )
        if not phone_number:
            raise ValueError("Phone number must be required.")

        phone_set = {phone_number, alternate_phone_number, relative_phone_number}
        phone_set.discard(None)  # Remove None values

        if len(phone_set) < sum(
            bool(phone)
            for phone in [phone_number, alternate_phone_number, relative_phone_number]
        ):
            raise ValueError(
                "Phone numbers (phone_number, alternate_phone_number, relative_phone_number) must be different."
            )

        return values

    class Config:
        """Configuration for the User model."""

        from_attributes = True


class PatientCreateSchema(Patient):

    class Config:
        """Configuration for the User model."""

        extra = "forbid"

    @field_validator("arogya_card_no")
    def validate_arogya_card_no(cls, v):
        cls.check_unique(field="arogya_card_no", value=v, model=PatientDB)
        return v

    @field_validator("aadhar_number")
    def validate_aadhar_number(cls, v):
        cls.check_unique(field="aadhar_number", value=v, model=PatientDB)
        return v

    @field_validator("ration_card_number")
    def validate_ration_card_number(cls, v):
        cls.check_unique(field="ration_card_number", value=v, model=PatientDB)
        return v


class PatientUpdateSchema(Patient):
    # patient_id: int

    class Config:
        extra = "forbid"


class PatientBaseResponse(BaseModel):
    id: int
    first_name: str
    middle_name: Optional[str]
    last_name: str
    gender: str
    date_of_birth: datetime
    phone_number: Optional[str]
    alternate_phone_number: Optional[str]
    relative_name: Optional[str]
    relative_phone_number: Optional[str]
    ration_card_number: Optional[str]
    arogya_card_no: Optional[str]
    aadhar_number: Optional[str]
    date_of_enrollement: datetime

    class Config:
        from_attributes = True


class PatientStateReadResponse(BaseModel):
    id: int
    name: str


class PatientDistrictReadResponse(BaseModel):
    id: int
    name: str


class PatientCityReadResponse(BaseModel):
    id: int
    name: str


class AddressReadResponse(Address):
    id: int

    class Config:
        from_attributes = True


class PatientDocumentResponse(BaseModel):
    id: int
    document_no: Optional[str] = None
    document_master_id: int
    guid: str

    class Config:
        from_attributes = True


class PatientReadBaseResponse(PatientBaseResponse):
    address: List[AddressReadResponse] = []
    documents: List[PatientDocumentResponse] = []


class PatientRetrievedResponse(BaseResponse):
    """
    Represents a response containing a list of patients.

    Attributes:
        data (List[Patient]): The list of patients.
    """

    data: Optional[PatientReadBaseResponse] = None


class PatientLoginRequest(BaseModel):
    phone_number: str
    hospital_file_no: str
