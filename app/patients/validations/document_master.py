from pydantic import BaseModel, validators, constr, validator

from app.patients.services.document_master import DocumentMasterService

class DocumentMaster(BaseModel):
    document_name: str

class DocumentMasterCreateSchema(DocumentMaster):
    """Represents a document master creation schema."""

    @validator("document_name")
    def name_must_be_unique(cls, document_name):
        """Validates if the document master is unique."""
        is_document_name = DocumentMasterService().is_document_master_exists(document_name=document_name)

        if is_document_name:
            raise DocumentMasterAlreadyExistsError()
        return document_name


class DocumentMasterAlreadyExistsError(ValueError):
    """Exception raised when a document master already exists."""

    def __init__(self):
        self.msg = "Document master already exists"
        super().__init__(self.msg)

