from pydantic import BaseModel, validators, constr, validator

from app.patients.services.document_master import DocumentMasterService

class PatientDocumentMaster(BaseModel):
    document_type_id: str

class DocumentMasterCreateSchema(PatientDocumentMaster):
    """Represents a patient document master creation schema."""

    @validator("document_type_id")
    def name_must_be_unique(cls, document_type_id):
        """Validates if the document master is unique."""
        is_document_type_id = PatientDocumentMasterService().is_patient_document_master_exists(document_type_id=document_type_id)

        if is_document_type_id:
            raise PatientDocumentMasterAlreadyExistsError()
        return document_type_id


class PatientDocumentMasterAlreadyExistsError(ValueError):
    """Exception raised when a patient document master already exists."""

    def __init__(self):
        self.msg = "patient Document master already exists"
        super().__init__(self.msg)


class AssignType(BaseModel):
    """Represents a request to assign roles to a user."""
    type_id: int
    is_required: bool
    