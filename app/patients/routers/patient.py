from typing import Union

from fastapi import APIRouter, Depends, status
from fastapi.responses import JSONResponse
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.future import select
from sqlalchemy.orm import Session

from app.cases.models import Case
from app.cases.services.case import CaseService, StatusMasterService
from app.exception.errors.user import AuthErrors
from app.master.models.document_master import DocumentMaster
from app.models.user import UserDB
from app.patients.exception.custom.patient import PatientErrors
from app.patients.models.patient import ModuleDocumentMasterDB, PatientDB
from app.patients.services.patient import (
    ModuleDocumentMasterService,
    PatientAuditTrailService,
    PatientService,
)
from app.patients.validations.patient import (
    PatientCreateSchema,
    PatientLoginRequest,
    PatientRetrievedResponse,
    PatientUpdateSchema,
)

# from app.database.database import engine, Base, SessionLocal
from app.services.user import AuthService
from utils.exception import CustomException, NotFoundException
from utils.generate_response import generate_response
from utils.helpers import Except<PERSON><PERSON><PERSON><PERSON>, ExtractUserAndAddToDict
from utils.schema import Error

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Function to get a database session
from app.database.database import get_db

# Create an API router
router = APIRouter()
patient_service = PatientService()
patient_audit_trail_service = PatientAuditTrailService()


@router.post("/patients")
async def create_patient(
    patient: PatientCreateSchema,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    """
    Create a new patient.

    Args:
        patient (Patient): The patient data.
        db (Session, optional): The database session. Defaults to Depends(get_db).

    Returns:
        dict: A dictionary containing the message if the patient is created successfully.

    Raises:
        JSONResponse: If there is an internal server error.
    """
    try:
        patient_data_old = patient.model_dump()
        patient_data_new = ExtractUserAndAddToDict.extract_user_and_add_to_dict(
            token, patient_data_old
        )
        is_success = patient_service.create_patient(
            patient_data=patient_data_new, db=db
        )
        return generate_response(
            status_code=status.HTTP_201_CREATED,
            message="Patient created successfully",
        )

    except Exception as e:
        return ExceptionHandler().handle_exception(e=e)


@router.get(
    "/patients/{patient_id}", response_model=Union[PatientRetrievedResponse, Error]
)
async def get_patient(patient_id: int, db: Session = Depends(get_db)):
    """
    Get a patient by ID.

    Args:
        patient_id (int): The ID of the patient to retrieve.
        db (Session, optional): The database session. Defaults to Depends(get_db).

    Returns:
        dict: A dictionary containing the patient data if found, or a message if the patient is not found.

    Raises:
        JSONResponse: If there is an internal server error.
    """
    try:
        patient = patient_service.get_patient_or_raise(patient_id, db)
        return generate_response(
            data=patient,
            status_code=status.HTTP_200_OK,
            message="Patient retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.delete("/patients/{patient_id}")
async def delete_patient(patient_id: int, db: Session = Depends(get_db)):
    """
    Delete a patient by ID.

    Args:
        patient_id (int): The ID of the patient to delete.
        db (Session, optional): The database session. Defaults to Depends(get_db).

    Returns:
        dict: A dictionary containing the message if the patient is deleted successfully, or a message if the patient is not found.

    Raises:
        JSONResponse: If there is an internal server error.
    """
    try:
        message = patient_service.delete_patient(patient_id, db)
        return generate_response(
            data={"message": message}, status_code=status.HTTP_200_OK
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.put("/patients/{patient_id}")
async def update_patient(
    patient_id: int,
    patient: PatientUpdateSchema,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    """
    Update a patient with the given patient_id.

    Args:
        patient_id (int): The ID of the patient to update.
        patient (Patient): The updated patient data.
        db (Session, optional): The database session. Defaults to Depends(get_db).

    Returns:
        dict: A dictionary containing the updated patient if successful, or a message if the patient is not found.

    Raises:
        JSONResponse: If there is an internal server error.
    """
    try:
        patient_data_old = patient.model_dump()
        patient_data_new = ExtractUserAndAddToDict.extract_user_and_add_to_dict_updated(
            token, patient_data_old
        )
        is_updated = patient_service.update_patient(patient_id, patient_data_new, db)
        return generate_response(
            message="Patient updated successfully.", status_code=status.HTTP_200_OK
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e=e)


@router.get("/patients")
async def search_patients(
    first_name: str = None,
    last_name: str = None,
    gender: str = None,
    phone_number: str = None,
    aadhar_number: str = None,
    ration_card_number: str = None,
    arogya_card_no: str = None,
    page: int = None,
    size: int = None,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    """
    Search for patients based on name and/or age.

    Args:
        name (str, optional): The name of the patient. Defaults to None.
        age (int, optional): The age of the patient. Defaults to None.
        db (Session, optional): The database session. Defaults to Depends(get_db).

    Returns:
        dict: A dictionary containing the list of patients matching the search criteria.

    Raises:
        JSONResponse: If there is an internal server error.
    """
    try:
        # Extract user from token
        user = AuthService().get_user_from_token(token)
        
        query_params = {
            "first_name": first_name,
            "last_name": last_name,
            "phone_number": phone_number,
            "aadhar_number": aadhar_number,
            "gender": gender,
            "ration_card_number": ration_card_number,
            "arogya_card_no": arogya_card_no,
            "user_id": user.id if user else None,
        }
        # Calculate skip_count based on page and size
        skip_count = 0
        limit = 10  # Default limit
        
        if page is not None and page > 0:
            if size is not None and size > 0:
                limit = size
                skip_count = (page - 1) * size
            else:
                skip_count = (page - 1) * 10  # Default to 10 items per page
        
        patients, total_count = patient_service.search_patients(
            query_params, skip_count, limit, db=db
        )
        return generate_response(
            data=patients,
            total_count=total_count,
            status_code=status.HTTP_200_OK,
            message="Patients retrieved successfully.",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.get("/patients/{patient_id}/audit-trail")
async def get_audit_trail(patient_id: int, db: Session = Depends(get_db)):
    """
    Get the audit trail for a patient by ID.

    Args:
        patient_id (int): The ID of the patient.
        db (Session, optional): The database session. Defaults to Depends(get_db).

    Returns:
        dict: A dictionary containing the audit trail data if found, or a message if the patient is not found.

    Raises:
        JSONResponse: If there is an internal server error.
    """
    try:
        audit_trail = patient_audit_trail_service.get_audit_trail_by_patient_id(
            patient_id=patient_id, db=db
        )
        if audit_trail:
            return generate_response(data=audit_trail, status_code=status.HTTP_200_OK)
        else:
            return generate_response(
                custom_response={"message": "Audit trail not found"},
                status_code=status.HTTP_404_NOT_FOUND,
            )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.delete("/patients/{patient_id}/audit-trail")
async def delete_audit_trail(patient_id: int, db: Session = Depends(get_db)):
    """
    Delete the audit trail for a patient by ID.

    Args:
        patient_id (int): The ID of the patient.
        db (Session, optional): The database session. Defaults to Depends(get_db).

    Returns:
        dict: A dictionary containing the message if the audit trail is deleted successfully, or a message if the patient is not found.

    Raises:
        JSONResponse: If there is an internal server error.
    """
    try:
        is_deleted, message = (
            patient_audit_trail_service.delete_audit_trail_by_patient_id(
                patient_id=patient_id, db=db
            )
        )
        if is_deleted:
            return generate_response(
                data={"message": message}, status_code=status.HTTP_200_OK
            )
        else:
            return generate_response(
                custom_response={"message": message},
                status_code=status.HTTP_404_NOT_FOUND,
            )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.get("/moduledocumentmaster/{key}")
async def get_moduledocumentmaster(key: str, db: Session = Depends(get_db)):
    """
    Get the document list for a given key.

    Args:
        key (str): The key to retrieve the document list.
        db (Session, optional): The database session. Defaults to Depends(get_db).

    Returns:
        dict: A dictionary containing the document list if found, or a message if the key is not found.

    Raises:
        JSONResponse: If there is an internal server error.
    """
    try:
        document_list = ModuleDocumentMasterService().get_document_master_by_key(
            key=key, db=db
        )
        if not document_list:
            return generate_response(
                custom_response={"message": "Document list not found"},
                status_code=status.HTTP_404_NOT_FOUND,
            )

        # Fetch document master names based on document_master_id in one query
        document_list_with_names = (
            db.query(ModuleDocumentMasterDB, DocumentMaster.name)
            .join(
                DocumentMaster,
                ModuleDocumentMasterDB.document_master_id == DocumentMaster.id,
            )
            .filter(ModuleDocumentMasterDB.module_key == key)
            .all()
        )

        # Update the response data with document master names
        response_data = []
        for doc, master_name in document_list_with_names:
            doc_dict = doc.__dict__
            doc_dict["document_master_name"] = master_name
            response_data.append(doc_dict)

        return generate_response(data=response_data, status_code=status.HTTP_200_OK)
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Pydantic schema for login request


@router.post("/patient/login")
async def patient_login(
    login_request: PatientLoginRequest, db: Session = Depends(get_db)
):
    phone_number = login_request.phone_number
    hospital_file_no = login_request.hospital_file_no
    try:
        # Query the patient from the database
        patient_query = db.query(PatientDB).filter(
            PatientDB.phone_number == phone_number
        )
        patient = patient_query.first()
        if not patient:
            raise PatientErrors().raise_patient_not_found_exception(phone_number)

        # Check if the hospital_file_no matches any active case
        active_case_status_list = [
            "Pre-Investigation Pending",
            "Pre-Investigation Submitted",
            "PreAuth Pending",
            "PreAuth Submitted",
            "PreAuth Approved",
            "PreAuth Rejected",
            "Treatment document Pending",
            "Treatment document Submitted",
            "Post-Investigation document Pending",
            "Post-Investigation document Submitted",
            "Discharge document Pending",
            "Discharge document Submitted",
        ]
        status_ids = StatusMasterService().get_status_ids(db, active_case_status_list)
        # Check if the hospital_file_no matches any active case
        case_query = db.query(Case).filter(
            Case.patient_id == patient.id,
            Case.hospital_file_no == hospital_file_no,
            # Replace with actual active status values
            Case.status.in_(status_ids),
        )
        active_cases = case_query.all()
        # if not active_cases:
        #     raise AuthErrors().raise_invalid_creadentials_exception(
        #         login_request.dict()
        #     )
        users = db.query(UserDB).filter(UserDB.mobile_number == phone_number)
        for user in users:

            if AuthService().verify_password(hospital_file_no, user.password):
                # Generate JWT token
                token = AuthService().generate_token(user.id)

        MJP_JAY_SCHEME_ID = 1  # Replace with actual ID for MJP-JAY
        case_service = CaseService()
        # Construct the detailed case data
        cases = [
            {
                "id": case.id,
                "case_no": case.govt_case_id,
                "procedure_code": case_service.get_combination_code_from_case(
                    case, MJP_JAY_SCHEME_ID
                ),
                "procedure_name": case.package.procedure_name,
                "hospital_file_number": case.hospital_file_no,
                "pre_auth_status_date": case.pre_auth_status_date,
            }
            for case in active_cases
        ]

        data = {
            "token": token,
            "cases": cases,
            "first_name": patient.first_name,
            "last_name": patient.last_name,
            "patient_id": patient.id,
            "user_id": user.id,
        }

        return generate_response(
            data=data, message="Login successful", status_code=status.HTTP_200_OK
        )

    except Exception as e:
        return ExceptionHandler().handle_exception(e)
