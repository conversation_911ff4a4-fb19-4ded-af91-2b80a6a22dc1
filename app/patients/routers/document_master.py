from fastapi import APIRouter, Depends, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from app.database.database import Base, SessionLocal, engine
from app.patients.services.document_master import DocumentMasterService
from app.patients.validations.document_master import (
    DocumentMaster, DocumentMasterCreateSchema)
from utils.common import get_db
from utils.generate_response import generate_response
from utils.helpers import Exception<PERSON>andler, ExtractUserAndAddToDict

router = APIRouter()
document_master_service = DocumentMasterService()


@router.post("/document-master")
async def create_document_master(document_master: DocumentMasterCreateSchema, db: Session = Depends(get_db)):
    """
    Create a new document master.

    Parameters:
    - document_master: The data of the document master to be created.
    - db: The database session.

    Returns:
    - The created document master.
    """
    try:
        created_document_master = document_master_service.create_document_master(document_master_data=document_master, db=db)
        return generate_response(
            data=created_document_master,
            status_code=status.HTTP_201_CREATED,
            message="Document master created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)



@router.get("/document-master/{document_master_id}")
async def get_document_master(document_master_id: int, db: Session = Depends(get_db)):
    """
    Get a document master by ID.

    Parameters:
    - document_master_id: The ID of the document master.
    - db: The database session.

    Returns:
    - The document master with the given ID if found, or a not found message.
    """
    try:
        is_exist, document_master = document_master_service.get_document_master_by_id(document_master_id, db)
        if is_exist:
            return generate_response(data=document_master, status_code=status.HTTP_200_OK)
        else:
            return generate_response(
                custom_response={"message": "Document master not found for given id."},
                status_code=status.HTTP_404_NOT_FOUND,
            )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)



@router.get("/document-master")
async def get_all_document_master(db: Session = Depends(get_db)):
    """
    Get all document masters.

    Parameters:
    - db: The database session.

    Returns:
    - All document masters.
    """
    try:
        document_master = document_master_service.get_all_document_master(db)
        return generate_response(data=document_master, status_code=status.HTTP_200_OK)
    except Exception as e:
        return ExceptionHandler().handle_exception(e)



@router.delete("/document-master/{document_master_id}")
async def delete_document_type(document_master_id: int, db: Session = Depends(get_db)):
    """
    Delete a document master by ID.

    Parameters:
    - document_master_id: The ID of the document master.
    - db: The database session.

    Returns:
    - A success message if the document master is deleted, or a not found message.
    """
    try:
        is_deleted, message = document_master_service.delete_document_master(document_master_id, db)
        if is_deleted:
            return generate_response(
                data={"message": f'Id {message} deleted successfully'}, status_code=status.HTTP_200_OK
            )
        else:
            return generate_response(
                custom_response={"message": message},
                status_code=status.HTTP_404_NOT_FOUND,
            )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)



@router.put("/document-master/{document_master_id}")
async def update_document_type(
    document_master_id: int, document_master: DocumentMaster, db: Session = Depends(get_db)
):
    """
    Update a document master by ID.

    Parameters:
    - document_master_id: The ID of the document master.
    - document_master: The updated data of the document master.
    - db: The database session.

    Returns:
    - The updated document master if it is updated, or the updated data if not updated.
    """
    try:
        is_updated, updated_document_type = document_master_service.update_document_master(
            document_master_id, document_master, db
        )
        if is_updated:
            return generate_response(
                data=updated_document_type, status_code=status.HTTP_200_OK
            )
        else:
            return generate_response(
                custom_response=updated_document_type.dict()
            )

    except Exception as e:
        return ExceptionHandler().handle_exception(e)

