import unittest
from fastapi.testclient import TestClient
from app.main import app
from app.patients.routers.document_master import update_document_master

class TestDocumentMasterRoutes(unittest.TestCase):
    def setUp(self):
        self.client = TestClient(app)

    def test_update_document_master_success(self):
        # Prepare test data
        document_master_id = 1
        document_master = {
            "name": "Updated Document Master"
        }

        # Make the request
        response = self.client.put(f"/document-masters/{document_master_id}", json=document_master)

        # Check the response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"data": {"name": "Updated Document Master"}})

    def test_update_document_master_not_found(self):
        # Prepare test data
        document_master_id = 999
        document_master = {
            "name": "Updated Document Master"
        }

        # Make the request
        response = self.client.put(f"/document-masters/{document_master_id}", json=document_master)

        # Check the response
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.json(), {"message": "Document master not found"})

    def test_update_document_master_internal_server_error(self):
        # Prepare test data
        document_master_id = 1
        document_master = {
            "name": "Updated Document Master"
        }

        # Mock the service function to raise an exception
        def mock_update_document_master(*args, **kwargs):
            raise Exception("Some error occurred")

        # Replace the original function with the mock
        update_document_master.__globals__["document_master_service"].update_document_master = mock_update_document_master

        # Make the request
        response = self.client.put(f"/document-masters/{document_master_id}", json=document_master)

        # Check the response
        self.assertEqual(response.status_code, 500)
        self.assertEqual(response.json(), {"message": "Internal server error"})
        
    def test_create_document_master_success(self):
        # Prepare test data
        document_master = {
            "name": "New Document Master"
        }
        # Make the request
        response = self.client.post("/document-masters", json=document_master)
        # Check the response
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.json(), {"data": {"name": "New Document Master"}})
        
    def test_get_all_document_masters_success(self):
        # Make the request
        response = self.client.get("/document-masters")
        # Check the response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"data": []})  # Assuming an empty list for now

    def test_get_document_master_by_id_success(self):
        # Prepare test data
        document_master_id = 1
        # Make the request
        response = self.client.get(f"/document-masters/{document_master_id}")
        # Check the response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"data": {"name": "Document Master"}})  # Replace with actual data

    def test_update_document_master_success(self):
        # Prepare test data
        document_master_id = 1
        document_master = {
            "name": "Updated Document Master"
        }
        # Make the request
        response = self.client.put(f"/document-masters/{document_master_id}", json=document_master)
        # Check the response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"data": {"name": "Updated Document Master"}})

    def test_delete_document_master_success(self):
        # Prepare test data
        document_master_id = 1
        # Make the request
        response = self.client.delete(f"/document-masters/{document_master_id}")
        # Check the response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"message": "Document master deleted"})


if __name__ == '__main__':
    unittest.main()
