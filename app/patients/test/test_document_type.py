import unittest
from fastapi.testclient import TestClient
from app.main import app
from app.patients.routers.document_type import update_document_type

class TestDocumentTypeRoutes(unittest.TestCase):
    def setUp(self):
        self.client = TestClient(app)

    def test_update_document_type_success(self):
        # Prepare test data
        document_type_id = 1
        document_type = {
            "name": "Updated Document Type"
        }

        # Make the request
        response = self.client.put(f"/document-types/{document_type_id}", json=document_type)

        # Check the response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"data": {"name": "Updated Document Type"}})

    def test_update_document_type_not_found(self):
        # Prepare test data
        document_type_id = 999
        document_type = {
            "name": "Updated Document Type"
        }

        # Make the request
        response = self.client.put(f"/document-types/{document_type_id}", json=document_type)

        # Check the response
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.json(), {"message": "Document type not found"})

    def test_update_document_type_internal_server_error(self):
        # Prepare test data
        document_type_id = 1
        document_type = {
            "name": "Updated Document Type"
        }

        # Mock the service function to raise an exception
        def mock_update_document_type(*args, **kwargs):
            raise Exception("Some error occurred")

        # Replace the original function with the mock
        update_document_type.__globals__["document_type_service"].update_document_type = mock_update_document_type

        # Make the request
        response = self.client.put(f"/document-types/{document_type_id}", json=document_type)

        # Check the response
        self.assertEqual(response.status_code, 500)
        self.assertEqual(response.json(), {"message": "Internal server error"})
        
    def test_create_document_type_success(self):
        # Prepare test data
        document_type = {
            "name": "New Document Type"
        }
        # Make the request
        response = self.client.post("/document-types", json=document_type)
        # Check the response
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.json(), {"data": {"name": "New Document Type"}})
        
    def test_get_all_document_types_success(self):
        # Make the request
        response = self.client.get("/document-types")
        # Check the response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"data": []})  # Assuming an empty list for now

    def test_get_document_type_by_id_success(self):
        # Prepare test data
        document_type_id = 1
        # Make the request
        response = self.client.get(f"/document-types/{document_type_id}")
        # Check the response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"data": {"name": "Document Type"}})  # Replace with actual data

    def test_update_document_type_success(self):
        # Prepare test data
        document_type_id = 1
        document_type = {
            "name": "Updated Document Type"
        }
        # Make the request
        response = self.client.put(f"/document-types/{document_type_id}", json=document_type)
        # Check the response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"data": {"name": "Updated Document Type"}})

    def test_delete_document_type_success(self):
        # Prepare test data
        document_type_id = 1
        # Make the request
        response = self.client.delete(f"/document-types/{document_type_id}")
        # Check the response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"message": "Document type deleted"})


if __name__ == '__main__':
    unittest.main()