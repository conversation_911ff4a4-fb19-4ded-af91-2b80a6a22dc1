import os
import threading
from typing import List, Optional, Union

from fastapi import (
    APIRouter,
    Body,
    Depends,
    File,
    HTTPException,
    Query,
    UploadFile,
    status,
)
from fastapi.responses import JSONResponse
from itsdangerous import URLSafeTimedSerializer
from pydantic import constr
from sqlalchemy.orm import Session

from app.database.database import SessionLocal
from app.services.user import (
    AuthService,
    PasswordService,
    UserDB,
    UserEmailService,
    UserService,
)
from app.validations.user import (
    OTP,
    AuthenticationSchema,
    MobileNumberAuth,
    PasswordReset,
    User,
    UserCreateSchema,
    UserFilterPaginationResponse,
    UserIdValidator,
    UserListResponse,
    UserRetrievedResponse,
    UserUpdate,
)
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler
from utils.mapper import AttributeMapper
from utils.otp import generate_otp, send_otp
from utils.panda import PandaUtils
from utils.schema import Error

s = URLSafeTimedSerializer(os.getenv("SECRET_KEY"))


# Function to get a database session
from app.database.database import get_db

# Create an API router
router = APIRouter()
user_service = UserService()
auth_service = AuthService()


# Endpoint to get all users
@router.get("/users")
def get_users(db: Session = Depends(get_db), response_model=User):
    try:
        users = user_service.get_all_users(db)
        return generate_response(
            data=users,
            status_code=status.HTTP_200_OK,
            message="Users retrieved successfully",
        )
    except Exception as e:
        
        return ExceptionHandler().handle_exception(e)


@router.get("/users/filter", response_model=UserFilterPaginationResponse)
def read_users(
    db: Session = Depends(get_db),
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1),
    order_by: str = None,
    first_name: str = Query(None, min_length=3),
    last_name: str = Query(None, min_length=3),
    email: str = None,
    mobile_number: str = None,
    is_active: bool = None,
    roles: List[int] = Query(None),
):
    try:
        users, total_count = user_service.filter_and_sort_users(
            db=db,
            page=page,
            limit=size,
            order_by=order_by,
            first_name=first_name,
            last_name=last_name,
            email=email,
            mobile_number=mobile_number,
            is_active=is_active,
            roles=roles,
        )
        return generate_response(
            total_count=total_count,
            data=users,
            status_code=status.HTTP_200_OK,
            message="Users Filtered successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to get a specific user by ID
@router.get("/users/{user_id}", response_model=Union[UserRetrievedResponse, Error])
def get_user(user_id: int, db: Session = Depends(get_db)):
    try:
        content = user_service.get_user_or_raise(user_id, db)
        return generate_response(
            data=content,
            status_code=status.HTTP_200_OK,
            message="User retrieved successfully",
        )
    except Exception as e:

        return ExceptionHandler().handle_exception(e)


# Endpoint to create a new user
@router.post("/users", response_model=Union[UserRetrievedResponse, Error])
async def create_user(user: UserCreateSchema, db: Session = Depends(get_db)):
    """
    Create a new user.

    Args:
        user (UserCreateSchema): The user data to be created.
        db (Session, optional): The database session. Defaults to Depends(get_db).

    Returns:
        dict: A dictionary containing the created user.

    Raises:
        JSONResponse: If there is an internal server error.
    """
    try:
        created_user = user_service.create_user(db=db, user_data=user)
        return generate_response(
            data=created_user,
            status_code=status.HTTP_201_CREATED,
            message="User created successfully",
        )
    except Exception as e:
        
        return ExceptionHandler().handle_exception(e)


# Endpoint to update a user by ID
@router.put("/users/{user_id}", response_model=Union[UserRetrievedResponse, Error])
async def update_user(user_id: int, user: UserUpdate, db: Session = Depends(get_db)):
    """
    Update a user with the given user_id.

    Args:
        user_id (int): The ID of the user to update.
        user (UserCreateSchema): The updated user data.
        db (Session, optional): The database session. Defaults to Depends(get_db).

    Returns:
        dict: A dictionary containing the updated user if successful, or a message if the user is not found.

    Raises:
        JSONResponse: If there is an internal server error.
    """
    try:
        # validated_user_id = UserIdValidator(id=user_id)
        updated_user = user_service.update_user(user_id, user, db)
        return generate_response(
            data=updated_user,
            status_code=status.HTTP_200_OK,
            message="User updated successfully",
        )

    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.post("/login")
async def login(user_details: AuthenticationSchema, db: Session = Depends(get_db)):
    """
    Authenticates the user with the provided user details.

    Args:
        user_details (AuthenticationSchema): The user details for authentication.
        db (Session, optional): The database session. Defaults to Depends(get_db).

    Returns:
        dict: A dictionary containing the login result message and token if successful.
    """
    try:
        user, token = auth_service.authenticate_user(user_details=user_details, db=db)

        # Generate token here
        is_exist, user = user_service.get_user_by_email(user_details.email, db)
        if not is_exist:
            return generate_response(custom_response=user.dict())
        roles, groups, permissions = user_service.get_user_roles_group_perms(
            email=user_details.email, db=db
        )
        data = AttributeMapper(
            token=token,
            roles=roles,
            groups=groups,
            permissions=permissions,
            first_name=user.first_name,
            last_name=user.last_name,
            id=user.id,
        ).__dict__
        return generate_response(
            data=data,
            status_code=status.HTTP_200_OK,
            message="Login successful",
        )
    except Exception as e:
        
        return ExceptionHandler().handle_exception(e)


# Endpoint for login with OTP
@router.post("/login/otp")
async def login_with_otp(
    mobile_number: MobileNumberAuth, db: Session = Depends(get_db)
):
    """
    Endpoint to log in with OTP.

    Args:
        mobile_number (MobileNumberAuth): The user's mobile number.
        db (Session, optional): The database session. Defaults to Depends(get_db).

    Returns:
        dict: A dictionary containing the message and OTP sent to the user's phone number.

    Raises:
        JSONResponse: If there is an internal server error.
    """
    try:
        # Generate OTP and send it to the user's phone number
        is_exists, content = user_service.get_user_by_mobile_number(
            mobile_number.mobile_number, db
        )
        if is_exists:
            otp = check_and_generate_otp(mobile_number, db)
            if otp == False:
                return generate_response(
                    status_code=status.HTTP_200_OK,
                    message="OTP already sent. Please wait for 3 minutes",
                )
            else:
                otp = send_otp(mobile_number, otp)
                auth_service.save_otp(mobile_number, otp, db)
                return generate_response(
                    data=AttributeMapper(otp=otp).__dict__,
                    status_code=status.HTTP_200_OK,
                    message="OTP sent successfully",
                )
        else:
            return generate_response(custom_response=content.dict())

    except Exception as e:
        
        return ExceptionHandler().handle_exception(e)


# Endpoint to verify OTP
@router.post("/login/otp/verify")
async def verify_otp(otp_details: OTP, db: Session = Depends(get_db)):
    """
    Verify the OTP entered by the user and generate a token if the OTP is valid.

    Args:
        otp_details (OTP): The OTP details entered by the user.
        db (Session, optional): The database session. Defaults to Depends(get_db).

    Returns:
        dict: A dictionary containing the message and token if the OTP is valid, or an error message if the OTP is invalid.

    Raises:
        JSONResponse: If there is an internal server error.
    """
    try:
        is_valid = auth_service.verify_otp(
            otp_details.mobile_number, otp_details.otp, db
        )
        if is_valid.status_code == 200:
            user = auth_service.get_user_by_phone_number(otp_details.mobile_number, db)
            token = auth_service.generate_token(user.id)
            return generate_response(
                data=token, status_code=status.HTTP_200_OK, message="Login successful"
            )
        else:
            return {"message": "Invalid OTP or Regenerate the OTP"}
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# shift below functions into utils.
# Function to generate a 6-digit OTP
def check_and_generate_otp(mobile_number, db):
    # Check if there is an existing valid OTP
    if auth_service.is_existing_valid_otp(mobile_number.mobile_number, db):
        return False

    return generate_otp()


# Endpoint to list users with pagination and filter support
@router.get("/users-list", response_model=UserFilterPaginationResponse)
def list_users(
    page: Optional[int] = 1,
    size: Optional[int] = 10,
    value: Optional[constr(min_length=3)] = Query(None),
    db: Session = Depends(get_db),
):
    try:
        users, total_count = user_service.filter_users_by_combined_name(
            page=page, limit=size, value=value, db=db
        )

        return generate_response(
            total_count=total_count,
            data=users,
            status_code=status.HTTP_200_OK,
            message="Users retrieved successfully",
        )
    except Exception as e:
        
        return ExceptionHandler().handle_exception(e)


@router.post("/users/upload", response_model=UserListResponse)
async def upload_file(file: UploadFile = File(...), db: Session = Depends(get_db)):
    try:
        contents = await file.read()
        df = PandaUtils().convert_to_dataframe(contents=contents)
        user_created = user_service.bulk_create_users(df=df, db=db)

        return generate_response(
            data=user_created,
            status_code=status.HTTP_201_CREATED,
            message="Bulk Users created successfully",
        )

    except Exception as e:
        
        return ExceptionHandler().handle_exception(e)


@router.post("/password-reset/{email}")
async def reset_password(
    email: str, is_web: bool = Query(False), db: Session = Depends(get_db)
):
    try:

        is_exist, user = user_service.get_user_by_email(db=db, email=email)
        if not is_exist:
            # return generate_response(custom_response=user.dict())
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND, content={"message": user}
            )
        reset_pass_link = PasswordService().get_reset_password_link(email, is_web)
        email_service = UserEmailService(user=user, password="")

        threading.Thread(
            target=email_service.send_forgot_password_email,
            args=(email, reset_pass_link),
        ).start()
        # if is_sent:
        return generate_response(
            status_code=status.HTTP_200_OK,
            message="Reset link has been sent to your email",
        )
        # else:
        #     return generate_response(status_code=status.HTTP_400_BAD_REQUEST, message="Something went wrong")
    except Exception as e:
        
        return ExceptionHandler().handle_exception(e)


@router.put("/password-reset/{token}")
async def update_password(
    token: str, password: PasswordReset = Body(...), db: Session = Depends(get_db)
):
    try:
        flag, content = PasswordService().reset_password(token, password)

        if flag:
            return generate_response(
                status_code=status.HTTP_200_OK, message="Your password has been updated"
            )

    except Exception as e:
        return ExceptionHandler().handle_exception(e)



# Endpoint to delete a step by ID
@router.delete("/users/{user_id}")
async def delete_user(user_id: int,force_delete:bool=Query(False), db: Session = Depends(get_db)):
    try:
        user_deleted = user_service.delete_user(user_id=user_id, db=db,force_delete=force_delete)
        return generate_response(
            status_code=status.HTTP_200_OK, message=user_deleted
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)
