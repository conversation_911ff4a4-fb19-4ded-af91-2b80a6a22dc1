from fastapi import APIRouter, Depends, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

# Function to get a database session
from app.database.database import SessionLocal, get_db
from app.services.authorisation.role import RoleService
from app.validations.authorisation.role import Role, RoleCreateSchema
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler

# Create an API router
router = APIRouter()


@router.post("/roles")
def create_role(
    role: RoleCreateSchema,
    db: Session = Depends(get_db),
    response_model=RoleCreateSchema,
):
    try:
        # Create the role using the role service
        created_role = RoleService().create_role(role_data=role, db=db)
        return generate_response(
            data=created_role,
            status_code=status.HTTP_201_CREATED,
            message="Role created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.get("/roles")
def list_roles(db: Session = Depends(get_db)):
    try:
        # Get the list of roles using the role service
        roles = RoleService().get_all_roles(db=db)
        return generate_response(
            data=roles,
            status_code=status.HTTP_200_OK,
            message="Roles listed successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.get("/roles/{role_id}")
def get_role(role_id: int, db: Session = Depends(get_db)):
    try:
        # Get the role by id using the role service
        is_exist, role = RoleService().get_role_by_id(role_id=role_id, db=db)
        if is_exist:
            return generate_response(
                data=role,
                status_code=status.HTTP_200_OK,
                message="Role details retrieved successfully",
            )
        else:
            return generate_response(
                custom_response=role.dict(),
            )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.put("/roles/{role_id}")
def update_role(role_id: int, role: Role, db: Session = Depends(get_db)):
    try:
        # Update the role using the role service
        is_updated, updated_role = RoleService().update_role(
            role_id=role_id, role_data=role, db=db
        )
        if is_updated:
            return generate_response(
                data=updated_role,
                status_code=status.HTTP_200_OK,
                message="Role updated successfully",
            )
        else:
            return generate_response(
                custom_response=updated_role.dict(),
            )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.delete("/roles/{role_id}")
def delete_role(role_id: int, db: Session = Depends(get_db)):
    try:
        # Delete the role using the role service
        is_deleted, content = RoleService().delete_role(role_id=role_id, db=db)
        if is_deleted:
            return generate_response(
                status_code=status.HTTP_204_NO_CONTENT,
                message="Role deleted successfully",
            )
        else:
            return generate_response(
                status_code=status.HTTP_400_BAD_REQUEST,
                message=content.dict(),
            )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)
