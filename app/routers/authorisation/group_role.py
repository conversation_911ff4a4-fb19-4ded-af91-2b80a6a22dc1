from fastapi import API<PERSON>outer, Depends, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

# Function to get a database session
from app.database.database import Session<PERSON><PERSON><PERSON>, get_db
from app.services.authorisation.group_role import GroupRoleService
from app.validations.authorisation.group_role import (
    AssignRoles,
    GroupRoles,
    GroupRolesRead,
)
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler

# Create an API router
router = APIRouter()


@router.get("/group-roles/{group_id}", response_model=GroupRolesRead)
def read_group_roles(group_id: int, db: Session = Depends(get_db)):
    try:
        group_roles = GroupRoleService().get_group_with_roles(group_id=group_id, db=db)
        if group_roles is None:
            return generate_response(
                data=group_roles,
                status_code=status.HTTP_204_NO_CONTENT,
                message="No content",
            )
        return generate_response(
            data=group_roles,
            status_code=status.HTTP_200_OK,
            message="Group roles retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.post("/group-roles/{group_id}")
def assign_roles_to_group(
    group_id: int, roles: AssignRoles, db: Session = Depends(get_db)
):
    try:
        is_assigned, content = GroupRoleService().assign_roles_to_group(
            group_id, roles.role_ids, db
        )
        if is_assigned is False:
            return generate_response(custom_response=content.dict())
        return generate_response(
            status_code=status.HTTP_201_CREATED, message="Roles assigned successfully"
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.delete("/group-roles/{group_id}")
def remove_group_roles(
    group_id: int, roles: AssignRoles, db: Session = Depends(get_db)
):
    try:
        is_removed, content = GroupRoleService().remove_roles_from_group(
            group_id=group_id, role_ids=roles.role_ids, db=db
        )
        if is_removed is False:
            return generate_response(custom_response=content.dict())
        return generate_response(
            status_code=status.HTTP_200_OK, message="Group roles removed successfully"
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)
