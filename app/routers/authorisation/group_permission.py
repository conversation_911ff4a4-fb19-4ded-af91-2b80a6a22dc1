from fastapi import APIRouter, Depends, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

# Function to get a database session
from app.database.database import <PERSON><PERSON><PERSON><PERSON>, get_db
from app.services.authorisation.group_permission import GroupPermissionsService
from app.validations.authorisation.group_permission import (
    AssignPermissions,
    GroupPermissionsRead,
)
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler

# Create an API router
router = APIRouter()


@router.get("/group-permissions/{group_id}", response_model=GroupPermissionsRead)
def read_group(group_id: int, db: Session = Depends(get_db)):
    try:
        group = GroupPermissionsService().get_group_with_permissions(
            group_id=group_id, db=db
        )
        if group is None:
            return generate_response(
                data=group, status_code=status.HTTP_204_NO_CONTENT, message="No content"
            )
        return generate_response(
            data=group,
            status_code=status.HTTP_200_OK,
            message="Group permissions retrieved successfully",
        )
    except Exception as e:
        
        return ExceptionHandler().handle_exception(e)


@router.post("/group-permissions/{group_id}")
def assign_permissions_to_group(
    group_id: int, permissions: AssignPermissions, db: Session = Depends(get_db)
):
    try:
        is_assgined, content = GroupPermissionsService().assign_permissions_to_group(
            group_id, permissions.permission_ids, db
        )
        if is_assgined is False:
            return generate_response(custom_response=content.dict())
        return generate_response(
            status_code=status.HTTP_201_CREATED,
            message="Permissions assigned successfully",
        )

    except Exception as e:
        
        return ExceptionHandler().handle_exception(e)


@router.delete("/group-permissions/{group_id}")
def remove_permissions_from_group(
    group_id: int, permissions: AssignPermissions, db: Session = Depends(get_db)
):
    try:
        is_removed, content = GroupPermissionsService().remove_permissions_from_group(
            group_id, permissions.permission_ids, db
        )
        if is_removed is False:
            return generate_response(custom_response=content.dict())
        return generate_response(
            status_code=status.HTTP_200_OK, message="Permissions removed successfully"
        )
    except Exception as e:
        
        return ExceptionHandler().handle_exception(e)
