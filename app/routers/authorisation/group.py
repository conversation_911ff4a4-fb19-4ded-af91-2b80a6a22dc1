import json

from fastapi import APIRouter, Depends, status
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

# Function to get a database session
from app.database.database import SessionLocal, get_db
from app.services.authorisation.group import GroupService
from app.validations.authorisation.group import Group, GroupCreateSchema
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler
from utils.mapper import AttributeMapper

# Create an API router
router = APIRouter()


@router.post("/groups")
def create_group(
    group: GroupCreateSchema,
    db: Session = Depends(get_db),
    response_model=GroupCreateSchema,
):
    try:
        # Create the group using the group service
        created_group = GroupService().create_group(group_data=group, db=db)
        return generate_response(
            data=created_group,
            status_code=status.HTTP_201_CREATED,
            message="Group created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.get("/groups")
def list_groups(db: Session = Depends(get_db)):
    try:
        # Get the list of groups using the group service
        groups = GroupService().get_all_groups(db=db)
        return generate_response(
            data=groups,
            status_code=status.HTTP_200_OK,
            message="Groups listed successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.get("/groups/{group_id}")
def get_group(group_id: int, db: Session = Depends(get_db)):
    try:
        # Get the group by id using the group service
        is_exist, group = GroupService().get_group_by_id(group_id=group_id, db=db)
        if is_exist:
            return generate_response(
                data=group,
                status_code=status.HTTP_200_OK,
                message="Group details retrieved successfully",
            )
        else:
            return generate_response(
                custom_response=group.dict(),
            )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.put("/groups/{group_id}")
def update_group(group_id: int, group: Group, db: Session = Depends(get_db)):
    try:
        # Update the group using the group service
        is_updated, updated_group = GroupService().update_group(
            group_id=group_id, group_data=group, db=db
        )
        if is_updated:
            return generate_response(
                data=updated_group,
                status_code=status.HTTP_200_OK,
                message="Group updated successfully",
            )
        else:
            return generate_response(
                custom_response=updated_group.dict(),
            )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.delete("/groups/{group_id}")
def delete_group(group_id: int, db: Session = Depends(get_db)):
    try:
        # Delete the group using the group service
        is_deleted, group = GroupService().delete_group(group_id=group_id, db=db)
        if is_deleted:
            return generate_response(
                status_code=status.HTTP_204_NO_CONTENT,
                message="Group deleted successfully",
            )
        else:
            return generate_response(custom_response=group.dict())

    except Exception as e:
        return ExceptionHandler().handle_exception(e)
