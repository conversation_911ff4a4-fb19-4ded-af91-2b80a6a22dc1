from fastapi import APIRouter, Depends, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from app.database.database import SessionLocal
from app.models.authorisation import HttpMethod
from app.services.authorisation.user_roles import UserRolesService
from app.validations.authorisation.group_role import AssignRoles
from app.validations.authorisation.user_roles import UserRolesRead
from utils.common import get_db
from utils.generate_response import generate_response
from utils.helpers import Exception<PERSON>and<PERSON>, ExtractUserAndAddToDict

# Create an API router
router = APIRouter()


@router.get("/user-roles/{user_id}", response_model=UserRolesRead)
def read_user_roles(user_id: int, db: Session = Depends(get_db)):
    try:
        user_roles = UserRolesService().get_user_with_roles(user_id=user_id, db=db)
        if user_roles is None:
            return generate_response(
                data=user_roles,
                status_code=status.HTTP_204_NO_CONTENT,
                message="No content",
            )
        return generate_response(
            data=user_roles,
            status_code=status.HTTP_200_OK,
            message="User Roles retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.post("/user-roles/{user_id}")
def assign_roles_to_user(
    user_id: int, roles: AssignRoles, db: Session = Depends(get_db)
):
    try:
        is_assigned, content = UserRolesService().assign_roles_to_user(
            user_id, roles.role_ids, db
        )
        if is_assigned is False:
            return generate_response(custom_response=content.dict())
        return generate_response(
            status_code=status.HTTP_201_CREATED, message="Roles assigned successfully"
        )

    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.delete("/user-roles/{user_id}")
def remove_group_roles(user_id: int, roles: AssignRoles, db: Session = Depends(get_db)):
    try:
        is_removed, content = UserRolesService().remove_roles_from_user(
            user_id=user_id, role_ids=roles.role_ids, db=db
        )
        if is_removed is False:
            return generate_response(custom_response=content.dict())
        return generate_response(
            status_code=status.HTTP_200_OK, message="User roles removed successfully"
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.get("/user-roles/{user_id}/check-permission")
def check_user_permission(user_id: int, endpoint_url: str):
    try:
        has_permission = UserRolesService().get_user_permissions(
            user_id=user_id, endpoint_url=endpoint_url, endpoint_method=1
        )
        return generate_response(
            data=has_permission,
            status_code=status.HTTP_200_OK,
            message="Permission checked successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)
