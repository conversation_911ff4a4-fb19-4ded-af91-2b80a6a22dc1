from fastapi import APIRouter, Depends, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

# Function to get a database session
from app.database.database import SessionLocal, get_db
from app.services.authorisation.permission import PermissionsService
from app.validations.authorisation.permission import Permission, PermissionCreateSchema
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler
from utils.mapper import AttributeMapper

# Create an API router
router = APIRouter()


@router.post("/permissions")
def create_permission(
    permission: PermissionCreateSchema,
    db: Session = Depends(get_db),
    response_model=PermissionCreateSchema,
):
    try:
        # Create the permission using the permission service
        created_permission = PermissionsService().create_permission(
            permission_data=permission, db=db
        )
        return generate_response(
            data=created_permission,
            status_code=status.HTTP_201_CREATED,
            message="Permission created successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.get("/permissions")
def list_permissions(db: Session = Depends(get_db)):
    try:
        # Get the list of permissions using the permission service
        permissions = PermissionsService().get_all_permissions(db=db)
        return generate_response(
            data=permissions,
            status_code=status.HTTP_200_OK,
            message="Permissions listed successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.get("/permissions/{permission_id}")
def get_permission(permission_id: int, db: Session = Depends(get_db)):
    try:
        # Get the permission by id using the permission service
        is_exist, permission = PermissionsService().get_permission_by_id(
            permission_id=permission_id, db=db
        )
        if is_exist:
            return generate_response(
                data=permission,
                status_code=status.HTTP_200_OK,
                message="Permission details retrieved successfully",
            )
        else:
            return generate_response(
                custom_response=permission.dict(),
            )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.put("/permissions/{permission_id}")
def update_permission(
    permission_id: int, permission: Permission, db: Session = Depends(get_db)
):
    try:
        # Update the permission using the permission service
        is_updated, updated_permission = PermissionsService().update_permission(
            permission_id=permission_id, permission_data=permission, db=db
        )
        if is_updated:
            return generate_response(
                data=updated_permission,
                status_code=status.HTTP_200_OK,
                message="Permission updated successfully",
            )
        else:
            return generate_response(
                custom_response=updated_permission.dict(),
            )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.delete("/permissions/{permission_id}")
def delete_permission(permission_id: int, db: Session = Depends(get_db)):
    try:
        # Delete the permission using the permission service
        is_deleted, permission = PermissionsService().delete_permission(
            permission_id=permission_id, db=db
        )
        if is_deleted:
            return generate_response(
                status_code=status.HTTP_204_NO_CONTENT,
                message="Permission deleted successfully",
            )
        else:
            return generate_response(custom_response=permission.dict())

    except Exception as e:
        return ExceptionHandler().handle_exception(e)
