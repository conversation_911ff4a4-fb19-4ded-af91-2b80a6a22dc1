from fastapi import APIRouter, Depends, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

# Function to get a database session
from app.database.database import <PERSON>L<PERSON>al, get_db
from app.services.authorisation.endpoint import EndpointService
from app.validations.authorisation.endpoint import Endpoint, EndpointCreateSchema
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler

# Create an API router
router = APIRouter()


@router.post("/endpoints")
def create_endpoints(
    group: EndpointCreateSchema,
    db: Session = Depends(get_db),
    response_model=EndpointCreateSchema,
):
    try:
        created_endpoint = EndpointService().create_endpoint(endpoint_data=group, db=db)
        return generate_response(
            data=created_endpoint,
            status_code=status.HTTP_201_CREATED,
            message="Endpoint created successfully",
        )
    except Exception as e:
        
        return ExceptionHandler().handle_exception(e)


@router.get("/endpoints")
def list_endpoints(db: Session = Depends(get_db)):
    try:
        # Get the list of endpoints using the endpoint service
        endpoints = EndpointService().get_all_endpoints(db=db)
        return generate_response(
            data=endpoints,
            status_code=status.HTTP_200_OK,
            message="Endpoints listed successfully",
        )
    except Exception as e:
        
        return ExceptionHandler().handle_exception(e)


@router.get("/endpoints/{endpoint_id}")
def get_endpoint(endpoint_id: int, db: Session = Depends(get_db)):
    try:
        # Get the endpoint by id using the endpoint service
        is_exist, endpoint = EndpointService().get_endpoint_by_id(
            endpoint_id=endpoint_id, db=db
        )
        if is_exist:
            return generate_response(
                data=endpoint,
                status_code=status.HTTP_200_OK,
                message="Endpoint details retrieved successfully",
            )
        else:
            return generate_response(
                custom_response=endpoint.dict(),
            )
    except Exception as e:
        
        return ExceptionHandler().handle_exception(e)


@router.put("/endpoints/{endpoint_id}")
def update_endpoint(
    endpoint_id: int, endpoint: Endpoint, db: Session = Depends(get_db)
):
    try:
        # Update the endpoint using the endpoint service
        is_updated, updated_endpoint = EndpointService().update_endpoint(
            endpoint_id=endpoint_id, endpoint_data=endpoint, db=db
        )
        if is_updated:
            return generate_response(
                data=updated_endpoint,
                status_code=status.HTTP_200_OK,
                message="Endpoint updated successfully",
            )
        else:
            return generate_response(
                custom_response=updated_endpoint.dict(),
            )
    except Exception as e:
        
        return ExceptionHandler().handle_exception(e)


@router.delete("/endpoints/{endpoint_id}")
def delete_endpoint(endpoint_id: int, db: Session = Depends(get_db)):
    try:
        # Delete the endpoint using the endpoint service
        is_deleted, endpoint = EndpointService().delete_endpoint(
            endpoint_id=endpoint_id, db=db
        )
        if is_deleted:
            return generate_response(
                status_code=status.HTTP_204_NO_CONTENT,
                message="Endpoint deleted successfully",
            )
        else:
            return generate_response(custom_response=endpoint.dict())

    except Exception as e:
        
        return ExceptionHandler().handle_exception(e)
