from fastapi import APIRouter, Depends, status
from fastapi.responses import JSONResponse
from fastapi.security import OAuth2Pass<PERSON><PERSON>earer
from sqlalchemy.orm import Session

from app.services.user import AuthService
from app.services.user_hospital import UserHospitalService, user_hospital_logger
from app.validations.user_hospital import (  # UserHospitalCreate,
    AssignHospitals,
    UserHospitalListResponse,
    UserHospitalRetrievedResponse,
)
from utils.common import get_db
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler

# Create an API router
router = APIRouter()


@router.get("/user-hospitals/{user_id}", response_model=UserHospitalListResponse)
def read_user_hospitals(user_id: int, db: Session = Depends(get_db)):
    try:
        user_hospitals = UserHospitalService().get_user_hospitals(user_id=user_id)
        if user_hospitals is None:
            return generate_response(
                data=user_hospitals,
                status_code=status.HTTP_204_NO_CONTENT,
                message="No content",
            )
        return generate_response(
            data=user_hospitals,
            status_code=status.HTTP_200_OK,
            message="User Hospitals retrieved successfully",
        )
    except Exception as e:
        user_hospital_logger.error("Error retrieving user hospitals for user ID %s: %s", user_id, str(e), exc_info=True)
        return ExceptionHandler().handle_exception(e)


@router.post("/user-hospitals/{user_id}")
def assign_hospitals_to_user(
    user_id: int, hospitals: AssignHospitals, db: Session = Depends(get_db)
):
    try:
        is_assigned, content = UserHospitalService().assign_hospitals_to_user(
            user_id, hospitals.hospital_ids, db
        )
        if is_assigned is False:
            return generate_response(custom_response=content.dict())
        return generate_response(
            status_code=status.HTTP_201_CREATED,
            message="Hospitals assigned successfully",
        )
    except Exception as e:
        user_hospital_logger.error("Error assigning hospitals to user ID %s: %s", user_id, str(e), exc_info=True)
        return ExceptionHandler().handle_exception(e)


@router.delete("/user-hospitals/{user_id}")
def remove_user_hospitals(
    user_id: int, hospitals: AssignHospitals, db: Session = Depends(get_db)
):
    try:
        is_removed, content = UserHospitalService().remove_hospitals_from_user(
            user_id=user_id, hospital_ids=hospitals.hospital_ids, db=db
        )
        if is_removed is False:
            return generate_response(custom_response=content.dict())
        return generate_response(
            status_code=status.HTTP_200_OK,
            message="User hospitals removed successfully",
        )
    except Exception as e:
        user_hospital_logger.error("Error removing hospitals from user ID %s: %s", user_id, str(e), exc_info=True)
        return ExceptionHandler().handle_exception(e)


oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


@router.get("/user/hospitals")
def get_user_hospitals(
    db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)
):
    try:
        user = AuthService().get_user_from_token(token)
        user_hospitals = [
            {"id": hospital.id, "name": hospital.name} for hospital in user.hospitals
        ]
        if user_hospitals is None:
            return generate_response(
                data=user_hospitals,
                status_code=status.HTTP_204_NO_CONTENT,
                message="No content",
            )
        return generate_response(
            data=user_hospitals,
            status_code=status.HTTP_200_OK,
            message="User Hospitals retrieved successfully",
        )
    except Exception as e:
        user_hospital_logger.error("Error retrieving hospitals for authenticated user: %s", str(e), exc_info=True)
        return ExceptionHandler().handle_exception(e)
