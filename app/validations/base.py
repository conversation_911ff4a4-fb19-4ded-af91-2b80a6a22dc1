import re
from contextlib import contextmanager
from typing import Any, Generator, Type

from fastapi import Depends
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.database.database import Base, SessionLocal
from utils.logger import core_logger


class CustomBaseModel(BaseModel):
    """Custom BaseModel with reusable DB constraint validations."""

    @staticmethod
    @contextmanager
    def get_db() -> Generator[Session, None, None]:
        """Context manager for the database session."""
        core_logger.debug("Creating a new database session for validation.")
        db = SessionLocal()
        try:
            yield db
        finally:
            core_logger.debug("Closing the database session after validation.")
            db.close()

    @classmethod
    def check_unique(cls, field: str, value: Any, model: Type[Base]):
        """Ensure a field value is unique in the given database table."""
        if value is None or (isinstance(value, str) and not value.strip()):
            core_logger.info(
                f"Skipping uniqueness check for {field} as value is empty or None"
            )
            return value

        with cls.get_db() as db:
            core_logger.info(f"Checking uniqueness of {field} and value: {value}")
            exists = db.query(model).filter(getattr(model, field) == value).first()
            if exists:
                core_logger.error(f"{field} already exists.")
                raise ValueError(f"{field} already exists.")
        return value

    @classmethod
    def check_foreign_key_exists(
        cls, verbose_name: str, field: str, value: Any, model: Type[Base]
    ):
        """
        Ensure referenced foreign key value(s) exist in the related table.
        Handles both single values and lists of values.
        """

        if value is None or not value:
            core_logger.info(
                f"Skipping uniqueness check for {field} as value is empty or None"
            )
            return value
        with cls.get_db() as db:
            # Handle single value
            if not isinstance(value, list):
                exists = db.query(model).filter(getattr(model, field) == value).first()
                if not exists:
                    error_message = f"{verbose_name} with ID {value} does not exist."
                    core_logger.error(error_message)
                    raise ValueError(error_message)
                return value

            # Query all existing IDs in a single database query
            query = db.query(getattr(model, field)).filter(
                getattr(model, field).in_(value)
            )
            existing_ids = {id_[0] for id_ in query.all()}

            # Use set difference to find missing IDs efficiently
            missing_values = list(set(value) - existing_ids)

            if missing_values:
                error_message = (
                    f"{verbose_name} with ID's {missing_values} do not exist."
                )
                core_logger.error(error_message)
                raise ValueError(error_message)

            return value

    @classmethod
    def check_object_exists(
        cls, verbose_name: str, field: str, value: Any, model: Type[Base]
    ):
        if value is None or not value:
            core_logger.info(
                f"Skipping object exists check for {field} as value is empty or None"
            )
            return value
        with cls.get_db() as db:
            core_logger.debug(f"Checking if {verbose_name} exists with {field}={value}")
            exists = db.query(model).filter(getattr(model, field) == value).first()
            core_logger.debug(f"Object exists: {exists is not None}")
        
            if not exists:
                error_message = f"{verbose_name} with {field}={value} does not exist."
                core_logger.error(error_message)
                raise ValueError(error_message)
            return value

    @classmethod
    def check_value_range(
        cls,
        field: str,
        value: Any,
        min_value: int = None,
        max_value: int = None,
        is_required: bool = False,
    ):
        """Ensure a field value is within a specified range."""
        if value is None:
            if is_required:
                raise ValueError(f"{field.capitalize()} is required.")
            return value

        # Check for all-whitespace strings and treat as empty if present
        if isinstance(value, str):
            if value.strip() == "" and (
                is_required or min_value is not None and min_value > 0
            ):
                raise ValueError(
                    f"{field.capitalize()} cannot be empty or contain only whitespace."
                )
            # Use len for string length check
            check_value = len(value)
        else:
            check_value = value

        if min_value is not None and check_value < min_value:
            raise ValueError(
                f"{field.capitalize()} must be greater than or equal to {min_value}."
            )
        if max_value is not None and check_value > max_value:
            raise ValueError(
                f"{field.capitalize()} must be less than or equal to {max_value}."
            )
        return value

    @classmethod
    def check_pattern(cls, value: str, pattern: str):
        """Ensure a field value matches a regex pattern."""
        if not re.match(pattern, value):
            raise ValueError(
                f"'{value}' does not match the expected pattern '{pattern}'."
            )
        return value

    @classmethod
    def check_alphanumeric(cls, value: str, field_name: str = "Value", allow_spaces: bool = False):
        """Ensure a field value contains only alphanumeric characters.
        
        Args:
            value: The string to validate
            field_name: Name of the field for error messages
            allow_spaces: Whether spaces are allowed in the string
            
        Returns:
            The validated string
            
        Raises:
            ValueError: If the string contains non-alphanumeric characters
        """
        if value is None:
            return value
            
        # Choose the appropriate pattern based on whether spaces are allowed
        if allow_spaces:
            pattern = r'^[a-zA-Z0-9 ]+$'  # Letters, numbers, and spaces
            error_message = f"{field_name} must contain only letters, numbers, and spaces."
        else:
            pattern = r'^[a-zA-Z0-9]+$'   # Only letters and numbers
            error_message = f"{field_name} must contain only letters and numbers."
        
        if not re.match(pattern, value):
            raise ValueError(error_message)
        
        return value

    @classmethod
    def check_allowed_values(cls, value: str, allowed_values: set):
        """Ensure a field value is within a set of allowed values."""
        if value not in allowed_values:
            raise ValueError(
                f"Invalid value '{value}'. Allowed values: {allowed_values}."
            )
        return value
