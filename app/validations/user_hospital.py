from typing import Optional, List
from pydantic import BaseModel, validator

from app.exception.errors.user import UserNotFoundValueError
from app.master.exception.errors.hospital import HospitalsNotFoundValueError


from app.master.services.hospital import HospitalService
from app.services.user import UserService
from utils.response import BaseResponse


class UserHospitalBase(BaseModel):
    """
    Represents the base schema for a user hospital.
    """

    user_id: int
    hospital_id: int


class AssignHospitals(BaseModel):
    """
    Represents the schema for creating a user hospital.
    """

    user_id: int
    hospital_ids: List[int]

    @validator("user_id")
    def check_user_exists(cls, v):
        """
        Check if the user exists.
        """
        user_service = UserService()
        is_exist = user_service.is_user_exists(user_id=v)
        if not is_exist:
            raise UserNotFoundValueError(user_id=v)
        return v

    @validator("hospital_ids")
    def check_hospital_exists(cls, v):
        """
        Check if the hospital exists.
        """
        hospital_service = HospitalService()
        is_exist, hospitals = hospital_service.is_hospital_exists_by_id(hospital_id=v)
        if not is_exist:
            raise HospitalsNotFoundValueError(hospital_ids=hospitals)
        return v


class UserHospitalBaseReadResponse(UserHospitalBase):
    """
    Represents the base schema for a user hospital response.
    """

    id: int


class UserHospitalRetrievedResponse(BaseResponse):
    """
    Represents a user hospital response schema.
    """

    data: Optional[UserHospitalBaseReadResponse] = None


class UserHospitalListResponse(BaseResponse):
    """
    Represents a user hospital response schema.
    """

    data: Optional[List[UserHospitalBaseReadResponse]] = None
