from typing import Optional, List

from pydantic import BaseModel

from app.validations.user import UserBase
from utils.response import BaseResponse
from app.validations.authorisation.role import Role, RoleBase


class UserRoles(UserBase):
    id: int
    roles: List[Role] = []

    class Config:
        from_attributes = True


class UserRolesRead(BaseResponse):
    """UserRoles read response."""

    data: Optional[UserRoles] = None

    class Config:
        from_attributes = True
