from typing import Optional

from pydantic import Base<PERSON><PERSON><PERSON>, constr, validator

from app.services.authorisation.group import GroupService
from app.validations.authorisation.permission import PermissionRead

class Group(BaseModel):
    """Represents a group."""

    id: Optional[int] = None
    name: constr(min_length=3, max_length=50)
    description: str

    class Config:
        """Configuration for the Group model."""

        from_attributes = True


class GroupCreateSchema(Group):
    """Represents a group creation schema."""

    @validator("name")
    def name_must_be_unique(cls, name):
        """Validates if the name is unique."""
        group = GroupService().is_group_exists(name=name)
        if group:
            raise GroupNameAlreadyExistsError()
        return name


class GroupNameAlreadyExistsError(ValueError):
    """Exception raised when a group name already exists."""

    def __init__(self):
        self.msg = "Group name already exists"
        super().__init__(self.msg)


class GroupBase(BaseModel):
    name: str


class GroupRead(BaseModel):
    id: int
    name: str
    description: str
    permissions: list[PermissionRead] = []