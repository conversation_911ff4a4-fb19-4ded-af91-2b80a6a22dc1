from typing import Optional

from pydantic import BaseModel, constr, validator

from app.services.authorisation.endpoint import EndpointService
from app.services.authorisation.permission import PermissionsService
from app.validations.authorisation.permission import PermissionNotFoundError


class Endpoint(BaseModel):
    id: Optional[int] = None
    name: constr(min_length=3, max_length=255)
    url: constr(min_length=3, max_length=255)
    method:str
    description: str
    permission_id: int


class EndpointCreateSchema(Endpoint):
    """Represents a endpoint creation schema."""

    @validator("name")
    def name_must_be_unique(cls, name):
        """Validates if the name is unique."""
        group = EndpointService().is_endpoint_exists(name=name)
        if group:
            raise EndpointAlreadyExistsError()
        return name

    @validator("permission_id")
    def check_permission_exists(cls, permission_id):
        """Validates if the permission exists."""
        permission = PermissionsService().is_permission_by_id_exists(permission_id)
        if not permission:
            raise PermissionNotFoundError()
        return permission_id

    @validator("url")
    def check_url_exists(cls, url):
        """Validates if the url exists."""
        url_exists = EndpointService().is_endpoint_url_exists(url)
        if not url_exists:
            raise EndpointUrlNotFoundError()
        return url


class EndpointAlreadyExistsError(ValueError):
    """Exception raised when an endpoint already exists."""

    def __init__(self):
        self.msg = "Endpoint already exists"
        super().__init__(self.msg)


class EndpointUrlNotFoundError(ValueError):
    """Exception raised when an endpoint URL is not found."""

    def __init__(self):
        self.msg = "Endpoint URL not found"
        super().__init__(self.msg)
