from typing import Optional, List

from pydantic import BaseModel, constr, validator

from app.services.authorisation.permission import PermissionsService


class Permission(BaseModel):
    """Represents a permission."""

    id: Optional[int] = None
    name: constr(min_length=3, max_length=50)
    description: str

    class Config:
        """Configuration for the Permission model."""

        from_attributes = True


class PermissionCreateSchema(Permission):
    """Represents a permission creation schema."""

    @validator("name")
    def name_must_be_unique(cls, name):
        """Validates if the name is unique."""
        permission = PermissionsService().is_permission_exists(name=name)
        if permission:
            raise PermsissionNameAlreadyExistsError()
        return name


class PermsissionNameAlreadyExistsError(ValueError):
    """Exception raised when a permission name already exists."""

    def __init__(self):
        self.msg = "permission name already exists"
        super().__init__(self.msg)


class PermissionNotFoundError(ValueError):
    """Exception raised when a permission is not found."""

    def __init__(self):
        self.msg = "Permission not found"
        super().__init__(self.msg)



class PermissionRead(Permission):
    """Represents a permission response."""
    pass