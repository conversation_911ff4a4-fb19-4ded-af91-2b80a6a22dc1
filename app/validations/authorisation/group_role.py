from typing import Optional, List

from pydantic import BaseModel

from app.validations.authorisation.group import GroupBase
from app.validations.authorisation.role import Role
from utils.response import BaseResponse


class GroupRoles(GroupBase):
    """Represents a group with associated roles."""

    id: int
    roles: List[Role] = []

    class Config:
        from_attributes = True


class AssignRoles(BaseModel):
    """Represents a request to assign roles to a user."""

    role_ids: List[int]


class GroupRolesRead(BaseResponse):
    """Group roles read response."""

    data: Optional[GroupRoles] = None

    class Config:
        from_attributes = True
