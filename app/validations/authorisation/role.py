from typing import Optional
from pydantic import Base<PERSON><PERSON><PERSON>, constr, validator
from app.services.authorisation.role import RoleService

from app.validations.authorisation.group import GroupRead


class Role(BaseModel):
    """Represents a role."""

    id: Optional[int] = None
    name: constr(min_length=3, max_length=50)
    description: str

    class Config:
        """Configuration for the Role model."""

        from_attributes = True


class RoleCreateSchema(Role):
    """Represents a role creation schema."""

    @validator("name")
    def name_must_be_unique(cls, name):
        """Validates if the name is unique."""
        role = RoleService().is_role_exists(name=name)
        if role:
            raise RoleNameAlreadyExistsError()
        return name


class RoleNameAlreadyExistsError(ValueError):
    """Exception raised when a role name already exists."""

    def __init__(self):
        self.msg = "Role name already exists"
        super().__init__(self.msg)


class RoleBase(BaseModel):
    name: str


class RoleRead(BaseModel):
    id: int
    name: str
    description: str
    groups: list[GroupRead] = []
