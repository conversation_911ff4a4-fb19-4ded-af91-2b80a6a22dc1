from typing import Optional, List

from pydantic import BaseModel

from app.validations.authorisation.group import GroupBase
from app.validations.authorisation.permission import Permission
from utils.response import BaseResponse


class GroupPermissions(GroupBase):
    id: int
    permissions: List[Permission] = []

    class Config:
        from_attributes = True


class AssignPermissions(BaseModel):
    permission_ids: List[int]


class GroupPermissionsRead(BaseResponse):
    """Group permissions read response."""

    data: Optional[GroupPermissions] = None

    class Config:
        from_attributes = True
