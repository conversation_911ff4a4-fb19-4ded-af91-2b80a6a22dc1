import re
from typing import Optional

from pydantic import BaseModel, EmailStr, constr, field_validator, validator

from app.master.models.hospital import HospitalDB
from app.master.validations.hospital import HospitalBaseReadResponse
from app.models.authorisation import RoleDB
from app.models.user import UserDB
from app.validations.authorisation.role import RoleRead
from utils.response import BaseResponse

from .base import CustomBaseModel


class User(BaseModel):
    """Represents a user."""

    first_name: str
    last_name: str
    email: EmailStr
    mobile_number: constr(min_length=10, max_length=15)  # type: ignore

    class Config:
        """Configuration for the User model."""

        from_attributes = True


class UserBase(CustomBaseModel):
    """Base class for User models."""

    first_name: str
    last_name: str
    mobile_number: str
    roles: list[int] = []
    hospitals: list[int] = []

    @field_validator("first_name")
    def check_first_name(cls, v):
        """Check if the first name is valid."""
        cls.check_value_range(
            field="first_name", value=v, min_value=2, max_value=50, is_required=True
        )
        return v

    @field_validator("last_name")
    def check_last_name(cls, v):
        """Check if the last name is valid."""
        cls.check_value_range(
            field="last_name", value=v, min_value=2, max_value=50, is_required=True
        )
        return v

    @validator("hospitals")
    def check_hospital_exists(cls, v):
        """
        Check if the hospital exists.
        """
        cls.check_foreign_key_exists(
            verbose_name="Hospital", field="id", value=v, model=HospitalDB
        )
        return v

    @validator("roles")
    def check_role_exists(cls, v):
        """
        Check if the role exists.
        """
        cls.check_foreign_key_exists(
            verbose_name="Role", field="id", value=v, model=RoleDB
        )
        return v


class UserCreateSchema(UserBase):
    """Represents a user creation schema."""

    email: str
    password: str = None

    @field_validator("email")
    def email_must_be_unique(cls, email):
        """Validates if the email is unique."""
        cls.check_unique("email", email, UserDB)
        return email

    @field_validator("password")
    def password_must_be_valid(cls, password):
        """Validates if the password is valid."""
        cls.check_value_range(
            field="password",
            value=password,
            min_value=8,
            max_value=50,
            is_required=False,
        )
        return password

    @field_validator("mobile_number")
    def mobile_number_must_be_unique(cls, mobile_number):
        """Validates if the mobile number is unique and valid."""
        cls.check_value_range(
            field="mobile_number",
            value=mobile_number,
            min_value=10,
            max_value=15,
            is_required=True,
        )
        cls.check_unique("mobile_number", mobile_number, UserDB)

        return mobile_number

    @validator("email")
    def validate_email(cls, v):
        # Regular expression for validating an Email
        """
        Validates if the email matches the regex pattern.

        Args:
            v (str): The email to be validated.

        Raises:
            ValueError: If the email does not match the regex pattern.

        Returns:
            str: The validated email.
        """
        regex = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"

        # If the provided email does not match the regex pattern, raise a ValueError
        if not re.match(regex, v):
            raise ValueError(
                "Invalid email format. Please provide a valid email address."
            )
        return v


class UserUpdate(UserBase):
    """Represents a user."""

    is_active: bool

    pass


class UserIdValidator(CustomBaseModel):
    """Represents a user."""

    id: int

    @field_validator("id")
    def check_id(cls, v):
        """Check if the id is valid."""
        cls.check_object_exists(verbose_name="User", field="id", value=v, model=UserDB)
        return v


class UserReadSchema(User):
    """Represents a user read schema."""

    id: int


class AuthenticationSchema(BaseModel):
    """Represents an authentication schema."""

    email: str
    password: constr(min_length=8, max_length=20)  # type: ignore

    @validator("email")
    def validate_email(cls, v):
        # Regular expression for validating an Email
        regex = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"

        # If the provided email does not match the regex pattern, raise a ValueError
        if not re.match(regex, v):
            raise ValueError("Invalid email format")
        return v


class MobileNumberAuth(BaseModel):
    """Represents a phone number."""

    mobile_number: constr(min_length=10, max_length=15)  # type: ignore


class OTP(MobileNumberAuth):
    """Represents an OTP."""

    otp: constr(min_length=6, max_length=6)  # type: ignore


class PasswordReset(BaseModel):
    """Represents a password reset schema."""

    new_password: str
    confirm_password: str


class UserResponse(User):
    """
    Represents a user response schema.
    """

    id: int
    roles: list[RoleRead]
    hospitals: list[HospitalBaseReadResponse]
    is_active: bool


class UserRetrievedResponse(BaseResponse):
    """
    Represents a user response schema.
    """

    data: Optional[UserResponse] = None


class UserListResponse(BaseResponse):
    """
    Represents a user list response schema.
    """

    data: Optional[list[UserResponse]] = None


class UserFilterPaginationResponse(BaseResponse):
    """
    Represents a user list response schema.
    """

    data: Optional[list[UserResponse]] = None
    total_count: int


class EmailAlreadyExistsError(ValueError):
    """Exception raised when a email already exists."""

    def __init__(self):
        self.msg = "Email already exists"
        super().__init__(self.msg)
