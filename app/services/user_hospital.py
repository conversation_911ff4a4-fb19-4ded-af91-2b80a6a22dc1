from typing import List
from sqlalchemy.orm import Session, joinedload

from app.database.database import engine, Base, SessionLocal
from app.exception.errors.user_hospital import UserHospitalErrors
from app.models.user import UserDB, UserHospitalDB
from app.master.models.hospital import HospitalDB
from app.models.user import UserHospitalDB


from utils.db import BaseService
from utils.logger import setup_logger

# Base.metadata.create_all(bind=engine)

user_hospital_logger = setup_logger(log_file="user_hospital.log")


class UserHospitalService(BaseService):
    def __init__(self) -> None:
        self.db = SessionLocal()
        self.logger = user_hospital_logger

    def get_user_hospital_by_attribute(
        self, value: str, db: Session, attribute: str = None
    ):
        """Retrieve a user hospital by a specific attribute."""
        try:
            if not attribute:
                attribute = UserHospitalDB.id
                self.logger.debug("No attribute specified, using default attribute: id")
            
            # Query the user hospital from the database
            user_hospital = self.get_by_attribute(
                db, UserHospitalDB, attribute, value)
                
            if not user_hospital:
                self.logger.warning("User hospital not found with %s=%s", 
                                   attribute.key if hasattr(attribute, 'key') else attribute, value)
                UserHospitalErrors().raise_user_hospital_not_found_exception(
                    user_hospital_id=value
                )
                return False, None
                
            self.logger.debug("Retrieved user hospital with %s=%s", 
                            attribute.key if hasattr(attribute, 'key') else attribute, value)
            return True, user_hospital
        except Exception as e:
            self.logger.error("Error retrieving user hospital by attribute: %s", str(e), exc_info=True)
            return False, str(e)

    def get_user_hospitals_by_user_id(self, user_id: int, db: Session):
        """Retrieve all hospitals associated with a user."""
        try:
            # Query the database for user hospitals
            user_hospitals = (
                db.query(UserHospitalDB).filter(
                    UserHospitalDB.user_id == user_id).all()
            )
            
            hospital_count = len(user_hospitals) if user_hospitals else 0
            self.logger.debug("Retrieved %d hospitals for user ID %s", hospital_count, user_id)
            
            return user_hospitals
        except Exception as e:
            self.logger.error("Error retrieving user hospitals by user ID %s: %s", user_id, str(e), exc_info=True)
            return []

    def assign_hospitals_to_user(self, user_id: int, hospital_ids: list):
        """
        Assign hospital to a user.

        Args:
            user_id (int): The ID of the user.
            hospital_ids (List[int]): The IDs of the hospital to assign.

        Returns:
            Tuple[bool, Union[List[UserRoleDB], Tuple[bool, str]]]: A tuple containing a boolean indicating success or failure,
            and either the list of assigned user hospitals objects or an error message.
        """
        try:
            # Fetch all hospitals at once
            hospitals = (
                self.db.query(HospitalDB).filter(
                    HospitalDB.id.in_(hospital_ids)).all()
            )
            existing_hospital_ids = {hospital.id for hospital in hospitals}
            
            if not existing_hospital_ids:
                self.logger.warning("No valid hospital IDs found in the provided list: %s", hospital_ids)
                return False, "No valid hospital IDs found"

            # Fetch all existing user-hospital relationships for the given user_id at once
            user_hospitals = (
                self.db.query(UserHospitalDB)
                .filter(
                    UserHospitalDB.user_id == user_id,
                    UserHospitalDB.hospital_id.in_(hospital_ids),
                )
                .all()
            )
            existing_user_hospital_ids = {
                user_hospital.hospital_id for user_hospital in user_hospitals
            }

            # Determine which hospital_ids are not found and which are new
            new_hospital_ids = existing_hospital_ids - existing_user_hospital_ids
            
            if not new_hospital_ids:
                self.logger.info("All hospitals already assigned to user ID %s", user_id)
                return True, user_hospitals

            # Use a bulk insert operation to add all new user-hospital relationships at once
            new_user_hospital = [
                UserHospitalDB(user_id=user_id, hospital_id=hospital_id)
                for hospital_id in new_hospital_ids
            ]
            self.db.bulk_save_objects(new_user_hospital)
            self.db.commit()
            
            # Get all user hospitals after the update
            updated_user_hospitals = (
                self.db.query(UserHospitalDB)
                .filter(UserHospitalDB.user_id == user_id)
                .all()
            )
            
            self.logger.info("Successfully assigned %d new hospitals to user ID %s", 
                           len(new_hospital_ids), user_id)
            self.db.close()
            return True, updated_user_hospitals
        except Exception as e:
            self.logger.error("Error assigning hospitals to user: %s", str(e), exc_info=True)
            self.db.rollback()
            self.db.close()
            return False, str(e)

    def get_user_hospitals(self, user_id):
        """Retrieve all hospitals associated with a user."""
        try:
            # Query the database for user hospitals
            user_hospitals = (
                self.db.query(UserHospitalDB)
                .filter(UserHospitalDB.user_id == user_id)
                .all()
            )

            hospital_count = len(user_hospitals) if user_hospitals else 0
            self.logger.debug("Retrieved %d hospitals for user ID %s", hospital_count, user_id)
            
            # Return the user hospitals
            return user_hospitals
        except Exception as e:
            self.logger.error("Error retrieving hospitals for user ID %s: %s", user_id, str(e), exc_info=True)
            return []

    def remove_hospitals_from_user(self, user_id: int, hospital_ids: list):
        """
        Remove assigned hospitals from a user.

        Args:
            user_id (int): The ID of the user.
            hospital_ids (List[int]): The IDs of the hospitals to remove.

        Returns:
            Tuple[bool, str]: A tuple containing a boolean indicating success or failure,
            and a message indicating the result of the operation.
        """
        try:
            if not hospital_ids:
                self.logger.warning("No hospital IDs provided for removal from user ID %s", user_id)
                return False, "No hospital IDs provided for removal"
                
            # Fetch the user-hospital relationships to be removed
            user_hospitals = (
                self.db.query(UserHospitalDB)
                .filter(
                    UserHospitalDB.user_id == user_id,
                    UserHospitalDB.hospital_id.in_(hospital_ids),
                )
                .all()
            )
            
            if not user_hospitals:
                self.logger.warning("No matching hospital assignments found for user ID %s with hospital IDs %s", 
                                  user_id, hospital_ids)
                return False, "No matching hospital assignments found"
                
            # Get the count of relationships to be removed
            removal_count = len(user_hospitals)
            
            # Remove the user-hospital relationships
            deleted_count = self.db.query(UserHospitalDB).filter(
                UserHospitalDB.user_id == user_id,
                UserHospitalDB.hospital_id.in_(hospital_ids),
            ).delete(synchronize_session=False)
            
            self.db.commit()
            self.logger.info("Successfully removed %d hospital assignments from user ID %s", 
                           deleted_count, user_id)
            self.db.close()
            return True, f"Successfully removed {deleted_count} hospital assignments"
        except Exception as e:
            self.logger.error("Error removing hospitals from user ID %s: %s", user_id, str(e), exc_info=True)
            self.db.rollback()
            self.db.close()
            return False, str(e)
