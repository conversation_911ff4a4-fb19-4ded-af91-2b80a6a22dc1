from typing import List

from sqlalchemy.orm import Session,joinedload


from app.database.database import engine, Base, SessionLocal
from app.exception.custom.authorisation import (
    GroupPermissionsExceptions,
)


from app.models.authorisation import GroupDB, GroupPermissionDB
from app.services.authorisation.group import GroupService
from app.services.authorisation.permission import PermissionsService

from utils.db import BaseService
from utils.logger import setup_logger




class GroupPermissionsService(BaseService):
    """
    Service class for managing group-permissions-related operations.
    """

    def __init__(self) -> None:
        super().__init__()
        self.db = SessionLocal()
        self.logger = setup_logger(log_file="group_permissions.log")

    def get_group_permission_by_ids(self, group_id: int, perm_id: int, db: Session):
        """
        Retrieve a group-permissions by a specific attribute.

        Args:
            group_id (int): The ID of the group.
            perm_id (int): The ID of the permission.
            db (Session): The database session.

        Returns:
            Tuple[bool, Union[GroupPermissionDB, str]]: A tuple containing a boolean indicating success or failure,
            and either the group permission object or an error message.
        """
        try:
            self.logger.info("Retrieving group permission with group_id=%d and perm_id=%d", group_id, perm_id)
            
            kwargs = {
                GroupPermissionDB.group_id: group_id,
                GroupPermissionDB.perm_id: perm_id,
            }
            # Query the group-permissions from the database
            group_permission = self.get_by_attributes(
                db,
                GroupPermissionDB,
                **kwargs,
            )
            if not group_permission:
                error = GroupPermissionsExceptions.generate_group_perms_not_found_error(
                    group_id=group_id, perm_id=perm_id
                )
                self.logger.warning("Group permission not found: group_id=%d, perm_id=%d", group_id, perm_id)
                return False, error

            self.logger.info("Successfully retrieved group permission: group_id=%d, perm_id=%d", group_id, perm_id)
            return True, group_permission
        except Exception as e:
            self.logger.error("Error retrieving group permission: group_id=%d, perm_id=%d, error=%s", 
                            group_id, perm_id, str(e), exc_info=True)
            raise

    def get_group_with_permissions(self, group_id: int, db: Session):
        """
        Retrieve a group with its associated permissions.

        Args:
            group_id (int): The ID of the group.
            db (Session): The database session.

        Returns:
            GroupDB: The group object with its associated permissions.
        """
        try:
            self.logger.info("Retrieving group with permissions for group_id=%d", group_id)
            
            group = db.query(GroupDB).options(joinedload(GroupDB.permissions)).filter(GroupDB.id == group_id).first()
            
            if not group:
                self.logger.warning("Group not found: group_id=%d", group_id)
                return None
                
            permission_count = len(group.permissions) if hasattr(group, 'permissions') else 0
            self.logger.info("Successfully retrieved group with %d permissions: group_id=%d", 
                           permission_count, group_id)
            
            return group
        except Exception as e:
            self.logger.error("Error retrieving group with permissions: group_id=%d, error=%s", 
                            group_id, str(e), exc_info=True)
            raise

    def assign_permissions_to_group(self, group_id: int, permission_ids: List[int], db: Session):
        """
        Assign permissions to a group.

        Args:
            group_id (int): The ID of the group.
            permission_ids (List[int]): The IDs of the permissions to assign.
            db (Session): The database session.

        Returns:
            Tuple[bool, Union[List[GroupPermissionDB], Tuple[bool, str]]]: A tuple containing a boolean indicating success or failure,
            and either the list of assigned group permission objects or an error message.
        """
        self.logger.info("Assigning %d permissions to group_id=%d", len(permission_ids), group_id)
        return self._update_group_permissions(group_id, permission_ids, db, add=True)

    def remove_permissions_from_group(self, group_id: int, permission_ids: List[int], db: Session):
        """
        Remove permissions from a group.

        Args:
            group_id (int): The ID of the group.
            permission_ids (List[int]): The IDs of the permissions to remove.
            db (Session): The database session.

        Returns:
            Tuple[bool, Union[List[GroupPermissionDB], Tuple[bool, str]]]: A tuple containing a boolean indicating success or failure,
            and either the list of remaining group permission objects or an error message.
        """
        self.logger.info("Removing %d permissions from group_id=%d", len(permission_ids), group_id)
        return self._update_group_permissions(group_id, permission_ids, db, add=False)

    def _update_group_permissions(self, group_id: int, permission_ids: List[int], db: Session, add: bool):
        """
        Update group permissions by assigning or removing permissions.

        Args:
            group_id (int): The ID of the group.
            permission_ids (List[int]): The IDs of the permissions to assign or remove.
            db (Session): The database session.
            add (bool): A flag indicating whether to assign or remove the permissions.

        Returns:
            Tuple[bool, Union[List[GroupPermissionDB], List[GroupPermissionDB]]]: A tuple containing a boolean indicating success or failure,
            and either the list of assigned group permission objects or the list of removed group permission objects.
        """
        try:
            action = "assigning" if add else "removing"
            self.logger.info("Updating group permissions: %s %d permissions for group_id=%d", 
                           action, len(permission_ids), group_id)
            
            # Check if the group exists
            is_group_exist, group = GroupService().get_group_by_id(group_id=group_id, db=db)
            if not is_group_exist:
                self.logger.warning("Group not found during permission update: group_id=%d", group_id)
                return is_group_exist, group

            # Check if the permissions exist
            is_perms_exist, permissions = PermissionsService().is_one_or_more_permissions_exists(permission_ids, db)
            if not is_perms_exist:
                self.logger.warning("One or more permissions not found during group permission update")
                return is_perms_exist, permissions

            # Check if the permissions are already assigned to the group
            existing_group_permissions = db.query(GroupPermissionDB).filter(
                GroupPermissionDB.group_id == group_id, 
                GroupPermissionDB.permission_id.in_(permission_ids)
            ).all()
            
            existing_permission_ids = [gp.permission_id for gp in existing_group_permissions]
            self.logger.debug("Found %d existing permissions already assigned to group_id=%d", 
                            len(existing_permission_ids), group_id)

            # Assign or remove the permissions to/from the group
            if add:
                # Filter out permissions that are already assigned
                permission_ids = [pid for pid in permission_ids if pid not in existing_permission_ids]
                
                if not permission_ids:
                    self.logger.info("All permissions are already assigned to group_id=%d", group_id)
                    return True, existing_group_permissions
                
                self.logger.info("Adding %d new permissions to group_id=%d", len(permission_ids), group_id)
                group_permissions = [GroupPermissionDB(group_id=group_id, permission_id=pid) for pid in permission_ids]
                db.add_all(group_permissions)
            else:
                # Filter to only remove permissions that exist
                permission_ids_to_remove = [pid for pid in existing_permission_ids if pid in permission_ids]
                
                if not permission_ids_to_remove:
                    self.logger.info("No matching permissions found to remove from group_id=%d", group_id)
                    return True, []
                
                self.logger.info("Removing %d permissions from group_id=%d", 
                               len(permission_ids_to_remove), group_id)
                db.query(GroupPermissionDB).filter(
                    GroupPermissionDB.group_id == group_id, 
                    GroupPermissionDB.permission_id.in_(permission_ids_to_remove)
                ).delete(synchronize_session=False)

            db.commit()
            
            result = group_permissions if add else existing_group_permissions
            self.logger.info("Successfully %s permissions for group_id=%d", 
                           "assigned" if add else "removed", group_id)
            
            return True, result
            
        except Exception as e:
            db.rollback()
            self.logger.error("Error %s permissions for group_id=%d: %s", 
                            "assigning" if add else "removing", group_id, str(e), exc_info=True)
            raise
