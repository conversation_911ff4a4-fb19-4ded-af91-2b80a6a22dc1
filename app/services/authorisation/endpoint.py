from sqlalchemy.orm import Session

from app.database.database import engine, Base, SessionLocal
from app.models.authorisation import EndpointDB
import app.main as main
from app.exception.custom.authorisation import EndpointExceptions

from utils.db import BaseService
from utils.logger import setup_logger

endpoint_logger = setup_logger(log_file="endpoint.log")


class EndpointService(BaseService):
    """
    Service class for managing endpoint-related operations.
    """

    def __init__(self) -> None:
        self.db = SessionLocal()
        self.logger = endpoint_logger

    def get_endpoint_by_attribute(self, value: str, db: Session, attribute: str = None):
        """Retrieve an endpoint by a specific attribute."""
        try:
            if not attribute:
                attribute = EndpointDB.id
            # Query the endpoint from the database
            endpoint = self.get_by_attribute(db, EndpointDB, attribute, value)
            if not endpoint:
                error = EndpointExceptions.generate_endpoint_not_found_error(
                    endpoint_id=value
                )
                return False, error

            return True, endpoint
        except Exception as e:
            self.logger.error("Error retrieving endpoint by attribute: %s", str(e), exc_info=True)
            raise

    def get_endpoint_by_id(self, endpoint_id: int, db: Session):
        """Retrieve an endpoint by ID."""

        try:
            is_exist, endpoint = self.get_endpoint_by_attribute(
                value=endpoint_id, db=db
            )
            return is_exist, endpoint
        except Exception as e:
            self.logger.error("Error retrieving endpoint by ID %s: %s", endpoint_id, str(e), exc_info=True)
            raise

    def get_endpoint_by_name(self, name: str, db: Session):
        """Retrieve an endpoint by name."""

        try:
            is_exist, endpoint = self.get_endpoint_by_attribute(
                value=name, db=db, attribute=EndpointDB.name
            )
            return is_exist, endpoint
        except Exception as e:
            self.logger.error("Error retrieving endpoint by name '%s': %s", name, str(e), exc_info=True)
            raise

    def get_all_endpoints(self, db: Session):
        """Retrieve all endpoints."""

        try:
            # Query all endpoints from the database.
            endpoints = self.get_all(db, EndpointDB)
            return endpoints
        except Exception as e:
            self.logger.error("Error retrieving all endpoints: %s", str(e), exc_info=True)
            raise

    def create_endpoint(self, endpoint_data, db: Session):
        """Create a new endpoint."""
        try:
            # Create a new endpoint instance and add it to the database
            endpoint = self.create(db, EndpointDB, **endpoint_data.dict())
            self.logger.info("Created new endpoint: %s", endpoint_data.name)
            return endpoint
        except Exception as e:
            self.logger.error("Error creating endpoint '%s': %s", endpoint_data.name, str(e), exc_info=True)
            raise

    def update_endpoint(self, endpoint_id: int, endpoint_data: dict, db: Session):
        """Update an endpoint."""

        try:
            is_exist, endpoint = self.get_endpoint_by_id(endpoint_id, db=db)
            if not is_exist:
                error = EndpointExceptions.generate_endpoint_not_found_error(
                    endpoint_id=endpoint_id
                )
                self.logger.warning("Endpoint with ID %s not found for update", endpoint_id)
                return False, error
            else:
                is_endpoint_name = self.is_endpoint_name_same(
                    endpoint_id, endpoint_data.name, db
                )
                if not is_endpoint_name:
                    is_endpoint_name_exist = self.is_endpoint_by_name_alredy_exists(
                        endpoint_data.name, db
                    )
                    if is_endpoint_name_exist:
                        error = EndpointExceptions.generate_endpoint_name_already_exists_error(
                            endpoint_name=endpoint_data.name
                        )
                        self.logger.warning("Endpoint with name '%s' already exists", endpoint_data.name)
                        return False, error
                endpoint_data_dict = endpoint_data.dict()
                endpoint_data_dict["id"] = endpoint.id
                endpoint = self.update(
                    db, EndpointDB, endpoint_id, **endpoint_data_dict
                )
                self.logger.info("Updated endpoint with ID %s to name '%s'", endpoint_id, endpoint_data.name)
                return True, endpoint

        except Exception as e:
            self.logger.error("Error updating endpoint with ID %s: %s", endpoint_id, str(e), exc_info=True)
            raise

    def delete_endpoint(self, endpoint_id: int, db: Session):
        """Delete an endpoint."""

        try:
            is_exist, endpoint = self.get_endpoint_by_id(endpoint_id, db=db)
            if not is_exist:
                error = EndpointExceptions.generate_endpoint_not_found_error(
                    endpoint_id=endpoint_id
                )
                self.logger.warning("Endpoint with ID %s not found for deletion", endpoint_id)
                return False, error
            else:
                is_deleted = self.delete(db, EndpointDB, endpoint_id)
                self.logger.info("Deleted endpoint with ID %s", endpoint_id)
                return is_deleted, endpoint_id
        except Exception as e:
            self.logger.error("Error deleting endpoint with ID %s: %s", endpoint_id, str(e), exc_info=True)
            raise

    def is_endpoint_exists(self, name: str):
        """Check if an endpoint exists."""

        try:
            is_exist, endpoint = self.get_endpoint_by_name(name, self.db)
            return is_exist
        except Exception as e:
            self.logger.error("Error checking if endpoint '%s' exists: %s", name, str(e), exc_info=True)
            raise

    def is_endpoint_name_same(self, endpoint_id: int, name: str, db: Session):

        try:
            is_exist, endpoint = self.get_endpoint_by_id(endpoint_id, db=db)
            if not is_exist:
                error = EndpointExceptions.generate_endpoint_not_found_error(
                    endpoint_id=endpoint_id
                )
                self.logger.warning("Endpoint with ID %s not found when checking name", endpoint_id)
                return False, error
            else:
                if endpoint.name == name:
                    return True
                else:
                    return False
        except Exception as e:
            self.logger.error("Error checking if endpoint ID %s has name '%s': %s", endpoint_id, name, str(e), exc_info=True)
            raise

    def is_endpoint_by_name_alredy_exists(self, name: str, db: Session):
        """Check if an endpoint exists."""

        try:
            is_exist, endpoint = self.get_endpoint_by_name(name, db)
            return is_exist
        except Exception as e:
            self.logger.error("Error checking if endpoint with name '%s' already exists: %s", name, str(e), exc_info=True)
            raise

    def is_endpoint_url_exists(self, url: str):
        """Check if an endpoint URL exists."""

        try:
            is_exist = main.check_endpoint_existence(endpoint_path=url)
            return is_exist
        except Exception as e:
            self.logger.error("Error checking if endpoint URL '%s' exists: %s", url, str(e), exc_info=True)
            raise
