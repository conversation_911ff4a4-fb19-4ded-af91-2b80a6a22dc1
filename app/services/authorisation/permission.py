from sqlalchemy.orm import Session


from app.database.database import engine, Base, SessionLocal
from app.models.authorisation import PermissionDB, GroupPermissionDB
from app.exception.custom.authorisation import (
    PermissionsExceptions,
)


from utils.db import BaseService
from utils.logger import setup_logger


class PermissionsService(BaseService):
    """
    Service class for managing permissions-related operations.
    """

    def __init__(self) -> None:
        super().__init__()
        self.db = SessionLocal()
        self.logger = setup_logger(log_file="permissions.log")

    def get_permission_by_attribute(
        self, value: str, db: Session, attribute: str = None
    ):
        """Retrieve a permissions by a specific attribute."""
        try:
            attr_name = attribute.key if hasattr(attribute, 'key') else str(attribute)
            self.logger.info("Retrieving permission with %s=%s", attr_name, value)
            
            if not attribute:
                attribute = PermissionDB.id
                attr_name = "id"
                
            # Query the permissions from the database
            permission = self.get_by_attribute(
                db, PermissionDB, attribute, value)
            if not permission:
                error = PermissionsExceptions.generate_permission_not_found_error(
                    permission_id=value
                )
                self.logger.warning("Permission not found with %s=%s", attr_name, value)
                return False, error

            self.logger.info("Successfully retrieved permission with %s=%s", attr_name, value)
            return True, permission
        except Exception as e:
            self.logger.error("Error retrieving permission with %s=%s: %s", 
                            attr_name, value, str(e), exc_info=True)
            raise

    def get_permission_by_id(self, permission_id: int, db: Session):
        """Retrieve a permissions by ID."""
        try:
            self.logger.info("Retrieving permission by ID: %d", permission_id)
            is_exist, permission = self.get_permission_by_attribute(
                value=permission_id, db=db
            )
            return is_exist, permission
        except Exception as e:
            self.logger.error("Error retrieving permission by ID %d: %s", 
                            permission_id, str(e), exc_info=True)
            raise

    def get_permission_by_name(self, name: str, db: Session):
        """Retrieve a permissions by name."""
        try:
            self.logger.info("Retrieving permission by name: %s", name)
            is_exist, permission = self.get_permission_by_attribute(
                value=name, db=db, attribute=PermissionDB.name
            )
            return is_exist, permission
        except Exception as e:
            self.logger.error("Error retrieving permission by name '%s': %s", 
                            name, str(e), exc_info=True)
            raise

    def get_all_permissions(self, db: Session):
        """Retrieve all permissions."""
        try:
            self.logger.info("Retrieving all permissions")
            permissions = self.get_all(db, PermissionDB)
            self.logger.info("Successfully retrieved %d permissions", len(permissions))
            return permissions
        except Exception as e:
            self.logger.error("Error retrieving all permissions: %s", str(e), exc_info=True)
            raise

    def create_permission(self, permission_data, db: Session):
        """Create a new permissions."""
        try:
            self.logger.info("Creating new permission with name: %s", permission_data.name)
            permission = self.create(
                db, PermissionDB, **permission_data.dict())
            self.logger.info("Successfully created permission with ID: %d", permission.id)
            return permission
        except Exception as e:
            self.logger.error("Error creating permission '%s': %s", 
                            permission_data.name, str(e), exc_info=True)
            raise

    def update_permission(self, permission_id: int, permission_data: dict, db: Session):
        """Update a permissions."""
        try:
            self.logger.info("Updating permission with ID: %d", permission_id)
            is_exist, permission = self.get_permission_by_id(permission_id, db=db)
            if not is_exist:
                error = PermissionsExceptions.generate_permission_not_found_error(
                    permission_id=permission_id
                )
                self.logger.warning("Permission not found for update: ID=%d", permission_id)
                return False, error
            else:
                self.logger.debug("Checking if permission name needs to be updated")
                is_perms_name_same = self.is_permission_name_same(
                    permission_id, permission_data.name, db
                )
                if not is_perms_name_same:
                    self.logger.debug("Permission name is being changed to: %s", permission_data.name)
                    is_group_name_exist = self.is_permission_by_name_alredy_exists(
                        permission_data.name, db
                    )
                    if is_group_name_exist:
                        error = PermissionsExceptions.generate_perms_name_already_exists_error(
                            permission_name=permission_data.name
                        )
                        self.logger.warning("Permission name '%s' already exists", permission_data.name)
                        return False, error
                        
                perms_data_dict = permission_data.dict()
                perms_data_dict["id"] = permission.id
                updated_permission = self.update(
                    db, PermissionDB, permission_id, **perms_data_dict)
                    
                self.logger.info("Successfully updated permission with ID: %d", permission_id)
                return True, updated_permission

        except Exception as e:
            self.logger.error("Error updating permission with ID %d: %s", 
                            permission_id, str(e), exc_info=True)
            raise

    def delete_permission(self, permission_id: int, db: Session):
        """Delete a permissions."""
        try:
            self.logger.info("Deleting permission with ID: %d", permission_id)
            is_exist, permission = self.get_permission_by_id(
                permission_id, db=db)
            if not is_exist:
                error = PermissionsExceptions.generate_permission_not_found_error(
                    permission_id=permission_id
                )
                self.logger.warning("Permission not found for deletion: ID=%d", permission_id)
                return False, error
            else:
                if self.is_permission_has_groups(permission_id, db):
                    error = PermissionsExceptions.generate_permission_associated_with_group_error(
                        permission_id=permission_id
                    )
                    self.logger.warning("Cannot delete permission ID=%d: associated with groups", 
                                      permission_id)
                    return False, error

                is_deleted = self.delete(db, PermissionDB, permission_id)
                self.logger.info("Successfully deleted permission with ID: %d", permission_id)
                return is_deleted, permission_id
        except Exception as e:
            self.logger.error("Error deleting permission with ID %d: %s", 
                            permission_id, str(e), exc_info=True)
            raise

    def is_permission_name_same(self, permission_id: int, name: str, db: Session):
        """Check if the permissions name is the same."""
        try:
            self.logger.debug("Checking if permission ID=%d has name '%s'", permission_id, name)
            is_exist, permission = self.get_permission_by_id(
                permission_id=permission_id, db=db
            )
            if not is_exist:
                self.logger.warning("Permission not found when checking name: ID=%d", permission_id)
                return False
                
            result = permission.name == name
            self.logger.debug("Permission ID=%d name check result: %s", 
                            permission_id, "same" if result else "different")
            return result
        except Exception as e:
            self.logger.error("Error checking permission name for ID %d: %s", 
                            permission_id, str(e), exc_info=True)
            raise

    def is_permission_exists(self, name: str):
        """Check if a permissions exists."""
        try:
            self.logger.debug("Checking if permission exists with name: %s", name)
            is_exist, permission = self.get_permission_by_name(name, self.db)
            self.logger.debug("Permission '%s' exists: %s", name, is_exist)
            return is_exist
        except Exception as e:
            self.logger.error("Error checking if permission '%s' exists: %s", 
                            name, str(e), exc_info=True)
            raise

    def is_permission_by_name_alredy_exists(self, name: str, db: Session):
        """Check if a permissions exists."""
        try:
            self.logger.debug("Checking if permission already exists with name: %s", name)
            is_exist, permission = self.get_permission_by_name(name, db)
            self.logger.debug("Permission '%s' already exists: %s", name, is_exist)
            return is_exist
        except Exception as e:
            self.logger.error("Error checking if permission '%s' already exists: %s", 
                            name, str(e), exc_info=True)
            raise

    def is_permission_by_id_exists(self, permission_id: int) -> bool:
        """Check if a permissions exists."""
        try:
            self.logger.debug("Checking if permission exists with ID: %d", permission_id)
            is_exist, permission = self.get_permission_by_id(
                permission_id, self.db)
            self.logger.debug("Permission with ID %d exists: %s", permission_id, is_exist)
            return is_exist
        except Exception as e:
            self.logger.error("Error checking if permission with ID %d exists: %s", 
                            permission_id, str(e), exc_info=True)
            raise

    def is_one_or_more_permissions_exists(self, permission_ids: list, db: Session):
        """Check if one or more permissions exist."""
        try:
            self.logger.info("Checking if permissions exist with IDs: %s", permission_ids)
            permissions = (
                db.query(PermissionDB).filter(
                    PermissionDB.id.in_(permission_ids)).all()
            )
            found_ids = [p.id for p in permissions]
            self.logger.debug("Found %d of %d requested permissions", 
                            len(permissions), len(permission_ids))
            
            if len(permissions) != len(permission_ids):
                missing_ids = [pid for pid in permission_ids if pid not in found_ids]
                self.logger.warning("Some permissions not found: %s", missing_ids)
                error = (
                    PermissionsExceptions.generate_one_or_more_perms_not_found_error(
                        permission_ids=permission_ids
                    )
                )
                return False, error
                
            self.logger.info("All requested permissions exist")
            return True, permissions
        except Exception as e:
            self.logger.error("Error checking if permissions exist with IDs %s: %s", 
                            permission_ids, str(e), exc_info=True)
            raise

    def is_permission_has_groups(self, permission_id: int, db: Session):
        """Check if a group is associated with any permissions."""
        try:
            self.logger.debug("Checking if permission ID=%d is associated with any groups", 
                            permission_id)
            permission_groups = (
                db.query(GroupPermissionDB)
                .filter(GroupPermissionDB.permission_id == permission_id)
                .all()
            )
            result = bool(permission_groups)
            self.logger.debug("Permission ID=%d is associated with groups: %s (%d groups)", 
                            permission_id, result, len(permission_groups))
            return result
        except Exception as e:
            self.logger.error("Error checking if permission ID=%d has groups: %s", 
                            permission_id, str(e), exc_info=True)
            raise
