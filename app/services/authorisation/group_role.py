from typing import List

from sqlalchemy.orm import Session,joinedload


from app.database.database import <PERSON><PERSON><PERSON><PERSON>
from app.exception.custom.authorisation import GroupRolesExceptions


from app.models.authorisation import GroupDB,RoleDB,GroupRoleDB
from app.services.authorisation.group import GroupService
from app.services.authorisation.role import RoleService

from utils.db import BaseService
from utils.logger import setup_logger


class GroupRoleService(BaseService):
    """
    Service class for managing group-role-related operations.
    """

    def __init__(self) -> None:
        super().__init__()
        self.db = SessionLocal()
        self.logger = setup_logger(log_file="group_roles.log")

    def get_group_role_by_ids(self, group_id: int, role_id: int, db: Session):
        """
        Retrieve a group-role by a specific attribute.

        Args:
            group_id (int): The ID of the group.
            role_id (int): The ID of the role.
            db (Session): The database session.

        Returns:
            Tuple[bool, Union[GroupRoleDB, str]]: A tuple containing a boolean indicating success or failure,
            and either the group role object or an error message.
        """
        try:
            self.logger.info("Retrieving group role with group_id=%d and role_id=%d", group_id, role_id)
            
            kwargs = {
                GroupRoleDB.group_id: group_id,
                GroupRoleDB.role_id: role_id,
            }
            # Query the group-role from the database
            group_role = self.get_by_attributes(
                db,
                GroupRoleDB,
                **kwargs,
            )
            if not group_role:
                error = GroupRolesExceptions.generate_group_role_not_found_error(
                    group_id=group_id, role_id=role_id
                )
                self.logger.warning("Group role not found: group_id=%d, role_id=%d", group_id, role_id)
                return False, error

            self.logger.info("Successfully retrieved group role: group_id=%d, role_id=%d", group_id, role_id)
            return True, group_role
        except Exception as e:
            self.logger.error("Error retrieving group role: group_id=%d, role_id=%d, error=%s", 
                            group_id, role_id, str(e), exc_info=True)
            raise

    def get_group_with_roles(self, group_id: int, db: Session):
        """
        Retrieve a group with its associated roles.

        Args:
            group_id (int): The ID of the group.
            db (Session): The database session.

        Returns:
            GroupDB: The group object with its associated roles.
        """
        try:
            self.logger.info("Retrieving group with roles for group_id=%d", group_id)
            
            group = db.query(GroupDB).options(joinedload(GroupDB.roles)).filter(GroupDB.id == group_id).first()
            
            if not group:
                self.logger.warning("Group not found: group_id=%d", group_id)
                return None
                
            role_count = len(group.roles) if hasattr(group, 'roles') else 0
            self.logger.info("Successfully retrieved group with %d roles: group_id=%d", 
                           role_count, group_id)
            
            return group
        except Exception as e:
            self.logger.error("Error retrieving group with roles: group_id=%d, error=%s", 
                            group_id, str(e), exc_info=True)
            raise

    def assign_roles_to_group(self, group_id: int, role_ids: List[int], db: Session):
        """
        Assign roles to a group.

        Args:
            group_id (int): The ID of the group.
            role_ids (List[int]): The IDs of the roles to assign.
            db (Session): The database session.

        Returns:
            Tuple[bool, Union[List[GroupRoleDB], Tuple[bool, str]]]: A tuple containing a boolean indicating success or failure,
            and either the list of assigned group role objects or an error message.
        """
        self.logger.info("Assigning %d roles to group_id=%d", len(role_ids), group_id)
        return self._update_group_roles(group_id, role_ids, db, add=True)
    
    def remove_roles_from_group(self, group_id: int, role_ids: List[int], db: Session):
        """
        Remove roles from a group.

        Args:
            group_id (int): The ID of the group.
            role_ids (List[int]): The IDs of the roles to remove.
            db (Session): The database session.

        Returns:
            Tuple[bool, Union[List[GroupRoleDB], Tuple[bool, str]]]: A tuple containing a boolean indicating success or failure,
            and either the list of remaining group role objects or an error message.
        """
        self.logger.info("Removing %d roles from group_id=%d", len(role_ids), group_id)
        return self._update_group_roles(group_id, role_ids, db, add=False)
    
    def _update_group_roles(self, group_id: int, role_ids: List[int], db: Session, add: bool):
        """
        Update group roles by assigning or removing roles.

        Args:
            group_id (int): The ID of the group.
            role_ids (List[int]): The IDs of the roles to assign or remove.
            db (Session): The database session.
            add (bool): A flag indicating whether to assign or remove the roles.

        Returns:
            Tuple[bool, Union[List[GroupRoleDB], List[GroupRoleDB]]]: A tuple containing a boolean indicating success or failure,
            and either the list of assigned group role objects or the list of removed group role objects.
        """
        try:
            action = "assigning" if add else "removing"
            self.logger.info("Updating group roles: %s %d roles for group_id=%d", 
                           action, len(role_ids), group_id)
            
            # Check if the group exists
            is_group_exist, group = GroupService().get_group_by_id(group_id=group_id, db=db)
            if not is_group_exist:
                self.logger.warning("Group not found during role update: group_id=%d", group_id)
                return is_group_exist, group

            # Check if the roles exist
            is_roles_exist, roles = RoleService().is_one_or_more_roles_exists(role_ids, db)
            if not is_roles_exist:
                self.logger.warning("One or more roles not found during group role update")
                return is_roles_exist, roles

            # Check if the roles are already assigned to the group
            existing_group_roles = db.query(GroupRoleDB).filter(
                GroupRoleDB.group_id == group_id, 
                GroupRoleDB.role_id.in_(role_ids)
            ).all()
            
            existing_role_ids = [gr.role_id for gr in existing_group_roles]
            self.logger.debug("Found %d existing roles already assigned to group_id=%d", 
                            len(existing_role_ids), group_id)

            # Assign or remove the roles to/from the group
            if add:
                # Filter out roles that are already assigned
                role_ids = [rid for rid in role_ids if rid not in existing_role_ids]
                
                if not role_ids:
                    self.logger.info("All roles are already assigned to group_id=%d", group_id)
                    return True, existing_group_roles
                
                self.logger.info("Adding %d new roles to group_id=%d", len(role_ids), group_id)
                group_roles = [GroupRoleDB(group_id=group_id, role_id=rid) for rid in role_ids]
                db.add_all(group_roles)
            else:
                # Filter to only remove roles that exist
                role_ids_to_remove = [rid for rid in existing_role_ids if rid in role_ids]
                
                if not role_ids_to_remove:
                    self.logger.info("No matching roles found to remove from group_id=%d", group_id)
                    return True, []
                
                self.logger.info("Removing %d roles from group_id=%d", 
                               len(role_ids_to_remove), group_id)
                db.query(GroupRoleDB).filter(
                    GroupRoleDB.group_id == group_id, 
                    GroupRoleDB.role_id.in_(role_ids_to_remove)
                ).delete(synchronize_session=False)

            db.commit()
            
            result = group_roles if add else existing_group_roles
            self.logger.info("Successfully %s roles for group_id=%d", 
                           "assigned" if add else "removed", group_id)
            
            return True, result
            
        except Exception as e:
            db.rollback()
            self.logger.error("Error %s roles for group_id=%d: %s", 
                            "assigning" if add else "removing", group_id, str(e), exc_info=True)
            raise