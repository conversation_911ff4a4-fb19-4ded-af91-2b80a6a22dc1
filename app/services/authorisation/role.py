from sqlalchemy.orm import Session
from app.database.database import engine, Base, SessionLocal
from app.models.authorisation import RoleDB, GroupRoleDB, UserRoleDB
from app.exception.custom.authorisation import RoleExceptions
from utils.db import BaseService
from utils.logger import setup_logger


class RoleService(BaseService):
    """
    Service class for managing role-related operations.
    """

    def __init__(self) -> None:
        self.db = SessionLocal()
        self.logger = setup_logger(name=__name__, log_file="role_service.log")

    def get_role_by_attribute(self, value: str, db: Session, attribute: str = None):
        """Retrieve a role by a specific attribute."""
        try:
            if not attribute:
                attribute = RoleDB.id
            # Query the role from the database
            role = self.get_by_attribute(db, RoleDB, attribute, value)
            if not role:
                error = RoleExceptions.generate_role_not_found_error(role_id=value)
                self.logger.warning("Role not found with %s=%s", attribute.key if hasattr(attribute, 'key') else 'id', value)
                return False, error

            return True, role
        except Exception as e:
            self.logger.error("Error retrieving role by attribute: %s", str(e), exc_info=True)
            raise

    def get_role_by_id(self, role_id: int, db: Session):
        """Retrieve a role by ID."""
        try:
            is_exist, role = self.get_role_by_attribute(value=role_id, db=db)
            return is_exist, role
        except Exception as e:
            self.logger.error("Error retrieving role by id=%s: %s", role_id, str(e), exc_info=True)
            raise

    def get_role_by_name(self, name: str, db: Session):
        """Retrieve a role by name."""
        try:
            is_exist, role = self.get_role_by_attribute(
                value=name, db=db, attribute=RoleDB.name
            )
            return is_exist, role
        except Exception as e:
            self.logger.error("Error retrieving role by name=%s: %s", name, str(e), exc_info=True)
            raise

    def get_all_roles(self, db: Session):
        """Retrieve all roles."""
        try:
            # Query all roles from the database.
            roles = self.get_all(db, RoleDB)
            self.logger.debug("Retrieved %d roles", len(roles))
            return roles
        except Exception as e:
            self.logger.error("Error retrieving all roles: %s", str(e), exc_info=True)
            raise

    def create_role(self, role_data, db: Session):
        """Create a new role."""
        try:
            # Create a new role instance and add it to the database
            self.logger.info("Creating new role with name=%s", role_data.name)
            role = self.create(db, RoleDB, **role_data.dict())
            return role
        except Exception as e:
            self.logger.error("Error creating role: %s", str(e), exc_info=True)
            raise

    def update_role(self, role_id: int, role_data: dict, db: Session):
        """Update a role."""
        try:
            self.logger.info("Updating role with id=%s", role_id)
            is_exist, role = self.get_role_by_id(role_id, db=db)
            if not is_exist:
                error = RoleExceptions.generate_role_not_found_error(role_id=role_id)
                self.logger.warning("Role not found with id=%s", role_id)
                return False, error
            else:
                is_role_name_same = self.is_role_name_same(role_id, role_data.name, db)
                if not is_role_name_same:
                    is_role_name_exist = self.is_role_by_name_already_exists(
                        role_data.name, db
                    )
                    if is_role_name_exist:
                        error = RoleExceptions.generate_role_name_already_exists_error(
                            role_name=role_data.name
                        )
                        self.logger.warning("Role with name=%s already exists", role_data.name)
                        return False, error
                role_data_dict = role_data.dict()
                role_data_dict["id"] = role.id
                role = self.update(db, RoleDB, role_id, **role_data_dict)
                return True, role
        except Exception as e:
            self.logger.error("Error updating role with id=%s: %s", role_id, str(e), exc_info=True)
            raise

    def delete_role(self, role_id: int, db: Session):
        """Delete a role."""
        try:
            self.logger.info("Deleting role with id=%s", role_id)
            is_exist, role = self.get_role_by_id(role_id, db=db)
            if not is_exist:
                error = RoleExceptions.generate_role_not_found_error(role_id=role_id)
                self.logger.warning("Role not found with id=%s", role_id)
                return False, error
            else:
                error = self.check_role_associations(role_id, db)
                if error:
                    self.logger.warning("Role with id=%s has associations and cannot be deleted", role_id)
                    return False, error

                is_deleted = self.delete(db, RoleDB, role_id)
                self.logger.info("Role deleted successfully with id=%s", role_id)
                return is_deleted, role_id
        except Exception as e:
            self.logger.error("Error deleting role with id=%s: %s", role_id, str(e), exc_info=True)
            raise

    def check_role_associations(self, role_id: int, db: Session):
        """
        Check if a role is associated with any permissions or roles.

        Args:
            role_id (int): The ID of the role.
            db (Session): The database session.

        Returns:
            RoleExceptions or None: The error if the role is associated with permissions or roles, None otherwise.
        """
        try:
            error = None
            is_role_has_groups = self.is_role_has_groups(role_id, db)
            if is_role_has_groups:
                error = RoleExceptions.generate_role_associated_with_groups_error(
                    role_id=role_id
                )
            is_role_has_users = self.is_role_has_users(role_id, db)
            if is_role_has_users:
                if error:
                    error.detail.append(
                        RoleExceptions.generate_role_associated_with_users_error(
                            role_id=role_id
                        ).detail[0]
                    )
                else:
                    error = RoleExceptions.generate_role_associated_with_users_error(
                        role_id=role_id
                    )

            return error
        except Exception as e:
            self.logger.error("Error checking role associations for id=%s: %s", role_id, str(e), exc_info=True)
            raise

    def is_role_exists(self, name: str):
        """Check if a role exists."""
        try:
            is_exist, role = self.get_role_by_name(name, self.db)
            return is_exist
        except Exception as e:
            raise

    def is_role_name_same(self, role_id: int, name: str, db: Session):
        """Check if a role name is the same as the provided name."""
        try:
            is_exist, role = self.get_role_by_id(role_id, db=db)
            if not is_exist:
                error = RoleExceptions.generate_role_not_found_error(role_id=role_id)
                return False, error
            else:
                if role.name == name:
                    return True
                else:
                    return False
        except Exception as e:
            self.logger.error("Error checking if role name is same for id=%s: %s", role_id, str(e), exc_info=True)
            raise

    def is_role_by_name_already_exists(self, name: str, db: Session):
        """Check if a role exists."""
        try:
            is_exist, role = self.get_role_by_name(name, db)
            return is_exist
        except Exception as e:
            raise

    def is_one_or_more_roles_exists(self, role_ids: list, db: Session):
        """Check if one or more roles exist."""
        try:
            roles = db.query(RoleDB).filter(RoleDB.id.in_(role_ids)).all()
            if len(roles) != len(role_ids):
                error = RoleExceptions.generate_one_or_more_roles_not_found_error(
                    role_ids=role_ids
                )
                return False, error
            return True, roles
        except Exception as e:
            raise

    def are_roles_exist(self, role_ids):
        try:
            query = self.db.query(RoleDB.id).filter(RoleDB.id.in_(role_ids))
            existing_ids = {id_[0] for id_ in query.all()}
            not_exist_ids = list(set(role_ids) - existing_ids)
            if not_exist_ids:
                self.logger.warning("Roles not found with ids=%s", not_exist_ids)
                return False, not_exist_ids
            return True, None
        except Exception as e:
            self.logger.error("Error checking if roles exist with ids=%s: %s", role_ids, str(e), exc_info=True)
            raise

    def is_role_has_groups(self, role_id: int, db: Session):
        """
        Check if a role is associated with any groups.

        Args:
            role_id (int): The ID of the role.
            db (Session): The database session.

        Returns:
            bool: True if the role is associated with any groups, False otherwise.
        """
        role_groups = db.query(GroupRoleDB).filter(GroupRoleDB.role_id == role_id).all()
        return bool(role_groups)

    def is_role_has_groups(self, role_id: int, db: Session):
        """
        Check if a role is associated with any groups.

        Args:
            role_id (int): The ID of the role.
            db (Session): The database session.

        Returns:
            bool: True if the role is associated with any groups, False otherwise.
        """
        role_groups = db.query(GroupRoleDB).filter(GroupRoleDB.role_id == role_id).all()
        return bool(role_groups)

    def is_role_has_users(self, role_id: int, db: Session):
        """
        Check if a role is associated with any users.

        Args:
            role_id (int): The ID of the role.
            db (Session): The database session.

        Returns:
            bool: True if the role is associated with any users, False otherwise.
        """
        role_users = db.query(UserRoleDB).filter(UserRoleDB.role_id == role_id).all()
        return bool(role_users)
