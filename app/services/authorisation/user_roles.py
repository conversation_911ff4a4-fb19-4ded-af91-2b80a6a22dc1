from typing import List
from sqlalchemy.orm import Session, joinedload
from app.database.database import engine, Base, SessionLocal
from app.models.user import UserDB
from app.models.authorisation import (
    UserRoleDB,
    GroupRoleDB,
    GroupPermissionDB,
    EndpointDB,
    RoleDB,
)
from app.services.user import UserService
from app.services.authorisation.role import RoleService
from app.exception.custom.authorisation import UserRoleExceptions
from app.services.authorisation.helpers.logger import auth_logger


from utils.db import BaseService


class UserRolesService(BaseService):
    """
    Service class for managing user-roles-related operations.
    """

    def __init__(self) -> None:
        self.db = SessionLocal()

    def get_user_roles_by_ids(self, user_id: int, role_id: int, db: Session):
        """
        Retrieve a user-roles by a specific attribute.

        Args:
            user_id (int): The ID of the user.
            role_id (int): The ID of the permission.
            db (Session): The database session.

        Returns:
            Tuple[bool, Union[UserRolesDB, str]]: A tuple containing a boolean indicating success or failure,
            and either the user_roles object or an error message.
        """
        try:
            kwargs = {
                UserRoleDB.user_id: user_id,
                UserRoleDB.role_id: role_id,
            }
            # Query the user-permissions from the database
            user_roles = self.get_by_attributes(
                db,
                UserRoleDB,
                **kwargs,
            )
            if not user_roles:
                error = UserRoleExceptions.generate_user_role_not_found_error(
                    user_id=user_id, role_id=role_id
                )
                return False, error

            return True, user_roles
        except Exception as e:
            auth_logger.error("Error retrieving user roles for user_id=%s, role_id=%s: %s", user_id, role_id, str(e), exc_info=True)
            raise

    def get_user_with_roles(self, user_id: int, db: Session):
        """
        Retrieve a user with its associated roles.

        Args:
            user_id (int): The ID of the user.
            db (Session): The database session.

        Returns:
            UserDB: The user object with its associated roles.
        """
        user = (
            db.query(UserDB)
            .options(joinedload(UserDB.roles))
            .filter(UserDB.id == user_id)
            .first()
        )
        return user

    def assign_roles_to_user(self, user_id: int, role_ids: List[int], db: Session):
        """
        Assign roles to a user.

        Args:
            user_id (int): The ID of the user.
            role_ids (List[int]): The IDs of the roles to assign.
            db (Session): The database session.

        Returns:
            Tuple[bool, Union[List[UserRoleDB], Tuple[bool, str]]]: A tuple containing a boolean indicating success or failure,
            and either the list of assigned user role objects or an error message.
        """
        return self._update_user_roles(user_id, role_ids, db, add=True)

    def remove_roles_from_user(self, user_id: int, role_ids: List[int], db: Session):
        """
        Remove roles from a user.

        Args:
            user_id (int): The ID of the user.
            role_ids (List[int]): The IDs of the roles to remove.
            db (Session): The database session.

        Returns:
            Tuple[bool, Union[List[UserRoleDB], Tuple[bool, str]]]: A tuple containing a boolean indicating success or failure,
            and either the list of remaining user role objects or an error message.
        """
        return self._update_user_roles(user_id, role_ids, db, add=False)

    def _update_user_roles(
        self, user_id: int, role_ids: List[int], db: Session, add: bool
    ):
        """
        Update user roles by assigning or removing roles.

        Args:
            user_id (int): The ID of the user.
            role_ids (List[int]): The IDs of the roles to assign or remove.
            db (Session): The database session.
            add (bool): A flag indicating whether to assign or remove the roles.

        Returns:
            Tuple[bool, Union[List[UserRoleDB], List[userRoleDB]]]: A tuple containing a boolean indicating success or failure,
            and either the list of assigned user role objects or the list of removed user role objects.
        """
        # Check if the user exists
        is_user_exist, user = UserService().get_user_by_id(user_id=user_id, db=db)
        if not is_user_exist:
            return is_user_exist, user

        # Check if the roles exist
        is_roles_exist, roles = RoleService().is_one_or_more_roles_exists(role_ids, db)
        if not is_roles_exist:
            return is_roles_exist, roles

        # Check if the roles are already assigned to the user
        existing_user_roles = (
            db.query(UserRoleDB)
            .filter(UserRoleDB.user_id == user_id, UserRoleDB.role_id.in_(role_ids))
            .all()
        )

        # Assign or remove the roles to/from the user
        if add:
            role_ids = [
                role_id
                for role_id in role_ids
                if role_id not in [gr.role_id for gr in existing_user_roles]
            ]

            user_roles = [
                UserRoleDB(user_id=user_id, role_id=role_id) for role_id in role_ids
            ]
            db.add_all(user_roles)
        else:
            role_ids_to_remove = [
                gr.role_id for gr in existing_user_roles if gr.role_id in role_ids
            ]

            db.query(UserRoleDB).filter(
                UserRoleDB.user_id == user_id,
                UserRoleDB.role_id.in_(role_ids_to_remove),
            ).delete(synchronize_session=False)

        db.commit()

        return True, user_roles if add else existing_user_roles

    def get_user_roles(self, user_id):

        # Query the database for user roles
        user_roles = (
            self.db.query(UserRoleDB).filter(
                UserRoleDB.user_id == user_id).all()
        )

        # Return the user roles
        return user_roles

    def get_user_permissions(self, user_id, endpoint_url, endpoint_method):
        """
        Get the user permissions for a specific endpoint.

        Args:
            user_id (int): The ID of the user.
            endpoint_url (str): The URL of the endpoint.
            endpoint_method (str): The HTTP method of the endpoint.

        Returns:
            bool: True if the user has permission for the endpoint, False otherwise.
        """

        # Query the database for user roles
        user_roles = (
            self.db.query(UserRoleDB).filter(
                UserRoleDB.user_id == user_id).all()
        )
        # Get the role ids
        role_ids = [user_role.role_id for user_role in user_roles]
        # Query the database for groups associated with the roles
        groups = (
            self.db.query(GroupRoleDB).filter(
                GroupRoleDB.role_id.in_(role_ids)).all()
        )
        # Get the group ids
        group_ids = [group.group_id for group in groups]
        # Query the database for permissions associated with the groups
        group_permissions = (
            self.db.query(GroupPermissionDB)
            .filter(GroupPermissionDB.group_id.in_(group_ids))
            .all()
        )
        # Get the permission ids
        permission_ids = [
            group_permission.permission_id for group_permission in group_permissions
        ]
        # Query the database for the endpoint
        endpoint = (
            self.db.query(EndpointDB)
            .filter(
                EndpointDB.url == endpoint_url, EndpointDB.method == endpoint_method
            )
            .first()
        )
        # Check if the endpoint's permission is in the user's permissions
        if endpoint and endpoint.permission_id in permission_ids:
            return True

        # Close the session
        self.db.close()

        return False

    def add_roles_to_user(self, user_id: int, role_ids: list):
        """
        Assign roles to a user.

        Args:
            user_id (int): The ID of the user.
            role_ids (List[int]): The IDs of the roles to assign.

        Returns:
            Tuple[bool, Union[List[UserRoleDB], Tuple[bool, str]]]: A tuple containing a boolean indicating success or failure,
            and either the list of assigned user role objects or an error message.
        """

        # Fetch all roles at once
        roles = self.db.query(RoleDB).filter(RoleDB.id.in_(role_ids)).all()
        existing_role_ids = {role.id for role in roles}

        # Fetch all existing user-role relationships for the given user_id at once
        user_roles = (
            self.db.query(UserRoleDB)
            .filter(UserRoleDB.user_id == user_id, UserRoleDB.role_id.in_(role_ids))
            .all()
        )
        existing_user_role_ids = {
            user_role.role_id for user_role in user_roles}

        # Determine which role_ids are not found and which are new
        new_role_ids = existing_role_ids - existing_user_role_ids

        is_roles_exist, roles = RoleService().is_one_or_more_roles_exists(role_ids, self.db)
        if not is_roles_exist:
            return is_roles_exist, roles

        # Use a bulk insert operation to add all new user-role relationships at once
        new_user_roles = [
            UserRoleDB(user_id=user_id, role_id=role_id) for role_id in new_role_ids
        ]
        self.db.bulk_save_objects(new_user_roles)
        self.db.commit()
        self.db.close()
        return True, new_user_roles
