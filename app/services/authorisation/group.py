from sqlalchemy.orm import Session
from passlib.context import CryptContext


from app.database.database import engine, Base, SessionLocal
from app.models.authorisation import GroupDB, GroupPermissionDB, GroupRoleDB
from app.exception.custom.authorisation import GroupExceptions


from utils.db import BaseService
from utils.logger import setup_logger

group_logger = setup_logger(log_file="group.log")


class GroupService(BaseService):
    """
    Service class for managing group-related operations.
    """

    def __init__(self) -> None:
        self.db = SessionLocal()
        self.logger = group_logger

    def get_group_by_attribute(self, value: str, db: Session, attribute: str = None):
        """Retrieve a group by a specific attribute."""
        try:
            if not attribute:
                attribute = GroupDB.id
            # Query the group from the database
            group = self.get_by_attribute(db, GroupDB, attribute, value)
            if not group:
                error = GroupExceptions.generate_group_not_found_error(
                    group_id=value)
                return False, error

            return True, group
        except Exception as e:
            self.logger.error("Error retrieving group by attribute: %s", str(e), exc_info=True)
            raise

    def get_group_by_id(self, group_id: int, db: Session):
        """Retrieve a group by ID."""

        try:
            is_exist, group = self.get_group_by_attribute(
                value=group_id, db=db)
            return is_exist, group
        except Exception as e:
            self.logger.error("Error retrieving group by ID %s: %s", group_id, str(e), exc_info=True)
            raise

    def get_group_by_name(self, name: str, db: Session):
        """Retrieve a group by name."""

        try:
            is_exist, group = self.get_group_by_attribute(
                value=name, db=db, attribute=GroupDB.name
            )
            return is_exist, group
        except Exception as e:
            self.logger.error("Error retrieving group by name '%s': %s", name, str(e), exc_info=True)
            raise

    def get_all_groups(self, db: Session):
        """Retrieve all groups."""

        try:
            # Query all groups from the database.
            groups = self.get_all(db, GroupDB)
            return groups
        except Exception as e:
            self.logger.error("Error retrieving all groups: %s", str(e), exc_info=True)
            raise

    def create_group(self, group_data, db: Session):
        """Create a new group."""
        try:
            # Create a new group instance and add it to the database
            group = self.create(db, GroupDB, **group_data.dict())
            self.logger.info("Created new group: %s", group_data.name)
            return group
        except Exception as e:
            self.logger.error("Error creating group '%s': %s", group_data.name, str(e), exc_info=True)
            raise

    def update_group(self, group_id: int, group_data: dict, db: Session):
        """Update a group."""

        try:
            is_exist, group = self.get_group_by_id(group_id, db=db)
            if not is_exist:
                error = GroupExceptions.generate_group_not_found_error(
                    group_id=group_id
                )
                self.logger.warning("Group with ID %s not found for update", group_id)
                return False, error
            else:
                is_group_name_same = self.is_group_name_same(
                    group_id, group_data.name, db
                )
                if not is_group_name_same:
                    is_group_name_exist = self.is_group_by_name_alredy_exists(
                        group_data.name, db
                    )
                    if is_group_name_exist:
                        error = (
                            GroupExceptions.generate_group_name_already_exists_error(
                                group_name=group_data.name
                            )
                        )
                        self.logger.warning("Group with name '%s' already exists", group_data.name)
                        return False, error
                group_data_dict = group_data.dict()
                group_data_dict["id"] = group.id
                group = self.update(db, GroupDB, group_id, **group_data_dict)
                self.logger.info("Updated group with ID %s to name '%s'", group_id, group_data.name)
                return True, group

        except Exception as e:
            self.logger.error("Error updating group with ID %s: %s", group_id, str(e), exc_info=True)
            raise

    def delete_group(self, group_id: int, db: Session):
        """Delete a group."""

        try:
            is_exist, group = self.get_group_by_id(group_id, db=db)
            if not is_exist:
                error = GroupExceptions.generate_group_not_found_error(
                    group_id=group_id
                )
                self.logger.warning("Group with ID %s not found for deletion", group_id)
                return False, error

            error = self.check_group_associations(group_id, db)
            if error:
                self.logger.warning("Cannot delete group with ID %s due to associations", group_id)
                return False, error

            is_deleted = self.delete(db, GroupDB, group_id)
            self.logger.info("Deleted group with ID %s", group_id)
            return is_deleted, group_id
        except Exception as e:
            self.logger.error("Error deleting group with ID %s: %s", group_id, str(e), exc_info=True)
            raise

    def check_group_associations(self, group_id: int, db: Session):
        """
        Check if a group is associated with any permissions or roles.

        Args:
            group_id (int): The ID of the group.
            db (Session): The database session.

        Returns:
            GroupExceptions or None: The error if the group is associated with permissions or roles, None otherwise.
        """
        try:
            error = None

            is_group_has_permissions = self.is_group_has_permissions(
                group_id, db)
            if is_group_has_permissions:
                self.logger.info("Group with ID %s is associated with permissions", group_id)
                error = GroupExceptions.generate_group_associated_with_permissions_error(
                    group_id=group_id
                )

            is_group_has_roles = self.is_group_has_roles(group_id, db)
            if is_group_has_roles:
                self.logger.info("Group with ID %s is associated with roles", group_id)
                if error:
                    error.detail.append(
                        GroupExceptions.generate_group_associated_with_roles_error(
                            group_id=group_id
                        ).detail[0]
                    )
                else:
                    error = GroupExceptions.generate_group_associated_with_roles_error(
                        group_id=group_id
                    )

            return error
        except Exception as e:
            self.logger.error("Error checking group associations for ID %s: %s", group_id, str(e), exc_info=True)
            raise

    def is_group_exists(self, name: str):
        """Check if a group exists."""

        try:
            is_exist, group = self.get_group_by_name(name, self.db)
            return is_exist
        except Exception as e:
            self.logger.error("Error checking if group '%s' exists: %s", name, str(e), exc_info=True)
            raise

    def is_group_name_same(self, group_id: int, name: str, db: Session):
        """Check if a group name is the same as the provided name."""
        try:
            is_exist, group = self.get_group_by_id(group_id, db=db)
            if not is_exist:
                error = GroupExceptions.generate_group_not_found_error(
                    group_id=group_id
                )
                self.logger.warning("Group with ID %s not found when checking name", group_id)
                return False, error
            else:
                if group.name == name:
                    return True
                else:
                    return False
        except Exception as e:
            self.logger.error("Error checking if group ID %s has name '%s': %s", group_id, name, str(e), exc_info=True)
            raise

    def is_group_by_name_alredy_exists(self, name: str, db: Session):
        """Check if a group exists."""

        try:
            is_exist, group = self.get_group_by_name(name, db)
            return is_exist
        except Exception as e:
            self.logger.error("Error checking if group with name '%s' already exists: %s", name, str(e), exc_info=True)
            raise

    def is_group_has_permissions(self, group_id: int, db: Session):
        """
        Check if a group is associated with any permissions.

        Args:
            group_id (int): The ID of the group.
            db (Session): The database session.

        Returns:
            bool: True if the group is associated with any permissions, False otherwise.
        """
        try:
            group_permissions = (
                db.query(GroupPermissionDB)
                .filter(GroupPermissionDB.group_id == group_id)
                .all()
            )
            return bool(group_permissions)
        except Exception as e:
            self.logger.error("Error checking if group ID %s has permissions: %s", group_id, str(e), exc_info=True)
            raise

    def is_group_has_roles(self, group_id: int, db: Session):
        """
        Check if a group is associated with any roles.

        Args:
            group_id (int): The ID of the group.
            db (Session): The database session.

        Returns:
            bool: True if the group is associated with any roles, False otherwise.
        """
        try:
            group_roles = (
                db.query(GroupRoleDB)
                .filter(GroupRoleDB.group_id == group_id)
                .all()
            )
            return bool(group_roles)
        except Exception as e:
            self.logger.error("Error checking if group ID %s has roles: %s", group_id, str(e), exc_info=True)
            raise
