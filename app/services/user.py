import ast
import os
import threading
from datetime import datetime, timedelta
from typing import List, Optional

import jwt
from fastapi import HTT<PERSON>Exception
from fastapi.responses import JSONResponse
from passlib.context import CryptContext
from sqlalchemy import asc, desc, or_
from sqlalchemy.orm import Session, joinedload

import app
from app.database.database import get_db_session
from app.email.subjects.user import UserSubjects
from app.exception.custom.user import PasswordExceptions, UserExceptions
from app.exception.errors.user import AuthErrors, UsersErrors
from app.master.models.hospital import HospitalDB
from app.models.authorisation import RoleDB, UserRoleDB
from app.models.user import OtpDB, UserDB
from app.services.user_hospital import UserHospitalService
from app.patients.models.patient import PatientDB
from app.cases.models.case import Case
from utils.db import BaseService, BulkCreateService, QueryWithCount
from utils.email import Email
from utils.html_parser import HTMLParser
from utils.logger import setup_logger
from utils.mapper import Attribute<PERSON>apper
from utils.pagination import Pagin<PERSON><PERSON>elper
from utils.password import PasswordGenerator, TokenManager
from utils.uuid import UUIDGenerator

from utils.common import delete_object


user_logger = setup_logger(log_file="user.log")


def send_email_async(user, password):
    """Send email asynchronously."""
    email_sent = UserEmailService(
        user=user, password=password
    ).send_user_registration_email(recipient_email=user.email)
    if not email_sent:
        return False
    return True


class UserService(BaseService, BulkCreateService):
    """
    Service class for managing user-related operations.
    """

    def __init__(self) -> None:
        self.logger = user_logger

    def get_user_by_attribute(self, value: str, db: Session, attribute: str = None):
        """Retrieve a user by a specific attribute."""
        try:
            if not attribute:
                attribute = UserDB.id

            # Query the user from the database
            user = self.get_by_attribute(db, UserDB, attribute, value)
            if not user:
                # UserErrors().raise_user_not_found_exception(user_id=value)
                return False, None
            return True, user
        except Exception as e:
            self.logger.error("Error retrieving user by attribute: %s", str(e), exc_info=True)
            raise

    def get_user_or_raise(self, user_id: int, db: Session):
        try:
            is_exist, user = self.get_user_by_id(user_id, db)
            if not is_exist:
                UsersErrors().raise_user_not_found_exception(user_id=user_id)
            return user
        except Exception as e:
            self.logger.error("Error retrieving or raising user: %s", str(e), exc_info=True)
            raise

    def get_user_by_id(self, user_id: int, db: Session):
        """Retrieve a user by ID."""

        try:
            is_exist, user = self.get_user_by_attribute(value=user_id, db=db)
            return is_exist, user
        except Exception as e:
            self.logger.error("Error retrieving user by ID: %s", str(e), exc_info=True)
            raise

    def get_user_by_email(self, email: str, db: Session):
        """Retrieve a user by email."""

        try:
            # Query the user from the database
            is_exist, user = self.get_user_by_attribute(
                value=email, db=db, attribute=UserDB.email
            )
            if not is_exist:
                return False, UsersErrors().raise_user_not_found_with_email_exception(
                    email=email
                )
            if is_exist and user.is_active == False:
                return False, UsersErrors().raise_user_not_active_exception(email=email)
            return is_exist, user
        except Exception as e:
            self.logger.error("Error retrieving user by email: %s", str(e), exc_info=True)
            raise

    def get_all_users(self, db: Session):
        """Retrieve all users."""

        try:
            # Query all users from the database.
            users = self.get_all(db, UserDB)
            return users
        except Exception as e:
            self.logger.error("Error retrieving all users: %s", str(e), exc_info=True)
            raise

    def get_paginated_users(self, page: int, limit: int, db: Session):
        """Retrieve paginated users."""
        try:
            # Query the paginated users from the database
            users, total_count = self.get_paginated_list(
                db, UserDB, page=page, limit=limit
            )
            return users, total_count
        except Exception as e:
            self.logger.error("Error retrieving paginated users: %s", str(e), exc_info=True)
            raise

    def get_paginated_users_with_filter(
        self, page: int, limit: int, filter_by: dict, db: Session
    ):
        """Retrieve paginated users."""
        try:

            from app.models.authorisation import GroupDB, RoleDB

            offset = (page - 1) * limit

            # Query users with pagination and eager loading of roles and permissions
            query = db.query(UserDB).options(
                joinedload(UserDB.roles)
                .joinedload(RoleDB.groups)
                .joinedload(GroupDB.permissions)
            )
            if filter_by:
                query = query.filter_by(**filter_by)
            total_count = query.count()
            users = query.offset(offset).limit(limit).all()
            return users, total_count
        except Exception as e:
            raise

    def create_user(self, db: Session, user_data):
        try:
            with db.begin():
                password = PasswordGenerator().random_password()
                pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
                hashed_password = pwd_context.hash(password)
                user_data.password = hashed_password

                user_dict = user_data.dict()
                role_ids = user_dict.pop("roles")
                hospital_ids = user_dict.pop("hospitals")

                # Create new user
                new_user = UserDB(**user_dict)
                db.add(new_user)
                db.flush()  # Ensure the user is added to the session and has an ID

                self.logger.info(
                    f"User {new_user.email} created with ID {new_user.id}."
                )
                # Assign roles to the user
                roles = db.query(RoleDB).filter(RoleDB.id.in_(role_ids)).all()
                new_user.roles = roles
                db.flush()

                # Assign hospitals to the user
                hospitals = (
                    db.query(HospitalDB).filter(HospitalDB.id.in_(hospital_ids)).all()
                )
                new_user.hospitals = hospitals

                db.flush()  # Ensure the relationships are persisted
                db.refresh(new_user)  # Refresh the user instance

                # Send email in a separate thread
                email_thread = threading.Thread(
                    target=send_email_async, args=(new_user, password)
                )
                email_thread.start()
                self.logger.info(f"Email sent to user {new_user.email}.")

                db.refresh(new_user)
                return new_user
        except Exception as e:
            import traceback

            self.logger.error(f"Error creating user: {e}")
            raise

    def create_patient_user(self, user_data, db: Session):
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        password = user_data.pop("password")
        hashed_password = pwd_context.hash(password)
        user_data["password"] = hashed_password
        user_data["is_patient"] = True
        user = self.create(db, UserDB, **user_data)
        return user

    def update_user(self, user_id: int, user_data: dict, db: Session):
        """Update a user."""

        try:
            self.logger.info(f"Starting update for user_id: {user_id}")
            with db.begin():
                user = self.get_user_or_raise(user_id, db)

                user_data_dict = user_data.model_dump()  # Ensure correct method
                self.logger.info(f"User data: {user_data_dict}")

                roles = user_data_dict.pop("roles", [])  # Default to empty list
                hospitals = user_data_dict.pop("hospitals", [])  # Default to empty list

                # Mobile Number Validation
                mobile_number = user_data_dict.get("mobile_number")
                if mobile_number:
                    self.logger.info(f"Checking mobile number: {mobile_number}")
                    existing_user = (
                        db.query(UserDB)
                        .filter(UserDB.mobile_number == mobile_number)
                        .first()
                    )

                    if existing_user and existing_user.id != user_id:
                        self.logger.warning(
                            f"Mobile number already exists for a different user: {mobile_number}"
                        )
                        raise UsersErrors().raise_mobile_no_already_exists_exception(
                            mobile_number
                        )

                instance = db.query(UserDB).filter(UserDB.id == user_id).first()
                for attr, value in user_data_dict.items():
                    setattr(instance, attr, value)
                db.flush()
                db.refresh(instance)

                self.logger.info(f"User updated: {user_id}")

                # Update user roles & hospitals
                if roles:
                    user.roles = db.query(RoleDB).filter(RoleDB.id.in_(roles)).all()
                else:
                    user.roles = []  # Clear roles if empty list

                if hospitals:
                    user.hospitals = (
                        db.query(HospitalDB).filter(HospitalDB.id.in_(hospitals)).all()
                    )
                else:
                    user.hospitals = []  # Clear hospitals if empty list
                self.logger.info(f"User update successful for user_id: {user_id}")
                return user

        except Exception as e:
            self.logger.error(f"An error occurred while updating user: {e}")
            raise e

    def delete_user(self, user_id: int, force_delete: bool, db: Session):
        """Delete a user."""

        try:
            user = self.get_user_or_raise(user_id, db=db)
            
            # Check if the user has created any patients
            patients_created = db.query(PatientDB).filter(
                PatientDB.created_by == user_id
            ).first()
            
            # Check if the user has created any cases
            cases_created = db.query(Case).filter(
                Case.created_by == user_id
            ).first()
            
            # If force_delete is False, check for all mappings
            if not force_delete:
                if patients_created:
                    UsersErrors().raise_user_has_patients_error(user_id)
                
                if cases_created:
                    UsersErrors().raise_user_has_cases_error(user_id)
                
                # # Check for user-hospital mappings
                # if user.hospitals:
                #     hospital_names = [hospital.name for hospital in user.hospitals]
                #     raise HTTPException(
                #         status_code=400,
                #         detail=f"User has hospital mappings: {', '.join(hospital_names)}. Use force_delete=True to delete anyway."
                #     )
                
                # # Check for user-role mappings
                # if user.roles:
                #     role_names = [role.name for role in user.roles]
                #     raise HTTPException(
                #         status_code=400,
                #         detail=f"User has role mappings: {', '.join(role_names)}. Use force_delete=True to delete anyway."
                #     )
            
            # If force_delete is True, proceed with hard deletion regardless of mappings
            is_deleted = delete_object(session=db, force_delete=True, soft_delete=False, obj=user)
            
            if is_deleted:
                return "User deleted successfully"
            else:
                return "Failed to delete user"
            
        except Exception as e:
            self.logger.error(f"An error occurred while deleting user: {e}")
            self.logger.error("Error deleting user: %s", str(e), exc_info=True)
            raise

    def is_user_exists(self, email: str):
        """Check if a user exists."""

        try:
            with get_db_session() as db:
                is_exist, user = self.get_user_by_email(email, db)
                return is_exist
        except Exception as e:
            raise

    def is_user_exists_by_id(self, user_id: int):
        """Check if a user exists by id."""

        try:
            with get_db_session() as db:
                is_exist, user = self.get_user_by_id(user_id, db)
                return is_exist
        except Exception as e:
            raise

    def is_user_exists_by_email(self, email):
        """Check if a user exists by email."""

        try:
            with get_db_session() as db:
                is_exist, user = self.get_user_by_attribute(
                    value=email, db=db, attribute=UserDB.email
                )
                return is_exist
        except Exception as e:
            raise

    def get_user_by_mobile_number(self, mobile_number: str, db: Session):
        """Retrieve a user by mobile number."""
        try:
            is_exist, user = self.get_user_by_attribute(
                value=mobile_number, db=db, attribute=UserDB.mobile_number
            )
            if not is_exist:
                error = UserExceptions.generate_user_not_found_error(
                    user_id=mobile_number
                )
                return False, error

            return True, user

        except Exception as e:
            self.logger.error("Error retrieving user by mobile number: %s", str(e), exc_info=True)
            raise

    def is_user_has_roles(self, user_id: int, db: Session):
        """Check if a user has roles."""
        # Query the user-roles from the database
        user_roles = db.query(UserRoleDB).filter(UserRoleDB.user_id == user_id).first()
        return bool(user_roles)

    def assign_hospitals(self, user_id: int, hospital_ids: List[int], db: Session):
        """Assign hospitals to a user."""
        try:
            UserHospitalService().assign_hospitals_to_user(user_id, hospital_ids)
        except Exception as e:
            self.logger.error("Error assigning hospitals to user: %s", str(e), exc_info=True)
            raise

    def bulk_create_users(self, df, db: Session):
        """Create multiple new users master."""
        try:
            # users = self.create_instances(db, UserDB, user_data)
            users = BulkCreateUsers().bulk_create_users(db, df)
            return users
        except Exception as e:
            self.logger.error("Error bulk creating users: %s", str(e), exc_info=True)
            raise

    def update_user_password(self, db: Session, user: UserDB, password: str):
        """Update the password for a user."""
        try:
            pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
            hashed_password = pwd_context.hash(password)
            user.password = hashed_password
            db.commit()
            db.refresh(user)
            return user
        except Exception as e:
            self.logger.error("Error updating user password: %s", str(e), exc_info=True)
            raise

    def get_user_roles_group_perms(self, email: str, db: Session):
        """Retrieve the roles, groups, and permissions associated with a user."""
        # Query the UserDB model using the provided user ID and perform eager loading
        from app.models.authorisation import GroupDB, RoleDB

        is_exist, user = UserService().get_user_by_email(email, db)
        user = (
            db.query(UserDB)
            .options(
                joinedload(UserDB.roles)
                .joinedload(RoleDB.groups)
                .joinedload(GroupDB.permissions)
            )
            .get(user.id)
        )

        if not user:
            raise ValueError(f"No user found with ID {user.id}")

        # Collect the associated roles, groups, and permissions
        roles = user.roles
        groups = {group for role in roles for group in role.groups}
        permissions = {
            permission for group in groups for permission in group.permissions
        }

        return list(roles), list(groups), list(permissions)

    def filter_users(
        self,
        db: Session,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        email: Optional[str] = None,
    ):
        """Filter sub-categories by code and name."""
        query_with_count = QueryWithCount(self.init_query(db=db, model=UserDB))
        if first_name:
            query_with_count.apply_filter(db, UserDB, UserDB.first_name, first_name)
        if last_name:
            query_with_count.apply_filter(db, UserDB, UserDB.last_name, last_name)
        if email:
            query_with_count.apply_filter(db, UserDB, UserDB.email, email)
        return query_with_count.query.all()

    def filter_users_by_combined_name(
        self, db: Session, value: str, page: int, limit: int
    ):
        """Filter users by combined first and last name."""
        try:

            if value is None:
                query = db.query(UserDB).filter(UserDB.is_patient.is_(False))
            else:
                pattern = ".*".join(value.lower())
                query = db.query(UserDB).filter(
                    UserDB.full_name.op("REGEXP")(pattern), UserDB.is_patient.is_(False)
                )

            paginated_query = PaginationHelper.paginate_query(query, page, limit)
            return paginated_query.all(), query.count()
        except Exception as e:
            self.logger.error("Error filtering users by combined name: %s", str(e), exc_info=True)
            raise

    def filter_and_sort_users(
        self,
        db: Session,
        page: int = 1,
        limit: int = 10,
        order_by: str = None,
        **kwargs,
    ):
        from app.master.models.hospital import HospitalDB
        from app.models.authorisation import RoleDB

        try:
            query = db.query(UserDB).filter(UserDB.is_patient.is_(False))
            filter_functions = {
                "first_name": lambda value: query.filter(
                    or_(UserDB.first_name.like(f"%{value}%"))
                ),
                "last_name": lambda value: query.filter(
                    or_(UserDB.last_name.like(f"%{value}%"))
                ),
                "roles": lambda value: query.join(UserDB.roles).filter(
                    RoleDB.id.in_(value)
                ),
                "hospitals": lambda value: query.join(UserDB.hospitals).filter(
                    HospitalDB.name == value
                ),
                "email": lambda value: query.filter(
                    or_(UserDB.email.like(f"%{value}%"))
                ),
            }

            for attribute, value in kwargs.items():
                if value is not None and attribute in filter_functions:
                    query = filter_functions[attribute](value)
                elif value is not None and hasattr(UserDB, attribute):
                    query = query.filter(getattr(UserDB, attribute) == value)

            if order_by is not None:
                direction = desc if order_by.startswith("-") else asc
                attribute = order_by.lstrip("-")
                if hasattr(UserDB, attribute):
                    query = query.order_by(direction(getattr(UserDB, attribute)))
            total_count = query.count()
            query = query.offset((page - 1) * limit).limit(limit)

            return query.all(), total_count
        except Exception as e:
            self.logger.error("Error filtering and sorting users: %s", str(e), exc_info=True)
            raise


class AuthService:
    """
    Service class for user authentication.
    """

    def __init__(self) -> None:
        self.logger = user_logger

    def authenticate_user(self, user_details, db: Session):
        """
        Authenticate a user and generate a JWT token.

        Args:
            user_details: The details of the user.
            db (Session): The database session.

        Returns:
            str: The JWT token.

        Raises:
            HTTPException: If the user is not found or the password is incorrect.
        """
        try:
            # Query the user from the database
            user_exists, user = UserService().get_user_by_email(
                email=user_details.email, db=db
            )
            if not user_exists:
                UsersErrors().raise_user_not_found_with_email_exception(
                    email=user_details.email
                )

            if user.is_active == False:
                # return False, UsersErrors().generate_user_not_active_error(user.id)
                UsersErrors().raise_user_not_active_exception(email=user_details.email)

                # return user_exists, user

            # Verify the password
            if not self.verify_password(user_details.password, user.password):
                # AuthErrors().raise_password_not_match_error()
                AuthErrors().raise_invalid_creadentials_exception(user_details.dict())
                # error = (
                #     AuthenticationExceptions.generate_password_does_not_match_error()
                # )
                # return False, error

            # Generate the JWT token
            token = self.generate_token(user.id)

            return True, token
        except Exception as e:
            self.logger.error("Error authenticating user: %s", str(e), exc_info=True)
            raise

    def verify_password(self, plain_password: str, hashed_password: str):
        """
        Verify a plain password against a hashed password.

        Args:
            plain_password (str): The plain password.
            hashed_password (str): The hashed password.

        Returns:
            bool: True if the passwords match, False otherwise.
        """
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        return pwd_context.verify(plain_password, hashed_password)

    def generate_token(self, user_id: int):
        """
        Generate a JWT token for a user.

        Args:
            user_id (int): The ID of the user.

        Returns:
            str: The JWT token.
        """
        # Set the expiration time for the token
        # expiry_unit = os.getenv("TOKEN_EXPIRY_UNIT", "minutes")
        expiry_value = int(os.getenv("TOKEN_EXPIRY", 15))
        expires_delta = timedelta(
            minutes=expiry_value
        )  # Set token expiration to 15 minutes
        expire = datetime.utcnow() + expires_delta

        # Create the payload for the token
        payload = {"user_id": user_id, "exp": expire}

        # Read secret key from environment variable
        secret_key = os.getenv("SECRET_KEY")
        # Generate the token
        token = jwt.encode(payload, secret_key, algorithm="HS256")

        return token

    def verify_token(self, token: str):
        """
        Verify a JWT token.

        Args:
            token (str): The JWT token to verify.

        Returns:
            dict: The decoded token payload if the token is valid.

        Raises:
            HTTPException: If the token is invalid or has expired.
        """
        try:
            # Read secret key from environment variable
            secret_key = os.getenv("SECRET_KEY")
            # Decode the token
            payload = jwt.decode(token, secret_key, algorithms=["HS256"])
            # Check if the token has expired
            if datetime.utcnow() > datetime.fromtimestamp(payload["exp"]):
                raise HTTPException(status_code=401, detail="Token has expired")
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=401, detail="Token has expired")
        except jwt.InvalidTokenError:
            raise HTTPException(status_code=401, detail="Invalid token")

    def get_user_by_phone_number(self, phone_number: str, db: Session):
        """
        Retrieve a user by phone number from the database.

        Args:
            phone_number (str): The phone number of the user to retrieve.
            db (Session): The database session.

        Returns:
            UserDB: The user object.

        Raises:
            HTTPException: If the user is not found or an internal server error occurs.
        """
        try:
            # Query the user from the database
            user = db.query(UserDB).filter(UserDB.mobile_number == phone_number).first()
            if not user:
                raise HTTPException(status_code=404, detail="User not found")

            return user
        except Exception as e:
            raise

    def save_otp(self, mobile_details: str, otp: str, db: Session):
        """
        Save the OTP for a user in the database.

        Args:
            user_id (int): The ID of the user.
            otp (str): The OTP to save.
            db (Session): The database session.

        Returns:
            None

        Raises:
            HTTPException: If an internal server error occurs.
        """
        try:
            # Query the user from the database
            user = (
                db.query(UserDB)
                .filter(UserDB.mobile_number == mobile_details.mobile_number)
                .first()
            )
            if not user:
                raise HTTPException(status_code=404, detail="User not found")

            # Save the OTP for the user
            otp_db = OtpDB(user_id=user.id, mobile_number=user.mobile_number, otp=otp)
            db.add(otp_db)
            db.commit()
        except Exception as e:
            self.logger.error("Error saving OTP: %s", str(e), exc_info=True)
            raise

    def verify_otp(self, mobile_number: str, otp: str, db: Session):
        """
        Verify the OTP for a user.

        Args:
            user_id (int): The ID of the user.
            otp (str): The OTP to verify.
            db (Session): The database session.

        Returns:
            bool: True if the OTP is valid, False otherwise.

        Raises:
            HTTPException: If an internal server error occurs.
        """
        try:
            # Query the OTP from the database
            otp_db = (
                db.query(OtpDB)
                .filter(OtpDB.mobile_number == mobile_number, OtpDB.is_active == True)
                .first()
            )
            if not otp_db:
                return JSONResponse(
                    status_code=404, content={"message": "OTP not found"}
                )

            # Verify the OTP
            if otp_db.otp == otp:
                # Mark the OTP as inactive
                otp_db.is_active = False
                db.commit()
                return JSONResponse(
                    status_code=200, content={"message": "OTP Verified."}
                )
            else:
                return JSONResponse(
                    status_code=404, content={"message": "OTP not found"}
                )
        except Exception as e:
            return JSONResponse(
                status_code=500, content={"message": "Internal server error"}
            )

    def is_existing_valid_otp(self, mobile_number: str, db: Session):
        """
        Check if there is an existing valid OTP for a user.

        Args:
            mobile_number (str): The mobile number of the user.
            db (Session): The database session.

        Returns:
            bool: True if there is an existing valid OTP, False otherwise.

        Raises:
            HTTPException: If an internal server error occurs.
        """
        try:
            # Query the OTP from the database
            otp_db = (
                db.query(OtpDB)
                .filter(
                    OtpDB.mobile_number == mobile_number,
                    OtpDB.is_active == True,
                    OtpDB.expire_at > datetime.utcnow(),
                )
                .first()
            )
            if otp_db:
                return True
            else:
                return False
        except Exception as e:
            self.logger.error("Error checking valid OTP: %s", str(e), exc_info=True)
            raise HTTPException(status_code=500, detail="Internal server error") from e

    # def change_password(self, user_id: int, old_password: str, new_password: str, confirm_password: str, db: Session):
    #     """Change the password for a user."""
    #     try:
    #         # Retrieve the user by ID
    #         is_exist, user = self.get_user_by_id(user_id, db=db)
    #         if not is_exist:
    #             raise UserExceptions.user_not_found()

    #         # Verify the old password
    #         if not self.verify_password(old_password, user.password):
    #             raise UserExceptions.incorrect_password()

    #         # Check if the new passwords match
    #         if new_password != confirm_password:
    #             raise UserExceptions.password_mismatch()

    #         # Hash the new password
    #         hashed_password = self.hash_password(new_password)

    #         # Update the user's password in the database
    #         user.password = hashed_password
    #         db.commit()

    #     except Exception as e:
    # self.logger.error("Error: %s", str(e), exc_info=True)
    #         raise HTTPException(status_code=500, detail="Internal server error") from e

    def get_user_from_token(self, token: str):
        try:
            # Decode the token
            token = token.replace("Bearer ", "")

            secret_key = os.getenv("SECRET_KEY")
            payload = jwt.decode(token, secret_key, algorithms=["HS256"])
            
            if "user_id" in payload:
                user_id = payload.get("user_id")
                with get_db_session() as db:
                    # Query the database for the user
                    user = (
                        db.query(UserDB)
                        .options(joinedload(UserDB.hospitals), joinedload(UserDB.roles))
                        .filter(UserDB.id == user_id)
                        .first()
                    )
                    
                    # Check if user exists and is active
                    if user and user.is_active:
                        return user
                    return None
            return None

        except Exception as e:
            self.logger.error("Error getting user from token: %s", str(e), exc_info=True)
            return None


class PasswordService:

    def __init__(self) -> None:
        self.token_manager = TokenManager()

    def get_reset_password_link(self, email: str, is_web: bool):
        """
        Generate a reset password link for a user.

        Args:
            email (str): The email address of the user.

        Returns:
            str: The reset password link.
        """
        try:
            # Create a token for the user
            token = self.token_manager.create_token(email)
            reset_link = (
                os.getenv("PASSWORD_RESET_URL")
                + token
                + "/"
                + email
                + "?is_web="
                + str(is_web)
            )
            return reset_link
        except Exception as e:
            self.logger.error("Error getting reset password link: %s", str(e), exc_info=True)
            raise

    def reset_password(self, token: str, password):
        """
        Reset the password for a user.

        Args:
            token (str): The token for the user.
            password (str): The new password.

        Returns:
            bool: True if the password is reset successfully, False otherwise.
        """
        try:

            # Load the token
            email = self.token_manager.load_token(token)
            if not email:
                UsersErrors().raise_token_expired_exception(token=token)
            # Retrieve the user by email
            with get_db_session() as db:
                is_exist, user = UserService().get_user_by_email(email, db)
                if not is_exist:
                    return is_exist, user
                if password.new_password != password.confirm_password:
                    error = PasswordExceptions.generate_password_mismatch_error()
                    return False, error
                # Update the user's password
                user = UserService().update_user_password(
                    db, user, password.confirm_password
                )

                return True, user
        except Exception as e:
            raise


class UserEmailService:
    """
    Service class for managing user-related email operations.
    """

    def __init__(self, user: UserDB, password: str) -> None:
        self.user = user
        self.password = password
        self.logger = user_logger

    def get_user_registration_email_content(self):
        """
        Get the email content for user registration.

        Args:
            user (UserDB): The user object.

        Returns:
            str: The email content.
        """
        try:
            reset_link = PasswordService().get_reset_password_link(
                email=self.user.email, is_web=True
            )
            uuid = UUIDGenerator().generate()
            kawrgs = AttributeMapper(
                email=self.user.email,
                first_name=self.user.first_name,
                last_name=self.user.last_name,
                reset_link=reset_link,
                uuid=uuid,
            ).__dict__
            template_path = os.getcwd() + os.getenv("TEMPLATE_PATH")
            html_content = HTMLParser(template_path=template_path).get_html_content(
                file_name="user_registration.html", **kawrgs
            )
            return html_content
        except Exception as e:
            self.logger.error("Error getting user registration email content: %s", str(e), exc_info=True)
            raise

    def send_user_registration_email(self, recipient_email: str):
        """
        Send an email with HTML content.

        Args:
            recipient_email (str): The email address of the recipient.
            subject (str): The subject of the email.
            html_content (str): The HTML content of the email.

        Returns:
            None
        """
        try:
            subject = UserSubjects().get_user_registration_subject()
            html_content = self.get_user_registration_email_content()
            is_sent = Email(
                recipient_email=recipient_email,
                subject=subject,
                html_content=html_content,
            ).send()
            if is_sent:
                return True
            else:
                return False
        except Exception as e:
            self.logger.error("Error sending user registration email: %s", str(e), exc_info=True)
            raise

    def send_forgot_password_email(self, recipient_email: str, reset_link: str):
        try:
            kwargs = AttributeMapper(
                reset_link=reset_link, email=recipient_email
            ).__dict__
            subject = UserSubjects().get_reset_password_subject()
            template_path = os.getcwd() + os.getenv("TEMPLATE_PATH")
            html_content = HTMLParser(template_path=template_path).get_html_content(
                file_name="forgot_password.html", **kwargs
            )
            is_sent = Email(
                recipient_email=recipient_email,
                subject=subject,
                html_content=html_content,
            ).send()
            if is_sent:
                return True
            else:
                return False
        except Exception as e:
            self.logger.error("Error sending forgot password email: %s", str(e), exc_info=True)
            raise


class BulkCreateUsers:

    def create_role_for_user(self, user, roles):
        # Implement this method to create roles for a user

        (
            is_created,
            content,
        ) = app.services.authorisation.user_roles.UserRolesService().add_roles_to_user(
            user_id=user.id, role_ids=roles
        )

    def create_hospital_for_user(self, user, hospitals, db):
        # Implement this method to create hospitals for a user
        UserService().assign_hospitals(user_id=user.id, hospital_ids=hospitals, db=db)

    def bulk_create_users(self, db, df):
        # Read the Excel file

        created_users = []  # List to store the created users

        # Iterate over the rows of the DataFrame
        for _, row in df.iterrows():
            user_data = row.to_dict()
            roles = ast.literal_eval(user_data.pop("roles", "[]"))
            hospitals = ast.literal_eval(user_data.pop("hospitals", "[]"))
            with get_db_session() as db:
                # Create the user
                user = UserDB(**user_data)
                db.add(user)
                db.commit()

            # Add the user to the list of created users
            created_users.append(user)

            # If the user has roles, create them
            if roles:
                self.create_role_for_user(user, roles)

            # If the user has hospitals, create them
            if hospitals:
                self.create_hospital_for_user(user, hospitals, db)

        return created_users


class UserHelperService:
    """Service class for user-related operations."""

    def update_user_roles(cls, db: Session, user, role_ids: list):
        """Update roles for a user."""
        from app.models.authorisation import RoleDB

        # Get roles
        roles = db.query(RoleDB).filter(RoleDB.id.in_(role_ids)).all()

        # Update user roles
        user.roles = roles

        # Commit changes
        db.commit()

    def update_user_hospitals(cls, db: Session, user, hospital_ids: list):
        """Update hospitals for a user."""
        from app.master.models.hospital import HospitalDB

        # Get hospitals
        hospitals = db.query(HospitalDB).filter(HospitalDB.id.in_(hospital_ids)).all()

        # Update user hospitals
        user.hospitals = hospitals

        # Commit changes
        db.commit()
