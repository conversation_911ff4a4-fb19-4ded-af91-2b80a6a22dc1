from fastapi import Fast<PERSON><PERSON>, Request, status, HTTPException
from fastapi.encoders import jsonable_encoder
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware

from app.middleware.authentication import CustomMiddleware
from app.database.database import Base, engine
from app.routers.user import router as user_router
from app.routers.authorisation.endpoint import router as endpoint_router
from app.routers.authorisation.group import router as group_router
from app.routers.authorisation.permission import router as permission_router
from app.routers.authorisation.role import router as role_router
from app.routers.authorisation.group_permission import router as group_permission_router
from app.routers.authorisation.group_role import router as group_role_router
from app.routers.authorisation.user_roles import router as user_roles_router

# from app.patients.routers.document_type import router as document_router

# from app.patients.routers.document_master import router as document_master_router
from app.patients.routers.patient import router as patient_router
from app.master.routers.step_master import router as step_router

from app.master.routers.scheme_type import router as scheme_type_router
from app.master.routers.document_master import router as document_master_router

from app.address.routers.sub_district import router as taluka_router
from app.address.routers.state import router as state_router
from app.address.routers.district import router as district_router

# master
from app.master.routers.category import router as category_router
from app.master.routers.sub_category import router as sub_category_router
from app.master.routers.package import router as package_router
from app.master.routers.package_step import router as package_step_router
from app.master.routers.package_step_document import (
    router as package_step_document_router,
)
from app.master.routers.hospital import router as hospital_router
from app.master.routers.scheme_document import router as scheme_document_router

from app.cases.routers import case_router, case_step_router, case_step_document_router
from app.routers.user_hospital import router as user_hospital_router
from app.notification.routers.notification import router as notification_router
from app.reports.routers.reports import router as reports_router

from app.health_check.routers.halth_check import router as health_check_router

tags_metadata = [
    {
        "name": "User",
        "description": "Operations related to user management, including user creation, updates, retrieval,login and logout.",
    },
    {
        "name": "Authorisation",
        "description": "Operations involving authorisation processes, including  and permission management.",
    },
    {
        "name": "Master",
        "description": "Operations for handling master data that serves as the reference for other modules.",
    },
    {
        "name": "Address Master",
        "description": "Operations for managing address master data, including addition, update, and retrieval of address records.",
    },
    {
        "name": "Patient",
        "description": "Operations related to patient data management, including patient creation, updates, and information retrieval.",
    },
    {
        "name": "Case",
        "description": "Operations for managing cases, including case creation, updates, and retrieval.",
    },
    {
        "name": "Case Step",
        "description": "Operations related to the steps within a case, including creation, updates, and retrieval of case steps.",
    },
    {
        "name": "Case Step Document",
        "description": "Operations involving documents associated with case steps, including upload, update, and retrieval of documents.",
    },
    {
        "name": "Notification",
        "description": "Operations for managing notifications, including creation, updates, and retrieval of notifications.",
    },
    {
        "name": "Reports",
        "description": "Operations for generating reports.",
    },
]

# Base.metadata.create_all(bind=engine)
app = FastAPI(openapi_tags=tags_metadata)


app.add_middleware(CustomMiddleware)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)


# app.add_middleware(AuthorisationMiddleware)


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    # Get the original 'detail' list of errors
    details = exc.errors()
    modified_details = []
    # Replace 'msg' with 'message' for each error
    for error in details:
        modified_details.append(
            {
                "loc": error["loc"],
                "msg": error["msg"].replace("Value error,", "").strip(),
            }
        )
    return JSONResponse(status_code=422, content={"detail": modified_details})

    # raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=modified_details)


app.include_router(user_router, tags=["User"])
app.include_router(group_router, tags=["Authorisation"])
app.include_router(endpoint_router, tags=["Authorisation"])
app.include_router(permission_router, tags=["Authorisation"])
app.include_router(role_router, tags=["Authorisation"])
app.include_router(group_permission_router, tags=["Authorisation"])
app.include_router(group_role_router, tags=["Authorisation"])
app.include_router(user_roles_router, tags=["Authorisation"])


# Address
app.include_router(taluka_router, tags=["Address Master"])
app.include_router(state_router, tags=["Address Master"])
app.include_router(district_router, tags=["Address Master"])

# Patient
app.include_router(patient_router, tags=["Patient"])

# Master
app.include_router(step_router, tags=["Master"])
app.include_router(scheme_type_router, tags=["Master"])
app.include_router(document_master_router, tags=["Master"])
app.include_router(category_router, tags=["Master"])
app.include_router(hospital_router, tags=["Master"])
app.include_router(sub_category_router, tags=["Master"])
app.include_router(package_router, tags=["Master"])
app.include_router(package_step_router, tags=["Master"])
app.include_router(package_step_document_router, tags=["Master"])
app.include_router(scheme_document_router, tags=["Master"])
app.include_router(user_hospital_router, tags=["Master"])
# Case
app.include_router(case_router, tags=["Case"])
app.include_router(case_step_router, tags=["Case Step"])
app.include_router(case_step_document_router, tags=["Case Step Document"])
# Notification
app.include_router(notification_router, tags=["Notification"])
app.include_router(reports_router, tags=["Reports"])

app.include_router(health_check_router, tags=["Health Check"])



def check_endpoint_existence(endpoint_path):
    """Check if an endpoint exists."""
    for route in app.routes:
        if route.path == endpoint_path:
            return True
    return False
