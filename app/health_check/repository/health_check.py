import asyncio
from sqlalchemy.orm import Session
from sqlalchemy.exc import OperationalError
from sqlalchemy.sql import text
class HealthCheckRepository:
    """
    Repository layer for health checks.
    """

    @staticmethod
    async def check_database_connection(db: Session):
        """
        Check the database connection.

        Args:
            db (Session): Database session dependency.

        Returns:
            str: "connected" if the database is reachable, otherwise "disconnected".
        """
        try:
            query = text("SELECT 1")
            result = db.execute(query)
            result.fetchone()
            return "connected"
        except OperationalError:
            return "disconnected"

    @staticmethod
    async def check_uptime():
        """
        Simulate an uptime check.

        Returns:
            str: "OK" if the service is running, otherwise "down".
        """
        await asyncio.sleep(0.1)  # Simulate async operation
        return "OK"