from fastapi import APIRouter, Depends, status
from utils.generate_response import generate_response
from utils.common import get_db
from utils.helpers import ExceptionHandler
from app.health_check.services.health_check import HealthCheckService
from sqlalchemy.orm import Session

# Create an API router
router = APIRouter()

@router.get("/health", summary="Health Check", tags=["Health Check"])
async def health_check(db: Session = Depends(get_db)):
    """
    Perform a health check and return the status of the application.

    Args:
        db (AsyncSession): Database session dependency.

    Returns:
        dict: Health status of the application.
    """
    try:
        health_status = await HealthCheckService.perform_health_check(db)
        # return health_status
        return generate_response(
            data=health_status,
            status_code=status.HTTP_200_OK,
            message="Health check successful",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)
