from app.health_check.repository.health_check import HealthCheckRepository

class HealthCheckService:
    """
    Service layer for health checks.
    """

    @staticmethod
    async def perform_health_check(db):
        """
        Perform health checks for the application.

        Args:
            db (AsyncSession): Database session dependency.

        Returns:
            dict: Health status of the application.
        """
        database_status = await HealthCheckRepository.check_database_connection(db)
        uptime_status = await HealthCheckRepository.check_uptime()

        return {
            "status": "healthy" if database_status == "connected" else "unhealthy",
            "database": database_status,
            "uptime": uptime_status
        }