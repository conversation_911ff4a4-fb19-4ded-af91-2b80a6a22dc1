from app.health_check.repository.health_check import HealthCheckRepository
from app.database.database import check_database_health

class HealthCheckService:
    """
    Service layer for health checks.
    """

    @staticmethod
    async def perform_health_check(db):
        """
        Perform health checks for the application.

        Args:
            db (Session): Database session dependency.

        Returns:
            dict: Health status of the application.
        """
        try:
            # Use our improved database health check
            database_healthy = check_database_health()
            database_status = "connected" if database_healthy else "disconnected"

            # Get uptime status
            uptime_status = await HealthCheckRepository.check_uptime()

            return {
                "status": "healthy" if database_healthy else "unhealthy",
                "database": database_status,
                "uptime": uptime_status,
                "timestamp": uptime_status.get("timestamp") if isinstance(uptime_status, dict) else None
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "database": "error",
                "uptime": "unknown",
                "error": str(e)
            }