from app.cases.models.case_step_document import CaseStepDocument
from app.cases.services.case import case_logger
from sqlalchemy import func

class CaseStepDocumentHelper:
    def are_all_mandatory_documents_approved(self, db, case_step_id):
        from app.cases.models.case import StatusMaster

        case_logger.info(f"Checking if all mandatory documents are approved for case step ID: {case_step_id}")

        try:
            approved_status_id = (
                db.query(StatusMaster.id)
                .filter(
                    StatusMaster.key == "case_step_document",
                    StatusMaster.name == "Approved",
                )
                .scalar()
            )
            case_logger.debug(f"Approved status ID: {approved_status_id}")

            mandatory_documents = (
                db.query(CaseStepDocument)
                .filter(
                    CaseStepDocument.case_step_id == case_step_id,
                    CaseStepDocument.is_required == True,
                )
                .all()
            )
            case_logger.debug(f"Mandatory documents count for case step ID {case_step_id}: {len(mandatory_documents)}")

            for document in mandatory_documents:
                case_logger.debug(f"Checking document ID: {document.id}, status: {document.status}")
                if document.status != approved_status_id:
                    case_logger.info(f"Document ID {document.id} is not approved.")
                    return False

            case_logger.info(f"All mandatory documents are approved for case step ID: {case_step_id}")
            return True

        except Exception as e:
            case_logger.error(f"Error checking mandatory documents for case step ID {case_step_id}: {str(e)}", exc_info=True)
            raise

    def is_any_document_pending_or_rejected(self, db, case_step_id):
        from app.cases.models.case import StatusMaster

        case_logger.info(f"Checking if any document is in Pending or Rejected status for case step ID: {case_step_id}")

        try:
            pending_status_id = (
                db.query(StatusMaster.id)
                .filter(
                    StatusMaster.key == "case_step_document",
                    StatusMaster.name == "Pending",
                )
                .scalar()
            )
            rejected_status_id = (
                db.query(StatusMaster.id)
                .filter(
                    StatusMaster.key == "case_step_document",
                    StatusMaster.name == "Rejected",
                )
                .scalar()
            )
            case_logger.debug(f"Pending status ID: {pending_status_id}, Rejected status ID: {rejected_status_id}")

            documents = (
                db.query(CaseStepDocument)
                .filter(
                    CaseStepDocument.case_step_id == case_step_id,
                    CaseStepDocument.status.in_([pending_status_id, rejected_status_id]),
                )
                .all()
            )
            case_logger.debug(f"Documents in Pending or Rejected status for case step ID {case_step_id}: {documents}")

            if documents:
                case_logger.info(f"Found documents in Pending or Rejected status for case step ID: {case_step_id}")
                return True

            case_logger.info(f"No documents in Pending or Rejected status for case step ID: {case_step_id}")
            return False

        except Exception as e:
            case_logger.error(f"Error checking documents for case step ID {case_step_id}: {str(e)}", exc_info=True)
            raise


    def has_required_documents(self, db, case_step_id):
            """
            Check if a case step has any required documents.
            
            Args:
                db (Session): The database session.
                case_step_id (int): The ID of the case step to check.
                
            Returns:
                bool: True if the step has any required documents, False otherwise.
            """
            case_logger.info(
                f"Checking if case step ID {case_step_id} has required documents"
            )
            
            required_documents_count = (
                db.query(func.count(CaseStepDocument.id))
                .filter(
                    CaseStepDocument.case_step_id == case_step_id,
                    CaseStepDocument.is_required == True,
                )
                .scalar()
            )
            
            case_logger.debug(
                f"Required documents count for case step ID {case_step_id}: {required_documents_count}"
            )
            
            return required_documents_count > 0