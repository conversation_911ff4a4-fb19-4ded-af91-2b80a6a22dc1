from datetime import datetime
from typing import List
from sqlalchemy import func

from app.cases.exception.custom.case_step_master import CaseStepExceptions
from app.cases.models import CaseStep, CaseStepDocument
from app.cases.models.case import Case, StatusMaster
from app.cases.services.case_audit_trail import CaseAuditTrailService
from app.database.database import SessionLocal
from app.master.models.document_master import DocumentMaster
from app.master.models.package import PackageStepMasterDB, StepMaster
from app.notification.models.notification import NotificationMasterDB
from app.notification.services.notification import NotificationService
from sqlalchemy import asc, desc, or_
from sqlalchemy.orm import Session, joinedload,aliased
from utils.db import BaseService

from .case import case_logger


class CaseStepService(BaseService):
    """
    Service class for managing cassestep master-related operations.
    """

    def __init__(self):
        self.logger = case_logger

    def bulk_create_case_steps(
        self, db: Session, package_step_master_ids: List[int], case_id: int
    ):
        """Bulk create CaseStep instances."""
        case_steps_data = [
            {"case_id": case_id, "package_step_master_id": package_step_master_id}
            for package_step_master_id in package_step_master_ids
        ]
        self.bulk_create(db, CaseStep, case_steps_data)

    def get_step_by_attribute(self, value: str, db: Session, attribute: str = None):
        """Retrieve a case step  by a specific attribute."""
        try:
            if not attribute:
                attribute = CaseStep.id
            # Query the case step  from the database
            step = self.get_by_attribute(db, CaseStep, attribute, value)
            return True, step
        except Exception as e:
            self.logger.error(e)
            raise

    def get_case_step_by_id(self, case_step_id: int, db: Session):
        """Retrieve a case step  by ID."""

        try:
            is_exist, step = self.get_step_by_attribute(value=case_step_id, db=db)
            if not is_exist:
                CaseStepExceptions().raise_case_step_not_found_exception(case_step_id)
            return is_exist, step
        except Exception as e:
            self.logger.error(e)
            raise

    def get_case_step_or_raise(self, db: Session, case_step_id: int):
        step = self.get_by_attribute(db, CaseStep, CaseStep.id, case_step_id)
        if not step:
            CaseStepExceptions().raise_case_step_not_found_exception(case_step_id)
        return step

    def get_case_step_details_by_id(self, db: Session, case_step_id: int, **kwargs):
        """Retrieve a single case step by its ID."""
        try:
            user = kwargs.get("user")
            step = self.get_case_step_or_raise(db=db, case_step_id=case_step_id)
            # Fetch the specific case step from the da  tabase
            case_step = (
                db.query(CaseStep)
                .join(
                    PackageStepMasterDB,
                    CaseStep.package_step_master_id == PackageStepMasterDB.id,
                )
                .join(StepMaster, PackageStepMasterDB.step_id == StepMaster.id)
                .filter(CaseStep.id == case_step_id)
                .options(
                    joinedload(CaseStep.case_step_package_step_master).joinedload(
                        PackageStepMasterDB.steps
                    )
                )
                .first()
            )

            # Retrieve associated documents for the step
            step_documents_query = db.query(CaseStepDocument).filter_by(
                case_step_id=case_step.id
            )
            if user.is_patient:
                # If user is a patient, filter documents uploaded by the patient and exclude others
                step_documents_query = step_documents_query.filter(
                    or_(
                        CaseStepDocument.uploaded_by == user.id,
                        ~CaseStepDocument.id.in_(
                            db.query(CaseStepDocument.id)
                            .filter(CaseStepDocument.case_step_id == step.id)
                            .filter(CaseStepDocument.uploaded_by != user.id)
                        ),
                    )
                )

            # Fetch the documents
            step_documents = step_documents_query.all()
            documents_data = []
            required_doc_count = 0
            for document in step_documents:
                document_master = (
                    db.query(DocumentMaster)
                    .filter_by(id=document.document_master_id)
                    .first()
                )
                documents_data.append(
                    {
                        "id": document.id,
                        "guid": document.guid,
                        "document_name": document_master.name,
                        "file_name": document.file_name,
                        "version": document.version,
                        "comment": document.comment,
                        "is_required": document_master.is_required,
                        "is_geotag_required": document_master.is_geotag_required,
                        "status": document.status_master,
                    }
                )

                if document_master.is_required:
                    required_doc_count += 1

            # Sort documents_data based on is_required flag (True first, then False)
            documents_data.sort(key=lambda x: not x["is_required"])

            step_data = {
                "position": case_step.case_step_package_step_master.steps.order,
                "id": case_step.id,
                "name": case_step.case_step_package_step_master.steps.type,
                "case_step_date": case_step.case_step_date,
                "status": case_step.status_master,
                "documents": documents_data,
                "required_doc_count": required_doc_count,
            }

            # If the step is a treatment step (position 3), add additional fields
            if step_data["position"] == 3:
                # Fetch all treatment steps for the case
                treatment_steps = (
                    db.query(CaseStep)
                    .join(
                        PackageStepMasterDB,
                        CaseStep.package_step_master_id == PackageStepMasterDB.id,
                    )
                    .join(StepMaster, PackageStepMasterDB.step_id == StepMaster.id)
                    .filter(
                        CaseStep.case_id == case_step.case_id, StepMaster.order == 3
                    )
                    .order_by(desc(CaseStep.case_step_date))
                    .all()
                )

                # Determine previous and next treatment dates
                current_step_index = next(
                    (
                        i
                        for i, step in enumerate(treatment_steps)
                        if step.id == case_step_id
                    ),
                    None,
                )
                previous_treatment_date = (
                    treatment_steps[current_step_index + 1].case_step_date.date()
                    if current_step_index is not None
                    and current_step_index < len(treatment_steps) - 1
                    and treatment_steps[current_step_index + 1].case_step_date
                    else None
                )
                next_treatment_date = (
                    treatment_steps[current_step_index - 1].case_step_date.date()
                    if current_step_index is not None
                    and current_step_index > 0
                    and treatment_steps[current_step_index - 1].case_step_date
                    else None
                )

                step_data["previous_treatment_date"] = previous_treatment_date
                step_data["next_treatment_date"] = next_treatment_date

            return step_data
        except Exception as e:
            self.logger.error(e)
            raise

    def get_all_case_steps(self, db: Session, **kwargs):
        """Retrieve all case steps with additional case information."""
        try:
            case_id = kwargs.pop("case_id")
            current_date = datetime.now().date()
            user = kwargs.pop("user")
            
            # First, get the case data to retrieve additional information
            case = db.query(Case).filter(Case.id == case_id).first()
            if not case:
                raise ValueError(f"Case with ID {case_id} not found")
                
            # Query all case steps from the database
            case_steps = (
                db.query(CaseStep)
                .join(
                    PackageStepMasterDB,
                    CaseStep.package_step_master_id == PackageStepMasterDB.id,
                )
                .join(StepMaster, PackageStepMasterDB.step_id == StepMaster.id)
                .filter(CaseStep.case_id == case_id)
                .order_by(asc(StepMaster.order), asc(CaseStep.case_step_date))
                .options(
                    joinedload(CaseStep.case_step_package_step_master).joinedload(
                        PackageStepMasterDB.steps
                    )
                )
                .all()
            )
    
            # Initialize data structures
            steps_data = []
            treatment_steps = []
            
            # Find the dates for pre-auth, treatment, and discharge
            preauth_date = None
            treatment_date = None
            discharge_date = None
            
            # Extract dates from the steps based on their position
            for step in case_steps:
                step_order = step.case_step_package_step_master.steps.order
                if step_order == 2 and step.case_step_date:  # PreAuth
                    preauth_date = step.case_step_date
                elif step_order == 3 and step.case_step_date:  # Treatment
                    if not treatment_date or step.case_step_date < treatment_date:
                        treatment_date = step.case_step_date
                elif step_order == 4 and step.case_step_date:  # Discharge
                    discharge_date = step.case_step_date
            
            # Process each step to create response
            for step in case_steps:
                # Base query for step documents
                step_documents_query = db.query(CaseStepDocument).filter_by(
                    case_step_id=step.id
                )
    
                if user.is_patient:
                    # If user is a patient, filter documents uploaded by the patient and exclude others
                    step_documents_query = step_documents_query.filter(
                        or_(
                            CaseStepDocument.uploaded_by == user.id,
                            ~CaseStepDocument.id.in_(
                                db.query(CaseStepDocument.id)
                                .filter(CaseStepDocument.case_step_id == step.id)
                                .filter(CaseStepDocument.uploaded_by != user.id)
                            ),
                        )
                    )
    
                # Fetch the documents
                step_documents = step_documents_query.all()
    
                # Fetch document masters for all documents in one query
                document_ids = [doc.document_master_id for doc in step_documents]
                document_masters = {
                    dm.id: dm
                    for dm in db.query(DocumentMaster)
                    .filter(DocumentMaster.id.in_(document_ids))
                    .all()
                }
    
                # Prepare documents data
                documents_data = []
                required_doc_count = 0
                for document in step_documents:
                    document_master = document_masters.get(document.document_master_id)
                    if document_master:
                        documents_data.append(
                            {
                                "id": document.id,
                                "guid": document.guid,
                                "document_name": document_master.name,
                                "file_name": document.file_name,
                                "comment": document.comment,
                                "version": document.version,
                                "is_required": document_master.is_required,
                                "is_geotag_required": document_master.is_geotag_required,
                                "status": document.status_master,
                                "uploaded_by": document.users,
                            }
                        )
                        if document_master.is_required:
                            required_doc_count += 1
    
                # Sort documents by `is_required` flag
                documents_data.sort(key=lambda x: not x["is_required"])
    
                # Prepare step data
                step_data = {
                    "position": step.case_step_package_step_master.steps.order,
                    "id": step.id,
                    "name": step.case_step_package_step_master.steps.type,
                    "case_step_date": step.case_step_date,
                    "status": step.status_master,
                    "documents": documents_data,
                    "required_doc_count": required_doc_count,
                }
    
                # Add step to appropriate list
                if step.case_step_package_step_master.steps.order == 3:
                    treatment_steps.append(step_data)
                else:
                    steps_data.append(step_data)
    
            # Determine which treatment step to include
            selected_treatment_step = None
            future_treatment_step = None  # Track the first future treatment step
    
            for i, treatment_step in enumerate(treatment_steps):
                step_date = (
                    treatment_step["case_step_date"].date()
                    if treatment_step["case_step_date"]
                    else None
                )
                previous_treatment_date = (
                    treatment_steps[i - 1]["case_step_date"].date()
                    if i > 0 and treatment_steps[i - 1]["case_step_date"]
                    else None
                )
                next_treatment_date = (
                    treatment_steps[i + 1]["case_step_date"].date()
                    if i < len(treatment_steps) - 1
                    and treatment_steps[i + 1]["case_step_date"]
                    else None
                )
    
                treatment_step["previous_treatment_date"] = previous_treatment_date
                treatment_step["next_treatment_date"] = next_treatment_date
    
                # Determine if the treatment step should be shown
                if step_date == current_date:
                    selected_treatment_step = treatment_step
                    break
                # If step_date is before current_date, keep track of it as a candidate
                elif step_date and step_date < current_date:
                    selected_treatment_step = treatment_step
                # If step_date is after current_date, track the first future step
                elif step_date and step_date > current_date and not future_treatment_step:
                    future_treatment_step = treatment_step
    
            # If no current or previous treatment step found, fallback to the future step
            if not selected_treatment_step and future_treatment_step:
                selected_treatment_step = future_treatment_step
    
            # Include only the selected treatment step if found, maintaining the order by position
            if selected_treatment_step:
                # Insert at the correct position based on 'position' value
                inserted = False
                for index, step in enumerate(steps_data):
                    if step["position"] > selected_treatment_step["position"]:
                        steps_data.insert(index, selected_treatment_step)
                        inserted = True
                        break
                if not inserted:
                    steps_data.append(selected_treatment_step)
    
            # Get the patient, hospital, scheme, and procedure information
            patient_name = None
            hospital_short_name = None
            scheme_code = None
            procedure_code = None
            
            # Import required models
            from app.patients.models.patient import PatientDB
            from app.master.models.hospital import HospitalDB
            from app.master.models.scheme_type import SchemeType
            
            # Query for patient information
            if case.patient_id:
                patient = db.query(PatientDB).filter(PatientDB.id == case.patient_id).first()
                if patient:
                    patient_name = f"{patient.first_name} {patient.last_name}".strip()
                    if hasattr(patient, 'middle_name') and patient.middle_name:
                        patient_name = f"{patient.first_name} {patient.middle_name} {patient.last_name}".strip()
            
            # Query for hospital information
            if case.hospital_id:
                hospital = db.query(HospitalDB).filter(HospitalDB.id == case.hospital_id).first()
                if hospital:
                    hospital_short_name = hospital.short_name
            
            # Query for package information (which contains procedure)
            if case.package_master_id:
                # Use the relationship instead of making another query
                package = case.package
                if package:
                    procedure_code = package.procedure_code  # Changed from code to procedure_code
                    
                    # Get scheme information from the package category
                    if hasattr(package, 'categories') and package.categories:
                        if hasattr(package.categories, 'scheme_type') and package.categories.scheme_type:
                            scheme_code = package.categories.scheme_type.type
                    
                    # If there's a direct scheme_id on package (which doesn't seem to exist in your model)
                    # This is a fallback in case the relationship path above doesn't work
                    if not scheme_code:
                        # Try to get scheme through another path if the first approach didn't work
                        category = package.categories
                        if category and hasattr(category, 'scheme_id'):
                            scheme = db.query(SchemeType).filter(SchemeType.id == category.scheme_id).first()
                            if scheme:
                                scheme_code = package.categories.scheme_type.type            
            # # Add the additional data to the response
            # response_data = {
            #     "steps": steps_data,
            #     "case_info": {
            #         "patient_name": patient_name,
            #         "hospital_short_name": hospital_short_name,
            #         "scheme_code": scheme_code,
            #         "procedure_code": procedure_code,
            #         "preauth_date": preauth_date.strftime('%Y-%m-%d') if preauth_date else None,
            #         "treatment_date": treatment_date.strftime('%Y-%m-%d') if treatment_date else None,
            #         "discharge_date": discharge_date.strftime('%Y-%m-%d') if discharge_date else None,
            #     }
            # }

            # return response_data
            return steps_data
        except Exception as e:
            self.logger.error(f"Error retrieving case steps: {str(e)}")
            raise




    def create_case_step(
        self,
        db: Session,
        step_data,
    ):
        """Create a new case step ."""

        try:
            # Create a new case step  instance and add it to the database
            step = self.create(db, CaseStep, **step_data)
            return step
        except Exception as e:
            self.logger.error(e)
            raise

    def update_case_step(self, db: Session, case_step_id: int, step_data: dict):
        """
        Update a case step.

        Args:
            db (Session): The database session.
            case_step_id (int): The ID of the case step to update.
            step_data (dict): The updated data for the case step.

        Returns:
            Tuple[bool, Union[CaseStep, Exception]]: A tuple containing a boolean indicating
            whether the update was successful, and the updated CaseStep object or an Exception
            if the update failed.
        """
        try:
            # Check if the case step exists
            case_step = self.get_case_step_or_raise(db=db, case_step_id=case_step_id)
            # Create a dictionary of the updated step data, excluding any None values
            step_data_dict = {
                key: value for key, value in step_data.items() if value is not None
            }
            # Add the current date as the case status date
            step_data_dict["step_status_date"] = datetime.now()

            # Update the case step
            step = self.update(db, CaseStep, case_step_id, **step_data_dict)

            # Remove the case status date from the step data dictionary
            step_data_dict.pop("step_status_date")

            # Update the associated case
            # case = self.update(db, Case, step.case_id, **step_data_dict)
            # Create an audit trail entry for the case step update
            audit_trail_data = {
                "case_id": case_step.case.id,
                "action": "Case Step Updated",
                "case_step_id": case_step_id,
                "case_step_document_id": None,
                "data": step.status_master.name,
            }

            # Create the case audit trail entry
            CaseAuditTrailService().create_case_audit_trail(
                db=db,
                audit_trail=audit_trail_data,
                user_id=step_data_dict["updated_by"],
            )

            # Return the updated case step
            return step

        except Exception as e:
            # Log the exception and re-raise it
            self.logger.error(e)
            raise




    def close_notification(self, db, prev_notification_data):
        from app.notification.models.notification import NotificationDB
        from app.notification.services.notification import NotificationService
        from app.cases.helpers.constants import NotificationStatus
    
        case_logger.info(f"Attempting to close notification with data: {prev_notification_data}")
        
        notification_service = NotificationService()
        
        try:
            previous_notification = notification_service.get_notification_by_attributes(
                db=db, **prev_notification_data
            )
            if previous_notification:
                case_logger.info(f"Found previous notification with ID: {previous_notification.id}")
                update_payload = {"notification_status": NotificationStatus.CLOSED}
                instance = (
                    db.query(NotificationDB)
                    .filter(NotificationDB.id == previous_notification.id)
                    .first()
                )
                if instance:
                    for attr, value in update_payload.items():
                        setattr(instance, attr, value)
                    db.flush()
                    db.refresh(instance)
                    case_logger.info(f"Notification with ID {previous_notification.id} closed successfully")
                else:
                    case_logger.warning(f"No instance found for notification ID: {previous_notification.id}")
            else:
                case_logger.info("No previous notification found to close")
        except Exception as e:
            case_logger.error(f"Error closing notification: {str(e)}", exc_info=True)
            raise
    
    def create_notification(self, db, notification_data):
        from app.notification.services.notification import NotificationService
        from app.cases.helpers.constants import NotificationStatus
    
        case_logger.info(f"Attempting to create notification with data: {notification_data}")
        
        notification_service = NotificationService()
        
        try:
            existing_notification = notification_service.get_notification_by_attributes(
                db=db,
                case_id=notification_data["case_id"],
                notification_info=notification_data["notification_info"],
                notification_status=NotificationStatus.OPEN,
            )
            if existing_notification:
                case_logger.info(f"Notification already exists with ID: {existing_notification.id}. Closing the existing notification...")
                self.close_notification(db, notification_data)
                notification_service.create_notification_automic(
                    notification_data=notification_data, db=db
                )
                case_logger.info(f"New notification created successfully after closing the existing one for case ID: {notification_data['case_id']}")
            else:
                notification_service.create_notification_automic(
                    notification_data=notification_data, db=db
                )
                case_logger.info(f"Notification created successfully for case ID: {notification_data['case_id']}")
        except Exception as e:
            case_logger.error(f"Error creating notification for case ID {notification_data['case_id']}: {str(e)}", exc_info=True)
            raise
    def generate_notification_payload(self, case, case_step_id, current_status_name, status, role_id, notification_status):
        case_logger.info(f"Generating notification payload for case ID: {case.id}, case step ID: {case_step_id}, status: {current_status_name}")
        
        notification_data = {
            "case_id": case.id,
            "case_step_id": case_step_id,
            "notification_info": current_status_name,
            "notification_status": notification_status,
            "status_id": status,
            "role_id": role_id,
        }
        
        case_logger.debug(f"Generated notification payload: {notification_data}")
        return notification_data

    def handle_notifications(self, db, case, case_step):
        from app.cases.helpers.constants import PENDING_STATUSES, RoleIds, SUBMITTED_STATUSES

        case_logger.info(f"Handling notifications for case ID: {case.id}, case step ID: {case_step.id}")

        try:
            current_step_order = int(case_step.case_step_package_step_master.steps.order)
            current_step_submitted_status = SUBMITTED_STATUSES.get(current_step_order, None)
            current_pending_status = PENDING_STATUSES.get(current_step_order, None)
            current_submitted_status_id = self.get_status_id(db, current_step_submitted_status)
            current_pending_status_id = self.get_status_id(db, current_pending_status)

            if not self.has_mandatory_documents(db, case_step.id) and self.is_any_non_mandatory_document_submitted(db, case_step.id):
                case_logger.info("Non-mandatory document submitted")
                self.create_and_close_notifications(db, case, case_step, current_step_submitted_status, current_submitted_status_id, current_pending_status, current_pending_status_id, RoleIds.CLAIM, RoleIds.DATA_ENTRY_OPERATOR)

            elif self.are_all_mandatory_documents_approved(db, case_step.id):
                next_case_step = self.get_next_case_step_id(db, case_step.case_id, case_step.id)
                case_logger.info(f"Next case step: {next_case_step}")
                if next_case_step:
                    self.create_next_step_notification(db, next_case_step, case_step)

            elif self.are_all_required_documents_submitted(db, case_step.id):
                case_logger.info(f"All required documents submitted for case step ID: {case_step.id}")
                self.create_and_close_notifications(db, case, case_step, current_step_submitted_status, current_submitted_status_id, current_pending_status, current_pending_status_id, RoleIds.CLAIM, RoleIds.DATA_ENTRY_OPERATOR)

            elif self.is_any_document_pending_or_rejected(db, case_step.id):
                case_logger.info("Document pending or rejected")
                self.create_and_close_notifications(db, case, case_step, current_pending_status, current_pending_status_id, current_pending_status, current_pending_status_id, RoleIds.DATA_ENTRY_OPERATOR, RoleIds.DATA_ENTRY_OPERATOR)

            else:
                case_logger.info("No condition met")
        except Exception as e:
            case_logger.error(f"Error handling notifications for case ID {case.id}, case step ID {case_step.id}: {str(e)}", exc_info=True)
            raise

    def create_and_close_notifications(self, db, case, case_step, current_status, current_status_id, old_status, old_status_id, current_role_id, old_role_id):
        from app.cases.helpers.constants import NotificationStatus
    
        case_logger.info(f"Creating new notification for case ID: {case.id}, case step ID: {case_step.id}, status: {current_status}")
        
        try:
            new_notification_payload = self.generate_notification_payload(case, case_step.id, current_status, current_status_id, current_role_id, NotificationStatus.OPEN)
            case_logger.debug(f"New notification payload: {new_notification_payload}")
            self.create_notification(db, new_notification_payload)
            
            old_notification_payload = self.generate_notification_payload(case, case_step.id, old_status, old_status_id, old_role_id, NotificationStatus.OPEN)
            case_logger.debug(f"Old notification payload: {old_notification_payload}")
            self.close_notification(db, old_notification_payload)
            
            case_logger.info(f"Successfully created and closed notifications for case ID: {case.id}, case step ID: {case_step.id}")
        except Exception as e:
            case_logger.error(f"Error creating and closing notifications for case ID {case.id}, case step ID {case_step.id}: {str(e)}", exc_info=True)
            raise

    def get_status_id(self, db: Session, status_name: str, key: str = None) -> int:
        from app.cases.services.case import StatusMasterService

        case_logger.info(
            f"Getting status ID for status name: {status_name} and key: {key}"
        )
        if not key:
            return StatusMasterService().get_status_id(db, status_name)
        return StatusMasterService().get_status_id(db, status_name, key)

    def create_next_step_notification(self, db: Session, next_case_step,case_step):
        from app.cases.helpers.constants import PENDING_STATUSES, RoleIds
        from app.cases.helpers.constants import NotificationStatus
        next_step_order = int(next_case_step.case_step_package_step_master.steps.order)
        case_logger.info(
            f"Next step order: {next_step_order} for case step ID: {case_step.id}"
        )
        next_pending_status = PENDING_STATUSES.get(next_step_order, None)
        case_logger.info(f"Next pending status: {next_pending_status}")
        if next_pending_status:
            next_pending_status_id = self.get_status_id(db, next_pending_status)
            case_logger.info(
                f"Status ID for '{next_pending_status}': {next_pending_status_id}"
            )
            new_notification_data = {
                "case_id": next_case_step.case_id,
                "case_step_id": next_case_step.id,
                "notification_info": next_pending_status,
                "notification_status": NotificationStatus.OPEN,
                "status_id": next_pending_status_id,
                "role_id": RoleIds.DATA_ENTRY_OPERATOR,
            }
            notification_service = NotificationService()
            case_logger.info(f"New notification data: {new_notification_data}")
            existing_notification = notification_service.get_notification_by_attributes(
                db=db, **new_notification_data
            )
            case_logger.info(f"Existing notification: {existing_notification}")
            if not existing_notification:
                    case_logger.info(
                        f"All mandatory documents approved for case step ID: {case_step.id}."
                    )
                    case_logger.info(
                        f"Creating new notification for case step ID {case_step.id} & status '{next_pending_status}'."
                    )
                    notification_service.create_notification(
                        notification_data=new_notification_data, db=db
                    )
                    case_logger.info(
                        f"New notification created for case step ID {case_step.id}. Data: {new_notification_data}"
                    )
            else:
                case_logger.info(
                    f"Updating existing notification for case step ID {case_step.id} & status '{next_pending_status}'."
                )

                self.close_notification(db, new_notification_data)
                case_logger.info(
                    f"Closed existing notification for case step ID {case_step.id} & status '{next_pending_status}'."
                )
                notification_service.create_notification_automic(
                    notification_data=new_notification_data, db=db
                )
                case_logger.info(
                    f"New notification created for case step ID {case_step.id}. Data: {new_notification_data}"
                )

    def get_next_case_step_id(
        self, db: Session, case_id: int, current_case_step_id: int
    ) -> int:
        from app.cases.models.case_step import CaseStep
        from app.master.models.package import PackageStepMasterDB, StepMaster

        # Get the current case step
        current_case_step = (
            db.query(CaseStep)
            .filter(CaseStep.id == current_case_step_id, CaseStep.case_id == case_id)
            .first()
        )
        case_logger.debug(f"Current case step: {current_case_step}")
        if not current_case_step:
            case_logger.error(
                f"Case step with ID {current_case_step_id} for case ID {case_id} not found"
            )
            return None

        # Get the current step order
        current_step_order = (
            db.query(StepMaster.order)
            .join(PackageStepMasterDB, StepMaster.id == PackageStepMasterDB.step_id)
            .filter(PackageStepMasterDB.id == current_case_step.package_step_master_id)
            .scalar()
        )
        case_logger.debug(f"Current step order: {current_step_order}")

        # Get the next step order
        next_step_order = (
            db.query(StepMaster.order)
            .filter(StepMaster.order > current_step_order)
            .order_by(StepMaster.order.asc())
            .first()
        )
        case_logger.debug(f"Next step order: {next_step_order}")
        if not next_step_order:
            case_logger.error(
                f"No next step found for case ID {case_id} and current case step ID {current_case_step_id}"
            )
            return None

        # Get the next case step
        next_case_step = (
            db.query(CaseStep)
            .join(
                PackageStepMasterDB,
                CaseStep.package_step_master_id == PackageStepMasterDB.id,
            )
            .join(StepMaster, PackageStepMasterDB.step_id == StepMaster.id)
            .filter(
                CaseStep.case_id == case_id,
                StepMaster.order == next_step_order.order,
            )
            .first()
        )
        case_logger.debug(f"Next case step: {next_case_step}")
        if not next_case_step:
            case_logger.error(
                f"No next case step found for case ID {case_id} and current case step ID {current_case_step_id}"
            )
            return None
        case_logger.debug(
            f"Next case step date : {next_case_step.case_step_date.date()} and current date : {datetime.now().date()}"
        )
        if next_case_step.case_step_date.date() <= datetime.now().date():
            case_logger.debug(
                f"Next case step date for case ID {case_id} and current case step ID {current_case_step_id} is in the past"
            )
            return next_case_step
        else:
            case_logger.error(
                f"Next case step date for case ID {case_id} and current case step ID {current_case_step_id} is in the future"
            )
            return None
        
    def submit_case_step(self, db: Session, case_step_id: int, step_data_dict: dict):
        """
        Submit a case step.
    
        Args:
            db (Session): The database session.
            case_step_id (int): The ID of the case step to update.
            step_data (dict): The updated data for the case step.
    
        Returns:
            Tuple[bool, Union[CaseStep, Exception]]: A tuple containing a boolean indicating
            whether the update was successful, and the updated CaseStep object or an Exception
            if the update failed.
        """
        try:
            with db.begin():
                # Check if the case step exists
                case_step = self.get_case_step_or_raise(case_step_id=case_step_id, db=db)
                case_logger.info(f"Case step found: {case_step}")
    
                # Set the current date as the case step status date
                step_data_dict["step_status_date"] = datetime.now()
                case_logger.debug(f"Step status date set to: {step_data_dict['step_status_date']}")
    
                from app.cases.services.case import StatusMasterService
    
                # Determine current step order and next step order
                step_order = int(case_step.case_step_package_step_master.steps.order)
                case_logger.info(f"Case step order: {step_order}")
                status_name_mapping = {
                    1: ("Pre-Investigation Submitted", "PreAuth Pending", "Pre-Investigation Pending"),
                    2: ("PreAuth Submitted", "Treatment document Pending", "PreAuth Pending"),
                    3: ("Treatment document Submitted", "Discharge document Pending", "Treatment document Pending"),
                    4: ("Discharge document Submitted", "Post-Investigation document Pending", "Discharge document Pending"),
                    5: ("Post-Investigation document Submitted", "Post-Investigation document Submitted", "Post-Investigation document Pending"),
                }
    
                # Set the current step status
                current_status_name, next_status_name, prev_status_name = status_name_mapping.get(step_order, (None, None, None))
                case_logger.info(f"Current status name: {current_status_name}, Next status name: {next_status_name}, Previous status name: {prev_status_name}")
    
                if current_status_name:
                    status = StatusMasterService().get_status_id(db=db, status_name=current_status_name)
                    step_data_dict["status"] = status
                    case_logger.debug(f"Status set to: {status}")
    
                updated_case_step = db.query(CaseStep).filter(CaseStep.id == case_step_id).first()
                for attr, value in step_data_dict.items():
                    setattr(updated_case_step, attr, value)
                db.flush()
                db.refresh(updated_case_step)
                case_logger.info(f"Updated case step: {updated_case_step}")
    
                # Update the associated case with the next step status
                if next_status_name:
                    next_status = StatusMasterService().get_status_id(db=db, status_name=next_status_name)
                    case_payload = {
                        "step_status": next_status,
                        "updated_by": step_data_dict["updated_by"],
                    }
                    case = db.query(Case).filter(Case.id == updated_case_step.case_id).first()
                    for attr, value in case_payload.items():
                        setattr(case, attr, value)
                    db.flush()
                    db.refresh(case)
                    case_logger.info(f"Updated case: {case}")
    
                # Create an audit trail entry for the case step update
                audit_trail_data = {
                    "case_id": case_step.case.id,
                    "action": "Case Step Updated",
                    "case_step_id": case_step_id,
                    "case_step_document_id": None,
                    "data": updated_case_step.status_master.name,
                }
                CaseAuditTrailService().create_case_audit_trail_by_transaction(db=db, audit_trail=audit_trail_data, user_id=step_data_dict["updated_by"])
                case_logger.info(f"Audit trail created: {audit_trail_data}")
    
                # Handle notifications
                self.handle_notifications(db=db, case=case, case_step=case_step)
    
                # Return the updated case step
                return True, updated_case_step
    
        except Exception as e:
            case_logger.error(f"Error submitting case step ID {case_step_id}: {str(e)}", exc_info=True)
            raise
    def get_documents(self, db, case_step_id, is_required=None, status=None):
        case_logger.info(f"Fetching documents for case step ID: {case_step_id}, is_required: {is_required}, status: {status}")

        try:
            # Subquery to get the latest version of each document
            latest_versions = (
                db.query(
                    CaseStepDocument.guid,
                    func.max(CaseStepDocument.version).label("latest_version")
                )
                .filter(CaseStepDocument.case_step_id == case_step_id)
                .group_by(CaseStepDocument.guid)
                .subquery()
            )
            case_logger.debug(f"Latest versions subquery: {latest_versions}")

            # Main query to get the documents with the latest version
            query = (
                db.query(CaseStepDocument)
                .join(
                    latest_versions,
                    (CaseStepDocument.guid == latest_versions.c.guid) &
                    (CaseStepDocument.version == latest_versions.c.latest_version)
                )
                .filter(CaseStepDocument.case_step_id == case_step_id)
            )
            case_logger.debug(f"Main query: {query}")

            if is_required is not None:
                query = query.filter(CaseStepDocument.is_required == is_required)
                case_logger.debug(f"Filtered by is_required: {is_required}")

            if status is not None:
                if isinstance(status, list):
                    query = query.filter(CaseStepDocument.status.in_(status))
                    case_logger.debug(f"Filtered by status list: {status}")
                else:
                    query = query.filter(CaseStepDocument.status == status)
                    case_logger.debug(f"Filtered by status: {status}")

            documents = query.all()
            case_logger.info(f"Fetched {len(documents)} documents for case step ID: {case_step_id}")
            return documents
        except Exception as e:
            case_logger.error(f"Error fetching documents for case step ID {case_step_id}: {str(e)}", exc_info=True)
            raise

    def has_mandatory_documents(self, db, case_step_id):
        case_logger.info(f"Checking if there are mandatory documents for case step ID: {case_step_id}")
        
        try:
            mandatory_documents = self.get_documents(db, case_step_id, is_required=True)
            if not mandatory_documents:
                case_logger.info(f"No mandatory documents for case step ID: {case_step_id}.")
                return False
            case_logger.debug(f"Mandatory documents count for case step ID {case_step_id}: {len(mandatory_documents)}")
            return True
        except Exception as e:
            case_logger.error(f"Error checking mandatory documents for case step ID {case_step_id}: {str(e)}", exc_info=True)
            raise

    def is_any_non_mandatory_document_submitted(self, db, case_step_id):
        from app.cases.models.case import StatusMaster
        
        case_logger.info(f"Checking if any non-mandatory document is submitted for case step ID: {case_step_id}")
        
        try:
            submitted_status_id = (
                db.query(StatusMaster.id)
                .filter(
                    StatusMaster.key == "case_step_document",
                    StatusMaster.name == "Submitted",
                )
                .scalar()
            )
            case_logger.debug(f"Submitted status ID: {submitted_status_id}")
            
            non_mandatory_documents = self.get_documents(db, case_step_id, is_required=False, status=submitted_status_id)
            if non_mandatory_documents:
                case_logger.info(f"Found non-mandatory documents submitted for case step ID: {case_step_id}")
                return True
            
            case_logger.info(f"No non-mandatory documents submitted for case step ID: {case_step_id}")
            return False
        except Exception as e:
            case_logger.error(f"Error checking non-mandatory documents for case step ID {case_step_id}: {str(e)}", exc_info=True)
            raise

    def are_all_mandatory_documents_approved(self, db, case_step_id):
        from app.cases.models.case import StatusMaster
        
        case_logger.info(f"Checking if all mandatory documents are approved for case step ID: {case_step_id}")
        
        try:
            approved_status_id = (
                db.query(StatusMaster.id)
                .filter(
                    StatusMaster.key == "case_step_document",
                    StatusMaster.name == "Approved",
                )
                .scalar()
            )
            case_logger.debug(f"Approved status ID: {approved_status_id}")
            
            mandatory_documents = self.get_documents(db, case_step_id, is_required=True)
            if not mandatory_documents:
                case_logger.info(f"No mandatory documents for case step ID: {case_step_id}.")
                return False
            case_logger.debug(f"Mandatory documents count for case step ID {case_step_id}: {len(mandatory_documents)}")
            
            for document in mandatory_documents:
                case_logger.debug(f"Checking document ID: {document.id}, status: {document.status}")
                if document.status != approved_status_id:
                    case_logger.info(f"Document ID {document.id} is not approved.")
                    return False
            case_logger.info(f"All mandatory documents are approved for case step ID: {case_step_id}")
            return True
        except Exception as e:
            case_logger.error(f"Error checking mandatory documents for case step ID {case_step_id}: {str(e)}", exc_info=True)
            raise

    def is_any_document_pending_or_rejected(self, db, case_step_id):
        from app.cases.models.case import StatusMaster
        
        case_logger.info(f"Checking if any document is in Pending or Rejected status for case step ID: {case_step_id}")
        
        try:
            pending_status_id = (
                db.query(StatusMaster.id)
                .filter(
                    StatusMaster.key == "case_step_document",
                    StatusMaster.name == "Pending",
                )
                .scalar()
            )
            rejected_status_id = (
                db.query(StatusMaster.id)
                .filter(
                    StatusMaster.key == "case_step_document",
                    StatusMaster.name == "Rejected",
                )
                .scalar()
            )
            case_logger.debug(f"Pending status ID: {pending_status_id}, Rejected status ID: {rejected_status_id}")
            documents = self.get_documents(db, case_step_id, status=[pending_status_id, rejected_status_id])
            if documents:
                case_logger.info(f"Found documents in Pending or Rejected status for case step ID: {case_step_id}")
                return True
            
            case_logger.info(f"No documents in Pending or Rejected status for case step ID: {case_step_id}")
            return False
        except Exception as e:
            case_logger.error(f"Error checking documents for case step ID {case_step_id}: {str(e)}", exc_info=True)
            raise
    def are_all_required_documents_submitted(self, db, case_step_id):
        from app.cases.models.case import StatusMaster
        
        case_logger.info(f"Checking if all required documents are submitted for case step ID: {case_step_id}")
        
        try:
            submitted_status_id = (
                db.query(StatusMaster.id)
                .filter(
                    StatusMaster.key == "case_step_document",
                    StatusMaster.name == "Submitted",
                )
                .scalar()
            )
            approved_status_id = (
                db.query(StatusMaster.id)
                .filter(
                    StatusMaster.key == "case_step_document",
                    StatusMaster.name == "Approved",
                )
                .scalar()
            )
            case_logger.debug(f"Submitted status ID: {submitted_status_id}, Approved status ID: {approved_status_id}")
            
            required_documents = self.get_documents(db, case_step_id, is_required=True)
            case_logger.debug(f"Required documents count for case step ID {case_step_id}: {len(required_documents)}")
            
            if not required_documents:
                case_logger.info(f"No required documents for case step ID: {case_step_id}.")
                return True
            
            for document in required_documents:
                case_logger.debug(f"Checking document ID: {document.id}, status: {document.status}")
                if document.status not in [submitted_status_id, approved_status_id]:
                    case_logger.info(f"Document ID {document.id} is not submitted or approved.")
                    return False
            
            case_logger.info(f"All required documents are submitted or approved for case step ID: {case_step_id}")
            return True
        except Exception as e:
            case_logger.error(f"Error checking required documents for case step ID {case_step_id}: {str(e)}", exc_info=True)
            raise
    def delete_case_step(self, db: Session, case_step_id: int):
        """Delete a case step ."""

        try:
            step = self.get_case_step_or_raise(db=db, case_step_id=case_step_id)
            is_deleted = self.delete(db, CaseStep, case_step_id)
            return is_deleted
        except Exception as e:
            self.logger.error(e)
            raise




    def get_stepwise_approved_documents(self, db: Session, case_id: int):
        """Retrieve stepwise approved documents grouped by case_step_date."""
        try:
            from .case import CaseService

            case = CaseService().get_case_or_raise(db=db, case_id=case_id)

            case_steps = (
                db.query(CaseStep)
                .join(
                    PackageStepMasterDB,
                    CaseStep.package_step_master_id == PackageStepMasterDB.id,
                )
                .join(StepMaster, PackageStepMasterDB.step_id == StepMaster.id)
                .filter(CaseStep.case_id == case_id)
                .order_by(asc(StepMaster.order))
                .options(
                    joinedload(CaseStep.case_step_package_step_master).joinedload(
                        PackageStepMasterDB.steps
                    )
                )
                .all()
            )

            approved_status = db.query(StatusMaster).filter_by(name="Approved").first()

            steps_data = []
            treatment_data = {
                "position": None,
                "id": None,
                "name": "On Treatment day",
                "documents": {},
            }

            for step in case_steps:
                step_documents = (
                    db.query(CaseStepDocument)
                    .filter(
                        CaseStepDocument.case_step_id == step.id,
                        CaseStepDocument.status == approved_status.id,
                    )
                    .all()
                )

                documents_data = []
                for document in step_documents:
                    document_master = (
                        db.query(DocumentMaster)
                        .filter_by(id=document.document_master_id)
                        .first()
                    )
                    file_data = self.get_document_version(document.guid)
                    filename = None
                    content_type = None
                    if file_data:
                        filename = file_data["versions"][0]["file_name"]
                        content_type = file_data["versions"][0]["content_type"]
                    documents_data.append(
                        {
                            "id": document.id,
                            "guid": document.guid,
                            "document_name": document_master.name,
                            "is_required": document.is_required,
                            "status": {
                                "id": document.status_master.id,
                                "name": document.status_master.name,
                                "key": document.status_master.key,
                            },
                            "filename": filename,
                            "content_type": content_type,
                        }
                    )

                if (
                    step.case_step_package_step_master.steps.order == 3
                ):  # Assuming '3' is the order for treatment steps
                    if step.case_step_date.date() <= datetime.now().date():
                        treatment_date = step.case_step_date.strftime("%d-%m-%Y")
                        if treatment_data["position"] is None:
                            treatment_data["position"] = (
                                step.case_step_package_step_master.steps.order
                            )
                            treatment_data["id"] = step.id
                        if treatment_date in treatment_data["documents"]:
                            treatment_data["documents"][treatment_date].extend(
                                documents_data
                            )
                        else:
                            treatment_data["documents"][treatment_date] = documents_data
                else:
                    steps_data.append(
                        {
                            "position": step.case_step_package_step_master.steps.order,
                            "id": step.id,
                            "name": step.case_step_package_step_master.steps.type,
                            "case_step_date": (
                                step.case_step_date.strftime("%Y-%m-%d")
                                if step.case_step_date
                                else None
                            ),
                            "documents": documents_data,
                        }
                    )

            # Append treatment data to steps_data only if there are treatment documents
            if treatment_data["documents"]:
                steps_data.append(treatment_data)

            # Sort steps_data by position
            steps_data.sort(key=lambda x: x["position"])

            return steps_data
        except Exception as e:
            self.logger.error(e)
            raise

    def get_document_version(self, guid):
        try:
            import os

            import requests

            DOCUMENT_COMPONENT_URL = os.getenv("DOCUMENT_COMPONENT_URL")
            headers = {"Tenant-Identifier": os.getenv("TENANT_IDENTIFIER")}
            params = {"guid": guid, "requested_by": "admin"}
            # Get the document version from the API
            doc_version_response = requests.get(
                f"{DOCUMENT_COMPONENT_URL}/document/get", params=params, headers=headers
            )
            if doc_version_response.status_code == 200:
                data = doc_version_response.json()["data"]
                return data
            return None
        except Exception as e:
            self.logger.error(e)
            raise
