import os
from datetime import datetime
from typing import List
from app.cases.helpers.constants import NotificationStatus
from sqlalchemy import func

import pytz
from app.cases.exception.custom.case_step_document import CaseStepDocumentExceptions
from app.cases.models import CaseStepDocument
from app.cases.models.case import Case
from app.cases.models.case_step import CaseStep
from app.cases.services.case import case_logger
from app.cases.services.case_audit_trail import CaseAuditTrailService
from app.cases.services.case_step_master import CaseStepService
from app.cases.validations.case_step_master import (
    CaseStepDocumentApprove,
    CaseStepDocumentReject,
)
from app.database.database import SessionLocal
from app.master.models.document_master import DocumentMaster
from app.notification.models.notification import NotificationDB, NotificationMasterDB
from app.notification.query.notifications import (
    CREATE_PENDING_NOTIFICATIONS,
    FETCH_CASE_STEPS,
    FETCH_ROLE,
    FETCH_STATUS_NAME,
    GET_CASE_STEP_PENDING_TREATMENT_AND_DISCHARGE,
)
from app.notification.services.notification import NotificationService
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.schedulers.blocking import BlockingScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from sqlalchemy import text
from sqlalchemy.orm import Session
from utils.db import BaseService
from utils.exception import NotFoundException
from utils.logger import setup_logger


class CaseStepDocumentService(BaseService):
    """
    Service class for managing cassestep master-related operations.
    """

    def __init__(self) -> None:
        self.db = SessionLocal()
        self.logger = case_logger

    def get_case_step_doc_by_id(self, case_step_doc_id: int, db: Session):
        """Retrieve a case step document by ID."""
        try:
            case_step_doc = self.get_by_attribute(
                db, CaseStepDocument, CaseStepDocument.id, case_step_doc_id
            )
            if not case_step_doc:
                CaseStepDocumentExceptions().raise_case_step_document_not_found_exception(
                    case_step_doc_id
                )
            return True, case_step_doc
        except Exception as e:
            self.logger.error(e)
            raise

    def get_case_step_document_by_id(self, id: int, db: Session):
        """Retrieve a case step document by ID."""
        try:
            is_exist, case_step_doc = self.get_case_step_doc_by_id(id, db=db)
            if is_exist:
                document_master = (
                    db.query(DocumentMaster)
                    .filter_by(id=case_step_doc.document_master_id)
                    .first()
                )
                data = {
                    "id": case_step_doc.id,
                    "guid": case_step_doc.guid,
                    "document_name": document_master.name,
                    "file_name": case_step_doc.file_name,
                    "version": case_step_doc.version,
                    "comment": case_step_doc.comment,
                    "is_required": document_master.is_required,
                    "is_geotag_required": document_master.is_geotag_required,
                    "status": case_step_doc.status_master,
                }
                return data
        except Exception as e:
            self.logger.error(e)
            raise

    def bulk_create_case_step_documents(
        self,
        db: Session,
        document_master_ids: List[int],
        document_type_master_ids: List[int],
        case_step_id: int,
    ):
        """Bulk create CaseStep instances."""

        case_step = CaseStepService().get_case_step_or_raise(db, case_step_id)
        case_step_document_data = [
            {
                "case_step_id": case_step_id,
                "document_master_id": document_master_id,
                "document_type_master_id": document_type_master_id,
            }
            for document_master_id, document_type_master_id in (
                document_master_ids,
                document_type_master_ids,
            )
        ]
        self.bulk_create(db, CaseStepDocument, case_step_document_data)

    def create_case_step_document(self, db: Session, case_step_doc_data):
        """Create a new case step document."""
        try:
            # Create a new case step document instance and add it to the database
            case_step_doc = self.create(db, CaseStepDocument, **case_step_doc_data)
            return case_step_doc
        except Exception as e:
            self.logger.error(e)
            raise

    def update_case_step_document(
        self, db: Session, case_step_doc_id: int, doc_step_data: dict
    ):
        """Update a case step ."""

        try:
            from app.cases.services.case import StatusMasterService

            is_exist, case_step_doc = self.get_case_step_doc_by_id(
                case_step_doc_id, db=db
            )
            doc_step_data_dict = {
                key: value for key, value in doc_step_data.items() if value is not None
            }
            doc_step_data_dict["updated_at"] = datetime.now()
            if "status" in doc_step_data_dict:
                status_id = StatusMasterService().get_status_id(
                    db, "Pending", "case_step_document"
                )
                if (
                    doc_step_data_dict["status"] is not None
                    and doc_step_data_dict["status"] == status_id
                ):
                    doc_step_data_dict["file_name"] = None
                    doc_step_data_dict["comment"] = None
                    doc_step_data_dict["uploaded_by"] = None

            step = self.update(
                db, CaseStepDocument, case_step_doc_id, **doc_step_data_dict
            )
            # Create an audit trail entry for the case step update
            audit_trail_data = {
                "case_id": case_step_doc.case_step.case_id,
                "action": "Step Document Updated",
                "case_step_id": case_step_doc.case_step.id,
                "case_step_document_id": case_step_doc_id,
            }
            if "status" in doc_step_data_dict and "guid" in doc_step_data_dict:
                audit_trail_data["data"] = (
                    f"{step.document_master.name} {step.status_master.name}\n,guid:{doc_step_data_dict['guid']}"
                )
            else:
                audit_trail_data["data"] = (
                    f"{step.document_master.name} {step.status_master.name}"
                )

            # Create the case audit trail entry
            CaseAuditTrailService().create_case_audit_trail(
                db=db,
                audit_trail=audit_trail_data,
                user_id=doc_step_data_dict["updated_by"],
            )

            data = self.get_case_step_document_by_id(case_step_doc_id, db=db)
            return True, data

        except Exception as e:
            self.logger.error(e)
            raise

    def submit_case_step_document(
        self, db: Session, case_step_document_id: int, doc_step_data: dict
    ):
        """Submit a case step document by ID."""

        try:
            from app.cases.services.case import StatusMasterService

            is_exist, case_step_doc = self.get_case_step_doc_by_id(
                case_step_document_id, db=db
            )
            if is_exist:
                status_id = StatusMasterService().get_status_id(
                    db, "Submitted", "case_step_document"
                )
                doc_step_data_dict = {
                    key: value
                    for key, value in doc_step_data.items()
                    if value is not None
                }
                doc_step_data_dict["updated_at"] = datetime.now()
                doc_step_data_dict["status"] = status_id
                step = self.update(
                    db, CaseStepDocument, case_step_document_id, **doc_step_data_dict
                )

                # Create an audit trail entry for the case step document approval
                audit_trail_data = {
                    "case_id": case_step_doc.case_step.case_id,
                    "action": "Step Document Submitted",
                    "case_step_id": case_step_doc.case_step.id,
                    "case_step_document_id": case_step_document_id,
                }
                CaseAuditTrailService().create_case_audit_trail(
                    db=db,
                    audit_trail=audit_trail_data,
                    user_id=doc_step_data_dict["updated_by"],
                )

                data = self.get_case_step_document_by_id(case_step_document_id, db=db)
                return data
            else:
                raise NotFoundException(
                    f"Case step document with ID {case_step_document_id} not found"
                )

        except Exception as e:
            self.logger.error(e)
            raise

    # def approve_case_step_document(
    #         self,
    #         db: Session,
    #         case_step_doc_id: int,
    #         user: object,
    #         data: CaseStepDocumentApprove,
    #     ):
    #         """Approve a case step document."""
    #         try:
    #             from app.cases.services.case import StatusMasterService

    #             case_logger.info(f"Approving case step document ID: {case_step_doc_id} by user ID: {user.id}")

    #             is_exist, case_step_doc = self.get_case_step_doc_by_id(
    #                 case_step_doc_id, db=db
    #             )
    #             case_logger.debug(f"Case step document exists: {is_exist}, case_step_doc: {case_step_doc}")

    #             status_id = StatusMasterService().get_status_id(
    #                 db, "Approved", "case_step_document"
    #             )
    #             case_logger.debug(f"Status ID for 'Approved': {status_id}")

    #             # Update the case step document status
    #             doc_step_data_dict = {
    #                 "status": status_id,
    #                 "updated_by": user.id,
    #                 "updated_at": datetime.now(),
    #             }
    #             step_document = self.update(db, CaseStepDocument,
    #                                         case_step_doc_id, **doc_step_data_dict)
    #             case_logger.debug(f"Updated step document: {step_document}")

    #             status_name_mapping = {
    #                 1: ("Pre-Investigation Submitted", "Pre-Investigation Pending"),
    #                 2: ("PreAuth Submitted", "PreAuth Pending"),
    #                 3: ("Treatment document Submitted", "Treatment document Pending"),
    #                 4: ("Discharge document Submitted", "Discharge document Pending"),
    #                 5: (
    #                 "Post-Investigation document Submitted",
    #                 "Post-Investigation document Pending",
    #             ),
    #             }
    #             case_step_order = int(case_step_doc.case_step.case_step_package_step_master.steps.order)
    #             case_logger.debug(f"Case step order: {case_step_order}")

    #             prev_status_name, status_name = status_name_mapping.get(case_step_order, (None, None))
    #             case_logger.debug(f"Previous status name: {prev_status_name}, Current status name: {status_name}")

    #             if status_name:
    #                 status_id = StatusMasterService().get_status_id(db=db, status_name=status_name)
    #                 case_logger.debug(f"Status ID for '{status_name}': {status_id}")

    #             if data.comment:
    #                 comment_data = {
    #                     "guid": step_document.guid,
    #                     "comment": data.comment,
    #                     "commented_by": f"{user.first_name} {user.last_name}",
    #                 }
    #                 case_logger.debug(f"Comment data: {comment_data}")

    #                 comment_response = self.submit_comment_to_api(comment_data, user)
    #                 case_logger.debug(f"Comment response status code: {comment_response.status_code}")

    #                 if comment_response.status_code != 201:
    #                     raise Exception(
    #                         f"Failed to submit comment: {comment_response.text}"
    #                     )
    #                 doc_step_data_dict = {
    #                     "comment": data.comment,
    #                     "updated_at": datetime.now(),
    #                 }
    #                 self.update(
    #                     db, CaseStepDocument, case_step_doc_id, **doc_step_data_dict
    #                 )
    #             else:
    #                 doc_step_data_dict = {"comment": None}
    #                 self.update(
    #                     db, CaseStepDocument, case_step_doc_id, **doc_step_data_dict
    #                 )

    #             audit_trail_data = {
    #                 "case_id": case_step_doc.case_step.case_id,
    #                 "action": "Step Document Approved",
    #                 "case_step_id": case_step_doc.case_step.id,
    #                 "case_step_document_id": case_step_doc_id,
    #             }
    #             case_logger.debug(f"Audit trail data: {audit_trail_data}")

    #             CaseAuditTrailService().create_case_audit_trail(
    #                 db=db, audit_trail=audit_trail_data, user_id=user.id)

    #             notification_service = NotificationService()
    #             notification_data = {
    #                 "case_id": case_step_doc.case_step.case_id,
    #                 "case_step_id": case_step_doc.case_step.id,
    #                 "notification_info": status_name,
    #                 "notification_status": "Open",
    #                 "status_id": status_id,
    #                 "role_id": db.query(NotificationMasterDB).filter(NotificationMasterDB.status_id == status_id).first().role_id
    #             }
    #             case_logger.debug(f"Notification data: {notification_data}")

    #             if prev_status_name:
    #                 prev_status = StatusMasterService().get_status_id(db=db, status_name=prev_status_name)
    #                 case_logger.debug(f"Previous status ID for '{prev_status_name}': {prev_status}")

    #                 prev_notification_data = {
    #                     "case_id": case_step_doc.case_step.case_id,
    #                     "case_step_id": case_step_doc.case_step.id,
    #                     "notification_info": prev_status_name,
    #                     "notification_status": "Open",
    #                     "status_id": prev_status,
    #                     "role_id": db.query(NotificationMasterDB).filter(NotificationMasterDB.status_id == prev_status).first().role_id
    #                 }
    #                 case_logger.debug(f"Previous notification data: {prev_notification_data}")

    #                 previous_notification = notification_service.get_notification_by_attributes(db=db, **prev_notification_data)
    #                 case_logger.debug(f"Previous notification: {previous_notification}")

    #                 if previous_notification:
    #                     notification_service.update_notification(
    #                         previous_notification.id,
    #                         notification_data={"notification_status": "Close"},
    #                         db=db,
    #                     )
    #                     case_logger.info(
    #                         f"Closed previous notification for status '{prev_status_name}'."
    #                     )
    #                 else:
    #                     case_logger.info(
    #                         f"No previous notification found for status '{prev_status_name}'."
    #                     )

    #             case_query = db.query(Case).filter_by(id=case_step_doc.case_step.case_id).first()
    #             case_logger.debug(f"Case query result: {case_query}")

    #             if not case_query:
    #                 raise NotFoundException(
    #                     f"Case with ID {case_step_doc.case_step.case_id} not found"
    #                 )

    #             data = self.get_case_step_document_by_id(case_step_doc_id, db=db)
    #             case_logger.info(f"Approved case step document ID: {case_step_doc_id} successfully.")
    #             return data

    #         except Exception as e:
    #             case_logger.error(f"Error approving case step document ID {case_step_doc_id}: {str(e)}", exc_info=True)
    #             raise

    def are_all_mandatory_documents_approved(self, db, case_step_id):
        from app.cases.models.case import StatusMaster

        case_logger.info(
            f"Checking if all mandatory documents are approved for case step ID: {case_step_id}"
        )

        approved_status_id = (
            db.query(StatusMaster.id)
            .filter(
                StatusMaster.key == "case_step_document",
                StatusMaster.name == "Approved",
            )
            .scalar()
        )
        case_logger.debug(f"Approved status ID: {approved_status_id}")

        mandatory_documents = (
            db.query(CaseStepDocument)
            .filter(
                CaseStepDocument.case_step_id == case_step_id,
                CaseStepDocument.is_required == True,
            )
            .all()
        )
        case_logger.debug(
            f"Mandatory documents count for case step ID {case_step_id}: {len(mandatory_documents)}"
        )

        for document in mandatory_documents:
            case_logger.debug(
                f"Checking document ID: {document.id}, status: {document.status}"
            )
            if document.status != approved_status_id:
                case_logger.info(f"Document ID {document.id} is not approved.")
                return False

        case_logger.info(
            f"All mandatory documents are approved for case step ID: {case_step_id}"
        )
        return True

    def has_required_documents(self, db, case_step_id):
        """
        Check if a case step has any required documents.
        
        Args:
            db (Session): The database session.
            case_step_id (int): The ID of the case step to check.
            
        Returns:
            bool: True if the step has any required documents, False otherwise.
        """
        case_logger.info(
            f"Checking if case step ID {case_step_id} has required documents"
        )
        
        required_documents_count = (
            db.query(func.count(CaseStepDocument.id))
            .filter(
                CaseStepDocument.case_step_id == case_step_id,
                CaseStepDocument.is_required == True,
            )
            .scalar()
        )
        
        case_logger.debug(
            f"Required documents count for case step ID {case_step_id}: {required_documents_count}"
        )
        
        return required_documents_count > 0

    # def approve_case_step_document(
    #         self,
    #         db: Session,
    #         case_step_doc_id: int,
    #         user: object,
    #         data: CaseStepDocumentApprove,
    #     ):
    #         """Approve a case step document."""
    #         try:
    #             from app.cases.services.case import StatusMasterService

    #             case_logger.info(
    #                 f"Approving case step document ID: {case_step_doc_id} by user ID: {user.id}"
    #             )

    #             is_exist, case_step_doc = self.get_case_step_doc_by_id(
    #                 case_step_doc_id, db=db
    #             )
    #             case_logger.debug(f"Case step document exists: {is_exist}, case_step_doc: {case_step_doc}")

    #             status_id = StatusMasterService().get_status_id(
    #                 db, "Approved", "case_step_document"
    #             )
    #             case_logger.debug(f"Status ID for 'Approved': {status_id}")

    #             # Update the case step document status
    #             doc_step_data_dict = {
    #                 "status": status_id,
    #                 "updated_by": user.id,
    #                 "updated_at": datetime.now(),
    #             }
    #             step_document = self.update(db, CaseStepDocument,
    #                                         case_step_doc_id, **doc_step_data_dict)
    #             case_logger.debug(f"Updated step document: {step_document}")

    #             status_name_mapping = {
    #                 1: ("Pre-Investigation Submitted", "Pre-Investigation Pending"),
    #                 2: ("PreAuth Submitted", "PreAuth Pending"),
    #                 3: ("Treatment document Submitted", "Treatment document Pending"),
    #                 4: ("Discharge document Submitted", "Discharge document Pending"),
    #                 5: (
    #                 "Post-Investigation document Submitted",
    #                 "Post-Investigation document Pending",
    #             ),
    #             }
    #             case_step_order = int(case_step_doc.case_step.case_step_package_step_master.steps.order)
    #             case_logger.debug(f"Case step order: {case_step_order}")

    #             prev_status_name, status_name = status_name_mapping.get(case_step_order, (None, None))
    #             case_logger.debug(f"Previous status name: {prev_status_name}, Current status name: {status_name}")

    #             if status_name:
    #                 status_id = StatusMasterService().get_status_id(db=db, status_name=status_name)
    #                 case_logger.debug(f"Status ID for '{status_name}': {status_id}")

    #             if data.comment:
    #                 comment_data = {
    #                     "guid": step_document.guid,
    #                     "comment": data.comment,
    #                     "commented_by": f"{user.first_name} {user.last_name}",
    #                 }
    #                 case_logger.debug(f"Comment data: {comment_data}")

    #                 comment_response = self.submit_comment_to_api(comment_data, user)
    #                 case_logger.debug(f"Comment response status code: {comment_response.status_code}")

    #                 if comment_response.status_code != 201:
    #                     raise Exception(
    #                         f"Failed to submit comment: {comment_response.text}"
    #                     )
    #                 doc_step_data_dict = {
    #                     "comment": data.comment,
    #                     "updated_at": datetime.now(),
    #                 }
    #                 self.update(
    #                     db, CaseStepDocument, case_step_doc_id, **doc_step_data_dict
    #                 )
    #             else:
    #                 doc_step_data_dict = {"comment": None}
    #                 self.update(
    #                     db, CaseStepDocument, case_step_doc_id, **doc_step_data_dict
    #                 )

    #             audit_trail_data = {
    #                 "case_id": case_step_doc.case_step.case_id,
    #                 "action": "Step Document Approved",
    #                 "case_step_id": case_step_doc.case_step.id,
    #                 "case_step_document_id": case_step_doc_id,
    #             }
    #             case_logger.debug(f"Audit trail data: {audit_trail_data}")

    #             CaseAuditTrailService().create_case_audit_trail(
    #                 db=db, audit_trail=audit_trail_data, user_id=user.id)

    #             notification_service = NotificationService()
    #             notification_data = {
    #                 "case_id": case_step_doc.case_step.case_id,
    #                 "case_step_id": case_step_doc.case_step.id,
    #                 "notification_info": status_name,
    #                 "notification_status": "Open",
    #                 "status_id": status_id,
    #                 "role_id": db.query(NotificationMasterDB).filter(NotificationMasterDB.status_id == status_id).first().role_id
    #             }
    #             case_logger.debug(f"Notification data: {notification_data}")

    #             if prev_status_name:
    #                 prev_status = StatusMasterService().get_status_id(db=db, status_name=prev_status_name)
    #                 case_logger.debug(f"Previous status ID for '{prev_status_name}': {prev_status}")

    #                 prev_notification_data = {
    #                     "case_id": case_step_doc.case_step.case_id,
    #                     "case_step_id": case_step_doc.case_step.id,
    #                     "notification_info": prev_status_name,
    #                     "notification_status": "Open",
    #                     "status_id": prev_status,
    #                     "role_id": db.query(NotificationMasterDB).filter(NotificationMasterDB.status_id == prev_status).first().role_id
    #                 }
    #                 case_logger.debug(f"Previous notification data: {prev_notification_data}")

    #                 previous_notification = notification_service.get_notification_by_attributes(db=db, **prev_notification_data)
    #                 case_logger.debug(f"Previous notification: {previous_notification}")

    #                 if previous_notification:
    #                     notification_service.update_notification(
    #                         previous_notification.id,
    #                         notification_data={"notification_status": "Close"},
    #                         db=db,
    #                     )
    #                     case_logger.info(
    #                         f"Closed previous notification for status '{prev_status_name}'."
    #                     )
    #                 else:
    #                     case_logger.info(
    #                         f"No previous notification found for status '{prev_status_name}'."
    #                     )

    #             case_query = db.query(Case).filter_by(id=case_step_doc.case_step.case_id).first()
    #             case_logger.debug(f"Case query result: {case_query}")

    #             if not case_query:
    #                 raise NotFoundException(
    #                     f"Case with ID {case_step_doc.case_step.case_id} not found"
    #                 )

    #             data = self.get_case_step_document_by_id(case_step_doc_id, db=db)
    #             case_logger.info(f"Approved case step document ID: {case_step_doc_id} successfully.")
    #             return data

    #         except Exception as e:
    #             case_logger.error(f"Error approving case step document ID {case_step_doc_id}: {str(e)}", exc_info=True)
    #             raise

    def approve_case_step_document(
        self,
        db: Session,
        case_step_doc_id: int,
        user: object,
        data: CaseStepDocumentApprove,
    ):
        """Approve a case step document."""
        try:
            case_logger.info(
                f"Approving case step document ID: {case_step_doc_id} by user ID: {user.id}"
            )

            is_exist, case_step_doc = self.get_case_step_doc_by_id(
                case_step_doc_id, db=db
            )
            if not is_exist:
                raise NotFoundException(
                    f"Case step document with ID {case_step_doc_id} not found"
                )

            status_id = self.get_status_id(db, "Approved", "case_step_document")
            self.update_case_step_document_status(
                db, case_step_doc_id, status_id, user.id
            )

            current_submitted_status, current_pending_status = self.get_status_names(case_step_doc)
            case_logger.info(
                f"Current submitted status: {current_submitted_status}, Current pending status: {current_pending_status}"
            )
            if current_pending_status:
                status_id = self.get_status_id(db, current_pending_status)
                case_logger.info(
                    f"Status ID for '{current_pending_status}': {status_id}"
                )
            if data.comment:
                case_logger.info(f"Comment: {data.comment}")
                self.handle_comment_submission(db, case_step_doc, data.comment, user)

            self.create_audit_trail(
                db, case_step_doc, case_step_doc_id, user.id, "Step Document Approved"
            )

            self.handle_notifications(
                db, case_step_doc, current_pending_status,current_submitted_status, status_id
            )

            case_query = (
                db.query(Case).filter_by(id=case_step_doc.case_step.case_id).first()
            )
            if not case_query:
                raise NotFoundException(
                    f"Case with ID {case_step_doc.case_step.case_id} not found"
                )

            data = self.get_case_step_document_by_id(case_step_doc_id, db=db)
            case_logger.info(
                f"Approved case step document ID: {case_step_doc_id} successfully."
            )
            return data

        except Exception as e:
            case_logger.error(
                f"Error approving case step document ID {case_step_doc_id}: {str(e)}",
                exc_info=True,
            )
            raise

    def get_status_id(self, db: Session, status_name: str, key: str = None) -> int:
        from app.cases.services.case import StatusMasterService

        case_logger.info(
            f"Getting status ID for status name: {status_name} and key: {key}"
        )
        if not key:
            return StatusMasterService().get_status_id(db, status_name)
        return StatusMasterService().get_status_id(db, status_name, key)

    def update_case_step_document_status(
        self, db: Session, case_step_doc_id: int, status_id: int, user_id: int
    ):
        doc_step_data_dict = {
            "status": status_id,
            "updated_by": user_id,
            "updated_at": datetime.now(),
        }
        self.update(db, CaseStepDocument, case_step_doc_id, **doc_step_data_dict)

    def get_status_names(self, case_step_doc) -> tuple:
        status_name_mapping = {
            1: ("Pre-Investigation Submitted", "Pre-Investigation Pending"),
            2: ("PreAuth Submitted", "PreAuth Pending"),
            3: ("Treatment document Submitted", "Treatment document Pending"),
            4: ("Discharge document Submitted", "Discharge document Pending"),
            5: (
                "Post-Investigation document Submitted",
                "Post-Investigation document Pending",
            ),
        }
        case_step_order = int(
            case_step_doc.case_step.case_step_package_step_master.steps.order
        )
        case_logger.info(
            f"Case step order: {case_step_order} for case step document ID: {case_step_doc.id}"
        )
        return status_name_mapping.get(case_step_order, (None, None))

    def handle_comment_submission(
        self, db: Session, case_step_doc, comment: str, user: object
    ):
        comment_data = {
            "guid": case_step_doc.guid,
            "comment": comment,
            "commented_by": f"{user.first_name} {user.last_name}",
        }
        case_logger.info(f"Comment data: {comment_data}")
        comment_response = self.submit_comment_to_api(comment_data, user)
        if comment_response.status_code != 201:
            case_logger.error(f"Failed to submit comment: {comment_response.text}")
            raise Exception(f"Failed to submit comment: {comment_response.text}")

        doc_step_data_dict = {
            "comment": comment,
            "updated_at": datetime.now(),
        }
        case_logger.info(f"Document step data dict: {doc_step_data_dict}")
        self.update(db, CaseStepDocument, case_step_doc.id, **doc_step_data_dict)

    def create_audit_trail(
        self,
        db: Session,
        case_step_doc,
        case_step_doc_id: int,
        user_id: int,
        action: str,
    ):
        audit_trail_data = {
            "case_id": case_step_doc.case_step.case_id,
            "action": action,
            "case_step_id": case_step_doc.case_step.id,
            "case_step_document_id": case_step_doc_id,
        }
        CaseAuditTrailService().create_case_audit_trail(
            db=db, audit_trail=audit_trail_data, user_id=user_id
        )

    def handle_notifications(
        self,
        db: Session,
        case_step_doc,
        current_pending_status: str,
        current_submitted_status: str,
        status_id: int,
    ):
        from app.cases.helpers.constants import NotificationStatus
        notification_service = NotificationService()
        case_logger.info(f"Current pending status: {current_pending_status}")
        case_logger.info(f"Current submitted status: {current_submitted_status}")
        if current_pending_status:
            prev_status = self.get_status_id(db, current_pending_status)
            case_logger.info(
                f"Status ID for '{current_pending_status}': {prev_status}"
            )
            prev_notification_data = {
                "case_id": case_step_doc.case_step.case_id,
                "case_step_id": case_step_doc.case_step.id,
                "notification_info": current_pending_status,
                "notification_status":NotificationStatus.OPEN,
                "status_id": prev_status,
                "role_id": db.query(NotificationMasterDB)
                .filter(NotificationMasterDB.status_id == prev_status)
                .first()
                .role_id,
            }
            case_logger.info(f"Previous notification data: {prev_notification_data}")
            previous_notification = notification_service.get_notification_by_attributes(
                db=db, **prev_notification_data
            )
            case_logger.info(f"Previous notification: {previous_notification}")
            if previous_notification:
                case_logger.info(
                    f"Updating previous notification for status '{current_pending_status}'."
                )
                if self.are_all_mandatory_documents_approved(
                    db, case_step_doc.case_step.id
                ) and not self.is_any_document_pending_or_rejected( db, case_step_doc.case_step.id):
                    case_logger.info(f"Closing previous notification for status '{current_pending_status}' & Notification ID: {previous_notification.id}")
                    notification_service.update_notification(
                        previous_notification.id,
                        notification_data={"notification_status": NotificationStatus.CLOSED},
                        db=db,
                    )
                    case_logger.info(
                        f"Closed previous notification for status '{current_pending_status} & Notification ID: {previous_notification.id}'."
                    )
        if current_submitted_status:
            current_status = self.get_status_id(db, current_submitted_status)
            case_logger.info(
                f"Status ID for '{current_submitted_status}': {current_status}"
            )
            prev_submitted_data = {
                "case_id": case_step_doc.case_step.case_id,
                "case_step_id": case_step_doc.case_step.id,
                "notification_info": current_submitted_status,
                "notification_status":NotificationStatus.OPEN,
                "status_id": current_status,
                "role_id": db.query(NotificationMasterDB)
                .filter(NotificationMasterDB.status_id == current_status)
                .first()
                .role_id,
            }
            case_logger.info(f"Current Submitted notification data: {prev_submitted_data}")
            previous_submitted_notification = notification_service.get_notification_by_attributes(
                db=db, **prev_submitted_data
            )
            case_logger.info(f"Previous notification: {previous_submitted_notification}")
            if previous_submitted_notification:
                case_logger.info(
                    f"Updating previous notification for status '{current_submitted_status}'."
                )
                if self.are_all_mandatory_documents_approved(
                    db, case_step_doc.case_step.id
                ) and not self.is_any_document_pending_or_rejected(
                    db, case_step_doc.case_step.id):
                    notification_service.update_notification(
                        previous_submitted_notification.id,
                        notification_data={"notification_status": NotificationStatus.CLOSED},
                        db=db,
                    )
                    case_logger.info(
                        f"Closeo d previous notification for status '{current_submitted_status} & Notification ID: {previous_submitted_notification .id}'."
                    )
        next_case_step = self.get_next_case_step_id(
            db, case_step_doc.case_step.case_id, case_step_doc.case_step.id
        )
        if next_case_step:

            self.create_next_step_notification(db, next_case_step, case_step_doc)

    def create_next_step_notification(self, db: Session, next_case_step, case_step_doc):
        from app.cases.helpers.constants import PENDING_STATUSES, RoleIds
        from app.cases.helpers.constants import NotificationStatus
        from app.cases.services.notification import CaseStepDocumentHelper
        has_required_documents = CaseStepDocumentHelper().has_required_documents(db, next_case_step.id)
        # Check if the next step has required documents
        if not has_required_documents:
            case_logger.info(
                f"Skipping notification creation for next step ID: {next_case_step.id} as it has no required documents"
            )
            return
            
        next_step_order = int(next_case_step.case_step_package_step_master.steps.order)
        case_logger.info(
            f"Next step order: {next_step_order} for case step ID: {case_step_doc.case_step.id}"
        )
        next_pending_status = PENDING_STATUSES.get(next_step_order, None)
        case_logger.info(f"Next pending status: {next_pending_status}")
        if next_pending_status:
            next_pending_status_id = self.get_status_id(db, next_pending_status)
            case_logger.info(
                f"Status ID for '{next_pending_status}': {next_pending_status_id}"
            )
            
            # Check if all mandatory documents in the next step are already approved
            if self.are_all_mandatory_documents_approved(db, next_case_step.id):
                case_logger.info(
                    f"All mandatory documents in the next step (ID: {next_case_step.id}) are already approved. Skipping notification generation."
                )
                return
                
            new_notification_data = {
                "case_id": next_case_step.case_id,
                "case_step_id": next_case_step.id,
                "notification_info": next_pending_status,
                "notification_status": NotificationStatus.OPEN,
                "status_id": next_pending_status_id,
                "role_id": RoleIds.DATA_ENTRY_OPERATOR,
            }
            notification_service = NotificationService()
            case_logger.info(f"New notification data: {new_notification_data}")
            existing_notification = notification_service.get_notification_by_attributes(
                db=db, **new_notification_data
            )
            case_logger.info(f"Existing notification: {existing_notification}")
            if not existing_notification:
                if self.are_all_mandatory_documents_approved(
                    db, case_step_doc.case_step.id
                ):
                    case_logger.info(
                        f"All mandatory documents approved for case step ID: {case_step_doc.case_step.id}."
                    )
                    case_logger.info(
                        f"Creating new notification for case step ID {case_step_doc.case_step.id} & status '{next_pending_status}'."
                    )
                    notification_service.create_notification(
                        notification_data=new_notification_data, db=db
                    )
                    case_logger.info(
                        f"New notification created for case step ID {case_step_doc.case_step.id}. Data: {new_notification_data}"
                    )
            else:
                case_logger.info(
                    f"Updating existing notification for case step ID {case_step_doc.case_step.id} & status '{next_pending_status}'."
                )
                notification_service.update_notification(
                    existing_notification.id,
                    notification_data={"notification_status": NotificationStatus.CLOSED},
                    db=db,
                )
                case_logger.info(
                    f"Closed existing notification for case step ID {case_step_doc.case_step.id} & status '{next_pending_status}'."
                )
                notification_service.create_notification(
                    notification_data=new_notification_data, db=db
                )
                case_logger.info(
                    f"New notification created for case step ID {case_step_doc.case_step.id}. Data: {new_notification_data}"
                )

    def is_any_document_pending_or_rejected(self, db, case_step_id):
        from app.cases.models.case import StatusMaster
        
        case_logger.info(f"Checking if any document is in Pending or Rejected status for case step ID: {case_step_id}")
        
        try:
            pending_status_id = (
                db.query(StatusMaster.id)
                .filter(
                    StatusMaster.key == "case_step_document",
                    StatusMaster.name == "Pending",
                )
                .scalar()
            )
            rejected_status_id = (
                db.query(StatusMaster.id)
                .filter(
                    StatusMaster.key == "case_step_document",
                    StatusMaster.name == "Rejected",
                )
                .scalar()
            )
            case_logger.debug(f"Pending status ID: {pending_status_id}, Rejected status ID: {rejected_status_id}")
            documents = self.get_documents(db, case_step_id, status=[pending_status_id, rejected_status_id])
            if documents:
                case_logger.info(f"Found documents in Pending or Rejected status for case step ID: {case_step_id}")
                return True
            
            case_logger.info(f"No documents in Pending or Rejected status for case step ID: {case_step_id}")
            return False
        except Exception as e:
            case_logger.error(f"Error checking documents for case step ID {case_step_id}: {str(e)}", exc_info=True)
            raise

    def get_documents(self, db, case_step_id, is_required=None, status=None):
        case_logger.info(f"Fetching documents for case step ID: {case_step_id}, is_required: {is_required}, status: {status}")

        try:
            # Subquery to get the latest version of each document
            latest_versions = (
                db.query(
                    CaseStepDocument.guid,
                    func.max(CaseStepDocument.version).label("latest_version")
                )
                .filter(CaseStepDocument.case_step_id == case_step_id)
                .group_by(CaseStepDocument.guid)
                .subquery()
            )
            case_logger.debug(f"Latest versions subquery: {latest_versions}")

            # Main query to get the documents with the latest version
            query = (
                db.query(CaseStepDocument)
                .join(
                    latest_versions,
                    (CaseStepDocument.guid == latest_versions.c.guid) &
                    (CaseStepDocument.version == latest_versions.c.latest_version)
                )
                .filter(CaseStepDocument.case_step_id == case_step_id)
            )
            case_logger.debug(f"Main query: {query}")

            if is_required is not None:
                query = query.filter(CaseStepDocument.is_required == is_required)
                case_logger.debug(f"Filtered by is_required: {is_required}")

            if status is not None:
                if isinstance(status, list):
                    query = query.filter(CaseStepDocument.status.in_(status))
                    case_logger.debug(f"Filtered by status list: {status}")
                else:
                    query = query.filter(CaseStepDocument.status == status)
                    case_logger.debug(f"Filtered by status: {status}")

            documents = query.all()
            case_logger.info(f"Fetched {len(documents)} documents for case step ID: {case_step_id}")
            return documents
        except Exception as e:
            case_logger.error(f"Error fetching documents for case step ID {case_step_id}: {str(e)}", exc_info=True)
            raise


    def get_next_case_step_id(
        self, db: Session, case_id: int, current_case_step_id: int
    ) -> int:
        from app.cases.models.case_step import CaseStep
        from app.master.models.package import PackageStepMasterDB, StepMaster

        # Get the current case step
        current_case_step = (
            db.query(CaseStep)
            .filter(CaseStep.id == current_case_step_id, CaseStep.case_id == case_id)
            .first()
        )
        case_logger.debug(f"Current case step: {current_case_step}")
        if not current_case_step:
            case_logger.error(
                f"Case step with ID {current_case_step_id} for case ID {case_id} not found"
            )
            return None

        # Get the current step order
        current_step_order = (
            db.query(StepMaster.order)
            .join(PackageStepMasterDB, StepMaster.id == PackageStepMasterDB.step_id)
            .filter(PackageStepMasterDB.id == current_case_step.package_step_master_id)
            .scalar()
        )
        case_logger.debug(f"Current step order: {current_step_order}")

        # Get the next step order
        next_step_order = (
            db.query(StepMaster.order)
            .filter(StepMaster.order > current_step_order)
            .order_by(StepMaster.order.asc())
            .first()
        )
        case_logger.debug(f"Next step order: {next_step_order}")
        if not next_step_order:
            case_logger.error(
                f"No next step found for case ID {case_id} and current case step ID {current_case_step_id}"
            )
            return None

        # Get the next case step
        next_case_step = (
            db.query(CaseStep)
            .join(
                PackageStepMasterDB,
                CaseStep.package_step_master_id == PackageStepMasterDB.id,
            )
            .join(StepMaster, PackageStepMasterDB.step_id == StepMaster.id)
            .filter(
                CaseStep.case_id == case_id,
                StepMaster.order == next_step_order.order,
            )
            .first()
        )
        case_logger.debug(f"Next case step: {next_case_step}")
        if not next_case_step:
            case_logger.error(
                f"No next case step found for case ID {case_id} and current case step ID {current_case_step_id}"
            )
            return None
        if next_case_step and next_case_step.case_step_date and next_case_step.case_step_date.date() <= datetime.now().date():
            case_logger.debug(
                f"Next case step date : {next_case_step.case_step_date.date()} and current date : {datetime.now().date()}"
            )
            case_logger.debug(
                f"Next case step date for case ID {case_id} and current case step ID {current_case_step_id} is in the past"
            )
            return next_case_step
        else:
            case_logger.error(
                f"Next case step date for case ID {case_id} and current case step ID {current_case_step_id} is in the future"
            )
            return None
    # def reject_case_step_document(
    #     self,
    #     db: Session,
    #     case_step_document_id: int,
    #     user: object,
    #     data: CaseStepDocumentReject,
    # ):
    #     """
    #     Reject a case step document and log actions.
    #     """
    #     try:
    #         case_logger.info(
    #             f"Attempting to reject case step document ID: {case_step_document_id} by user ID: {user.id}"
    #         )
    #         from app.cases.services.case import StatusMasterService

    #         # Check if the case step document exists
    #         is_exist, case_step_doc = self.get_case_step_doc_by_id(
    #             case_step_document_id, db=db
    #         )
    #         if is_exist:
    #             case_logger.info(
    #                 f"Case step document ID {case_step_document_id} found. Updating status to 'Rejected'."
    #             )

    #             # Update the case step document status to 'Rejected'
    #             status_id = StatusMasterService().get_status_id(
    #                 db, "Rejected", "case_step_document"
    #             )
    #             doc_step_data_dict = {
    #                 "status": status_id,
    #                 "updated_by": user.id,
    #                 "updated_at": datetime.now(),
    #             }
    #             step_document = self.update(
    #                 db, CaseStepDocument, case_step_document_id, **doc_step_data_dict
    #             )

    #             case_step_order = int(
    #                 case_step_doc.case_step.case_step_package_step_master.steps.order
    #             )
    #             status_name_mapping = {
    #                 1: ("Pre-Investigation Submitted", "Pre-Investigation Pending"),
    #                 2: ("PreAuth Submitted", "PreAuth Pending"),
    #                 3: ("Treatment document Submitted", "Treatment document Pending"),
    #                 4: ("Discharge document Submitted", "Discharge document Pending"),
    #                 5: (
    #                     "Post-Investigation document Submitted",
    #                     "Post-Investigation document Pending",
    #                 ),
    #             }

    #             prev_status_name, status_name = status_name_mapping.get(
    #                 case_step_order, (None, None)
    #             )
    #             if status_name:
    #                 status_id = StatusMasterService().get_status_id(
    #                     db=db, status_name=status_name
    #                 )

    #             case_step_data_dict = {"status": status_id}
    #             case_step = self.update(
    #                 db, CaseStep, case_step_doc.case_step.id, **case_step_data_dict
    #             )
    #             case = self.update(db, Case, case_step.case_id, step_status=status_id)
    #             case_logger.info(
    #                 f"Updated case step ID {case_step.id} and case ID {case.id} with status ID {status_id}."
    #             )

    #             # If a comment is provided, submit it to another application's API
    #             if data.comment:
    #                 case_logger.info(
    #                     f"Submitting comment for case step document ID {case_step_document_id}."
    #                 )
    #                 comment_data = {
    #                     "guid": step_document.guid,
    #                     "comment": data.comment,
    #                     "commented_by": f"{user.first_name} {user.last_name}",
    #                 }
    #                 comment_response = self.submit_comment_to_api(comment_data, user)
    #                 if comment_response.status_code != 201:
    #                     case_logger.error(
    #                         f"Failed to submit comment for document ID {case_step_document_id}: {comment_response.text}"
    #                     )
    #                     raise Exception(
    #                         f"Failed to submit comment: {comment_response.text}"
    #                     )

    #                 doc_step_data_dict = {
    #                     "comment": data.comment,
    #                     "updated_at": datetime.now(),
    #                 }
    #                 self.update(
    #                     db,
    #                     CaseStepDocument,
    #                     case_step_document_id,
    #                     **doc_step_data_dict,
    #                 )

    #             # Create an audit trail entry for the case step document rejection
    #             audit_trail_data = {
    #                 "case_id": case_step_doc.case_step.case_id,
    #                 "action": "Step Document Rejected",
    #                 "case_step_id": case_step_doc.case_step.id,
    #                 "case_step_document_id": case_step_document_id,
    #             }
    #             CaseAuditTrailService().create_case_audit_trail(
    #                 db=db, audit_trail=audit_trail_data, user_id=user.id
    #             )
    #             case_logger.info(
    #                 f"Audit trail created for case step document ID {case_step_document_id}."
    #             )

    #             # Create or update notification
    #             notification_service = NotificationService()
    #             notification_data = {
    #                 "case_id": case.id,
    #                 "case_step_id": case_step.id,
    #                 "notification_info": status_name,
    #                 "notification_status": "Open",
    #                 "status_id": status_id,
    #                 "role_id": db.query(NotificationMasterDB)
    #                 .filter(NotificationMasterDB.status_id == status_id)
    #                 .first()
    #                 .role_id,
    #             }
    #             if prev_status_name:
    #                 prev_status = StatusMasterService().get_status_id(
    #                     db=db, status_name=prev_status_name
    #                 )

    #                 prev_notification_data = {
    #                     "case_id": case.id,
    #                     "case_step_id": case_step.id,
    #                     "notification_info": prev_status_name,
    #                     "notification_status": "Open",
    #                     "status_id": prev_status,
    #                     "role_id": db.query(NotificationMasterDB)
    #                     .filter(NotificationMasterDB.status_id == prev_status)
    #                     .first()
    #                     .role_id,
    #                 }
    #                 previous_notification = (
    #                     notification_service.get_notification_by_attributes(
    #                         db=db, **prev_notification_data
    #                     )
    #                 )
    #                 if previous_notification:
    #                     notification_service.update_notification(
    #                         previous_notification.id,
    #                         notification_data={"notification_status": NotificationStatus.CLOSED},
    #                         db=db,
    #                     )
    #                     case_logger.info(
    #                         f"Closed previous notification for status '{prev_status_name}'."
    #                     )

    #             existing_notification = (
    #                 notification_service.get_notification_by_attributes(
    #                     db=db, **notification_data
    #                 )
    #             )

    #             if not existing_notification:
    #                 notification_service.create_notification(
    #                     notification_data=notification_data, db=db
    #                 )
    #                 case_logger.info(
    #                     f"New notification created for case step ID {case_step.id}."
    #                 )

    #             return self.get_case_step_document_by_id(case_step_document_id, db=db)

    #         else:
    #             case_logger.warning(
    #                 f"Case step document ID {case_step_document_id} not found."
    #             )
    #             raise NotFoundException(
    #                 f"Case step document with ID {case_step_document_id} not found"
    #             )

    #     except Exception as e:
    #         case_logger.error(
    #             f"Error rejecting case step document ID {case_step_document_id}: {str(e)}",
    #             exc_info=True,
    #         )
    #         raise
    

    def reject_case_step_document(
        self,
        db: Session,
        case_step_document_id: int,
        user: object,
        data: CaseStepDocumentReject,
    ):
        """
        Reject a case step document and log actions.
        """
        try:
            case_logger.info(
                f"Attempting to reject case step document ID: {case_step_document_id} by user ID: {user.id}"
            )
            from app.cases.services.case import StatusMasterService
    
            # Check if the case step document exists
            is_exist, case_step_doc = self.get_case_step_doc_by_id(
                case_step_document_id, db=db
            )
            if is_exist:
                case_logger.info(
                    f"Case step document ID {case_step_document_id} found. Updating status to 'Rejected'."
                )
    
                # Update the case step document status to 'Rejected'
                status_id = StatusMasterService().get_status_id(
                    db, "Rejected", "case_step_document"
                )
                doc_step_data_dict = {
                    "status": status_id,
                    "updated_by": user.id,
                    "updated_at": datetime.now(),
                }
                step_document = self.update(
                    db, CaseStepDocument, case_step_document_id, **doc_step_data_dict
                )
    
                current_submitted_status, current_pending_status = self.get_status_names(case_step_doc)
                case_logger.info(
                    f"Current submitted status: {current_submitted_status}, Current pending status: {current_pending_status}"
                )
                if current_pending_status:
                    status_id = self.get_status_id(db, current_pending_status)
                    case_logger.info(
                        f"Status ID for '{current_pending_status}': {status_id}"
                    )
    
                # If a comment is provided, submit it to another application's API
                if data.comment:
                    case_logger.info(f"Comment: {data.comment}")
                    self.handle_comment_submission(db, case_step_doc, data.comment, user)
    
                # Create an audit trail entry for the case step document rejection
                self.create_audit_trail(
                    db, case_step_doc, case_step_document_id, user.id, "Step Document Rejected"
                )
    
                # Handle notifications
                self.handle_reject_document_notification(
                    db, case_step_doc, current_pending_status, current_submitted_status, status_id
                )
    
                return self.get_case_step_document_by_id(case_step_document_id, db=db)
    
            else:
                case_logger.warning(
                    f"Case step document ID {case_step_document_id} not found."
                )
                raise NotFoundException(
                    f"Case step document with ID {case_step_document_id} not found"
                )
    
        except Exception as e:
            case_logger.error(
                f"Error rejecting case step document ID {case_step_document_id}: {str(e)}",
                exc_info=True,
            )
            raise
    def handle_reject_document_notification(
        self,
        db: Session,
        case_step_doc,
        current_pending_status: str,
        current_submitted_status: str,
        status_id: int,
    ):
        from app.cases.helpers.constants import NotificationStatus
        notification_service = NotificationService()
        case_logger.info("Handling reject document notification")

        if current_submitted_status:
            current_status = self.get_status_id(db, current_submitted_status)
            case_logger.info(
                f"Status ID for '{current_submitted_status}': {current_status}"
            )
            prev_submitted_data = {
                "case_id": case_step_doc.case_step.case_id,
                "case_step_id": case_step_doc.case_step.id,
                "notification_info": current_submitted_status,
                "notification_status": NotificationStatus.OPEN,
                "status_id": current_status,
                "role_id": db.query(NotificationMasterDB)
                .filter(NotificationMasterDB.status_id == current_status)
                .first()
                .role_id,
            }
            case_logger.info(f"Current Submitted notification data: {prev_submitted_data}")
            previous_submitted_notification = notification_service.get_notification_by_attributes(
                db=db, **prev_submitted_data
            )
            case_logger.info(f"Previous notification: {previous_submitted_notification}")
            if previous_submitted_notification:
                case_logger.info(
                    f"Updating previous notification for status '{current_submitted_status}'."
                )
                notification_service.update_notification(
                    previous_submitted_notification.id,
                    notification_data={"notification_status": NotificationStatus.CLOSED},
                    db=db,
                )
                case_logger.info(
                    f"Closed previous notification for status '{current_submitted_status} & Notification ID: {previous_submitted_notification.id}'."
                )

        if current_pending_status:
            prev_status = self.get_status_id(db, current_pending_status)
            case_logger.info(
                f"Status ID for '{current_pending_status}': {prev_status}"
            )
            prev_notification_data = {
                "case_id": case_step_doc.case_step.case_id,
                "case_step_id": case_step_doc.case_step.id,
                "notification_info": current_pending_status,
                "notification_status": NotificationStatus.OPEN,
                "status_id": prev_status,
                "role_id": db.query(NotificationMasterDB)
                .filter(NotificationMasterDB.status_id == prev_status)
                .first()
                .role_id,
            }
            case_logger.info(f"Previous notification data: {prev_notification_data}")
            previous_notification = notification_service.get_notification_by_attributes(
                db=db, **prev_notification_data
            )
            case_logger.info(f"Previous notification: {previous_notification}")
            if not previous_notification:

                notification_service.create_notification(
                    notification_data=prev_notification_data, db=db
                )
                case_logger.info(
                    f"New notification created for case step ID {case_step_doc.case_step.id}."
                )
            
    def submit_comment_to_api(self, comment_data, user):
        """
        Submit a comment to another application's API.

        Args:
            comment_data (dict): The comment data to submit.
            user (object): The user object of the user submitting the comment.

        Returns:
            Response: The response from the API.
        """
        import requests

        DOCUMENT_COMPONENT_URL = os.getenv("DOCUMENT_COMPONENT_URL")
        headers = {"Tenant-Identifier": os.getenv("TENANT_IDENTIFIER")}
        params = {
            "guid": comment_data["guid"],
            "requested_by": f"{user.first_name} {user.last_name}",
        }

        case_logger.info(f"Submitting comment to API for document GUID: {comment_data['guid']} by user: {user.first_name} {user.last_name}")

        # Get the document version from the API
        doc_version_response = requests.get(
            f"{DOCUMENT_COMPONENT_URL}/document/get", params=params, headers=headers
        )
        case_logger.debug(f"Document version response status code: {doc_version_response.status_code}")

        if doc_version_response.status_code == 200:
            doc_version = doc_version_response.json()["data"]["versions"][0]["number"]
            case_logger.info(f"Document version retrieved: {doc_version}")

            # Submit the comment to the API
            comment_data["version"] = doc_version
            comment_response = requests.post(
                f"{DOCUMENT_COMPONENT_URL}/comment/add/",
                json=comment_data,
                headers=headers,
            )
            case_logger.debug(f"Comment submission response status code: {comment_response.status_code}")
            return comment_response
        elif doc_version_response.status_code == 404:
            case_logger.error(f"Document with GUID {comment_data['guid']} not found")
            raise NotFoundException(f"Document with guid {comment_data['guid']}")
        else:
            error_message = doc_version_response.json().get('message', 'Unknown error')
            case_logger.error(f"Failed to get document version: {error_message}")
            raise Exception(f"Failed to get document version: {error_message}")

    def check_document_version_exists(self, guid, user):
        try:
            import requests

            DOCUMENT_COMPONENT_URL = os.getenv("DOCUMENT_COMPONENT_URL")
            headers = {"Tenant-Identifier": os.getenv("TENANT_IDENTIFIER")}
            params = {
                "guid": guid,
                "requested_by": f"{user.first_name} {user.last_name}",
            }
            # Get the document version from the API
            doc_version_response = requests.get(
                f"{DOCUMENT_COMPONENT_URL}/document/get", params=params, headers=headers
            )
            if doc_version_response.status_code == 200:
                return True
            elif doc_version_response.status_code == 404:
                raise NotFoundException(f"Document")
            else:
                raise Exception(
                    f"Failed to get document version: {doc_version_response.json()['message']}"
                )
        except Exception as e:
            raise e


# Function to create new notifications
def create_new_notifications():
    from app.cases.services.notification import CaseStepDocumentHelper
    db = SessionLocal()
    try:
        # Fetch case_steps for pending treatment and discharge for today (raw query)
        query = text(GET_CASE_STEP_PENDING_TREATMENT_AND_DISCHARGE)
        result = db.execute(query)
        rows = result.fetchall()
        columns = result.keys()  # Get column names from the result

        # Fetch role_id (assuming it comes from a single row)
        role_result = db.execute(text(FETCH_ROLE)).fetchone()
        role_id = role_result[0] if role_result is not None else None

        # Convert rows to a list of dictionaries
        case_steps = [dict(zip(columns, row)) for row in rows]

        for case_step in case_steps:
            case_id = case_step["case_id"]
            status_id = case_step["status"]
            case_step_id = case_step["id"]
            has_required_documents = CaseStepDocumentHelper().has_required_documents(db, case_step_id)
            if has_required_documents:
                

                # Fetch notification_info for the status
                notification_info_row = db.execute(
                    text(FETCH_STATUS_NAME), {"status_id": status_id}
                ).fetchone()
                notification_info = (
                    notification_info_row[0] if notification_info_row else None
                )

                # Check if a notification for the same case_id and status_id already exists
                fetch_query = text(FETCH_CASE_STEPS)
                existing = db.execute(
                    fetch_query, {"case_id": case_id, "status_id": status_id}
                ).fetchone()

                if existing is None:
                    case_logger.info("Creating new notification...")
                    notification_data = {
                        "case_id": case_id,
                        "case_step_id": case_step_id,
                        "notification_info": notification_info,
                        "notification_status": "Open",
                        "status_id": status_id,
                        "role_id": role_id,
                    }
                    case_logger.info(f"Notification data: {notification_data}")
                    try:
                        create_query = text(CREATE_PENDING_NOTIFICATIONS)
                        db.execute(create_query, notification_data)
                        db.commit()
                    except Exception as e:
                        db.rollback()
                        case_logger.error(f"Error inserting notification for case_id {case_id}: {str(e)}", exc_info=True)
                else:
                    case_logger.info(f"No notification created for case_id {case_id} as it already exists.")

    except Exception as e:
        case_logger.error(f"Error in create_new_notifications: {str(e)}", exc_info=True)
        db.rollback()
    finally:
        db.close()


timezone = pytz.timezone("Asia/Kolkata")


# Configure APScheduler
import os
os.environ["TZ"] = "Asia/Kolkata"
scheduler = BackgroundScheduler()
scheduler.add_job(create_new_notifications, "cron", hour="*/1")  # Runs every 1 hour
# scheduler.add_job(create_new_notifications, IntervalTrigger(seconds=15, timezone=timezone))  # Runs every 10 seconds
# scheduler.add_job(create_new_notifications, CronTrigger(hour=0, minute=1, timezone=timezone))
#scheduler.add_job(create_new_notifications, CronTrigger(minute="*/5", timezone=timezone))


scheduler.start()
