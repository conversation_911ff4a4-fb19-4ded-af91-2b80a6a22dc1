import os
from datetime import datetime, timezone, timedelta
from typing import List, Optional
import traceback  # Import the traceback module

from app.cases.exception.custom.case import CaseExceptions, StatusException
from app.cases.models import (
    Case,
    CasePatientRelativeMapping,
    CaseStep,
    CaseStepDocument,
    StatusMaster,
)
from app.notification.models.notification import NotificationDB
from app.cases.models.audit_trail_case import CaseAuditTrailDB
from app.cases.services.case_audit_trail import CaseAuditTrailService
from app.database.database import SessionLocal, transaction
from app.master.models import DocumentMaster, PackageStepDocumentDB, PackageStepMasterDB
from app.master.models.category import CategoryDB
from app.master.models.hospital import HospitalDB
from app.master.models.package import PackageDB, StepMaster
from app.master.models.scheme_type import SchemeType
from app.master.models.sub_category import SubCategoryDB
from app.master.services.scheme_type import SchemeTypeService
from app.models.user import UserDB
from app.patients.models.patient import PatientDB
from app.patients.services.patient import PatientService, PatientUserHelper
from dotenv import load_dotenv
from passlib.context import CryptContext
from sqlalchemy import and_, asc, desc, func, or_, text
from sqlalchemy.orm import Session, aliased, contains_eager, joinedload
from utils.db import BaseService
from utils.exception import NotFoundException

from ..helpers.logger import case_logger
from .case_step_document import CaseStepDocumentService
from .case_step_master import CaseStepService
from app.cases.services.notification import CaseStepDocumentHelper

load_dotenv()
from app.notification.services.notification import NotificationService
from ..helpers.constants import StatusMasterContants, RoleIds
from utils.logger import setup_logger

class CaseService(BaseService):
    """
    Service class for managing case-related operations.
    """

    def __init__(self) -> None:
        self.db = SessionLocal()
        self.logger = case_logger
        # Define status names for roles
        self.STATUS_NAMES = {
            "data_entry_operator": [
                "Pre-Investigation Pending",
                "Pre-Investigation Submitted",
                "PreAuth Pending",
                "PreAuth Submitted",
                "PreAuth Approved",
                "PreAuth Rejected",
                "Treatment document Pending",
                "Treatment document Submitted",
                "Post-Investigation document Pending",
                "Post-Investigation document Submitted",
                "Discharge document Pending",
                "Discharge document Submitted",
            ],
            "admin_and_claims_team_exclude": ["Claim Approved", "Claim Rejected"],
        }

    def row_to_dict(self, row):
        """Convert SQLAlchemy row result to dictionary."""
        if isinstance(row, dict):
            return row
        if hasattr(row, "_asdict"):
            return row._asdict()
        return {column: value for column, value in row.items()}

    def get_case_by_attribute(self, db: Session, value: str, attribute: str = None):
        """Retrieve a case by a specific attribute."""
        try:
            if not attribute:
                attribute = Case.id
            # Query the case from the database
            case = self.get_by_attribute(db, Case, attribute, value)
            if not case:
                return False, None
            return True, case
        except Exception as e:
            self.logger.error("Error retrieving case by attribute %s=%s: %s", 
                             attribute.key if hasattr(attribute, 'key') else 'id', value, str(e), 
                             exc_info=True)
            raise

    def get_case_by_id(self, db: Session, case_id: int):
        """Retrieve a patient by ID."""

        try:
            is_exist, case = self.get_case_by_attribute(value=case_id, db=db)
            if not is_exist:
                CaseExceptions().raise_case_not_found_exception(case_id)
            return is_exist, case
        except Exception as e:
            self.logger.error("Error retrieving case by id=%s: %s", case_id, str(e), 
                             exc_info=True)
            raise

    def get_case_or_raise(self, case_id: int, db: Session):
        case = self.get_by_attribute(db, Case, Case.id, case_id)
        if not case:
            CaseExceptions().raise_case_not_found_exception(case_id)
        return case

    def get_case_details(self, db: Session, case_id: int):
        """Retrieve a case by ID."""
        self.logger.info(f"Retrieving case details for case ID {case_id}")
        try:
            case = self.get_case_or_raise(db=db, case_id=case_id)
            self.logger.debug(f"Case retrieved: {case}")

            # Ensure only one relative is returned, if multiple exist select the first one
            relative = case.relatives[0] if case.relatives else None
            case.relative = relative
            case.scheme = case.package.categories.scheme_type

            # Retrieve associated steps
            case_steps = (
                db.query(CaseStep)
                .join(
                    PackageStepMasterDB,
                    CaseStep.package_step_master_id == PackageStepMasterDB.id,
                )
                .join(StepMaster, PackageStepMasterDB.step_id == StepMaster.id)
                .filter(CaseStep.case_id == case_id)
                .order_by(asc(StepMaster.order))
                .options(
                    joinedload(CaseStep.case_step_package_step_master).joinedload(
                        PackageStepMasterDB.steps
                    )
                )
                .all()
            )
            self.logger.debug(f"Case steps retrieved: {case_steps}")

            pre_auth_date = None
            treatment_date = None
            discharge_date = None
            for step in case_steps:
                step_order = step.case_step_package_step_master.steps.order
                # Capture the dates based on the step order
                if step_order == 2:
                    pre_auth_date = step.case_step_date
                elif step_order == 3:
                    treatment_date = (
                        db.query(CaseStep.case_step_date)
                        .filter_by(
                            case_id=case_id,
                            package_step_master_id=step.package_step_master_id,
                        )
                        .order_by(asc(CaseStep.case_step_date))
                        .first()
                    )
                    if treatment_date:
                        treatment_date = treatment_date[0]
                elif step_order == 4:
                    discharge_date = step.case_step_date

            # Add dates to the case object
            case.pre_auth_date = pre_auth_date
            case.treatment_date = treatment_date
            case.discharge_date = discharge_date
            self.logger.debug(
                f"Case dates set: pre_auth_date={pre_auth_date}, treatment_date={treatment_date}, discharge_date={discharge_date}"
            )

            # Construct response data
            self.logger.info(
                f"Case details retrieved successfully for case ID {case_id}"
            )
            return case
        except Exception as e:
            self.logger.error(
                f"Error occurred while retrieving case details for case ID {case_id}: {e}"
            )
            raise

    def get_case(self, db: Session, case_id: int, user):
        self.logger.info(f"Retrieving case with ID {case_id}")
        try:
            # Fetch case with all related data in a single query
            case_query = (
                db.query(Case)
                .options(
                    joinedload(Case.relatives),
                    joinedload(Case.package)
                    .joinedload(PackageDB.categories)
                    .joinedload(CategoryDB.scheme_type),
                    joinedload(Case.case_steps)
                    .joinedload(CaseStep.case_step_package_step_master)
                    .joinedload(PackageStepMasterDB.steps),
                    joinedload(Case.case_steps).joinedload(CaseStep.status_master),
                    joinedload(Case.case_steps)
                    .joinedload(CaseStep.case_step_documents)
                    .joinedload(CaseStepDocument.document_master),
                    joinedload(Case.case_steps)
                    .joinedload(CaseStep.case_step_documents)
                    .joinedload(CaseStepDocument.status_master),
                )
                .filter(Case.id == case_id)
            )

            if user.is_patient:
                case_query = case_query.outerjoin(
                    CaseStepDocument,
                    and_(
                        CaseStepDocument.case_step_id == CaseStep.id,
                        or_(
                            CaseStepDocument.uploaded_by == user.id,
                            CaseStepDocument.uploaded_by.is_(None),
                        ),
                    ),
                ).options(
                    contains_eager(Case.case_steps).contains_eager(
                        CaseStep.case_step_documents
                    )
                )

            case = case_query.first()

            if not case:
                raise CaseExceptions().raise_case_not_found_exception(case_id)

            relative = case.relatives[0] if case.relatives else None
            case.relative = relative
            case.scheme = case.package.categories.scheme_type

            pending_status_ids = set(
                StatusMasterService().get_status_ids(db, ["Pending"])
            )

            steps_data = []
            pre_auth_date = None
            treatment_date = None
            discharge_date = None
            is_any_treatment_document_uploaded = False
            is_any_discharge_document_uploaded = False

            for step in sorted(
                case.case_steps,
                key=lambda s: s.case_step_package_step_master.steps.order,
            ):
                documents_data = []
                is_any_document_uploaded = False

                for document in step.case_step_documents:
                    if user.is_patient and document.uploaded_by != user.id:
                        continue

                    documents_data.append(
                        {
                            "id": document.id,
                            "guid": document.guid,
                            "document_name": document.document_master.name,
                            "file_name": document.file_name,
                            "version": document.version,
                            "comment": document.comment,
                            "is_required": document.document_master.is_required,
                            "is_geotag_required": document.document_master.is_geotag_required,
                            "status": document.status_master,
                        }
                    )

                    if document.status_master.id not in pending_status_ids:
                        is_any_document_uploaded = True

                documents_data.sort(key=lambda x: not x["is_required"])

                steps_data.append(
                    {
                        "position": step.case_step_package_step_master.steps.order,
                        "id": step.id,
                        "name": step.case_step_package_step_master.steps.type,
                        "case_step_date": step.case_step_date,
                        "step_status_date": step.step_status_date,
                        "documents": documents_data,
                        "status": step.status_master,
                    }
                )

                order = step.case_step_package_step_master.steps.order
                if order == 1:
                    pre_auth_date = step.case_step_date
                elif order == 3:
                    if not treatment_date or step.case_step_date < treatment_date:
                        treatment_date = step.case_step_date
                        is_any_treatment_document_uploaded = is_any_document_uploaded
                elif order == 4:
                    discharge_date = step.case_step_date
                    is_any_discharge_document_uploaded = is_any_document_uploaded

            case.pre_auth_date = pre_auth_date
            case.treatment_date = treatment_date
            case.discharge_date = discharge_date
            case.is_any_treatment_document_uploaded = is_any_treatment_document_uploaded
            case.is_any_discharge_document_uploaded = is_any_discharge_document_uploaded

            case_data = {"case": case, "steps": steps_data}
            self.logger.info(f"Case data constructed: {case_data}")
            return case_data
        except Exception as e:
            self.logger.error(f"Error occurred while retrieving case: {e}")
            raise

    def get_all_cases(self, db: Session, **kwargs):
        """Retrieve all cases."""
        self.logger.info("Retrieving all cases")
        try:
            case_id = kwargs.pop("id")
            page = kwargs.pop("page")
            size = kwargs.pop("size")
            patient_id = kwargs.pop("patient_id")
            status = kwargs.pop("status")
            govt_case_id = kwargs.pop("govt_case_id")
            hospital_id = kwargs.pop("hospital_id")
            package_master_ids = kwargs.pop("package_master_id")
            claim_no = kwargs.pop("claim_no")
            scheme_type_id = kwargs.pop("scheme_type_id")
            patient_name = kwargs.pop("patient_name", None)
            hospital_file_no = kwargs.pop("hospital_file_no", None)
            discharge = kwargs.pop("discharge", None)

            self.logger.debug(f"Query parameters: {kwargs}")

            # Aliases for better readability
            StepMasterAlias = aliased(StepMaster)
            PackageStepMasterAlias = aliased(PackageStepMasterDB)
            CaseStepAlias = aliased(CaseStep)

            # Phase 1: Build base query with essential joins only
            # This reduces the initial dataset size significantly
            base_query = (
                db.query(
                    Case.id,
                    Case.govt_case_id,
                    Case.hospital_file_no,
                    Case.patient_id,
                    Case.package_master_id,
                    Case.pre_auth_approved_amount,
                    Case.claim_no,
                    Case.hospital_id,
                    Case.status,
                    Case.created_at,
                    Case.updated_at,
                )
            )

            # Apply primary key and foreign key filters first (most selective)
            # This drastically reduces the dataset before applying expensive joins
            if case_id:
                base_query = base_query.filter(Case.id == case_id)
            if patient_id:
                base_query = base_query.filter(Case.patient_id.in_(patient_id))
            if package_master_ids:
                base_query = base_query.filter(Case.package_master_id.in_(package_master_ids))
            if hospital_id:
                base_query = base_query.filter(Case.hospital_id.in_(hospital_id))
            if status:
                base_query = base_query.filter(Case.status.in_(status))

            # Apply exact match string filters (more selective than LIKE)
            if govt_case_id and govt_case_id.strip():
                # Use prefix matching instead of contains for better index usage
                base_query = base_query.filter(Case.govt_case_id.like(f"{govt_case_id}%"))
            if claim_no and claim_no.strip():
                base_query = base_query.filter(Case.claim_no.like(f"{claim_no}%"))
            if hospital_file_no and hospital_file_no.strip():
                base_query = base_query.filter(Case.hospital_file_no.like(f"{hospital_file_no}%"))

            # Phase 2: Add necessary joins based on filter requirements
            # Start with the most essential joins that are always needed
            query = (
                base_query
                .join(PatientDB, Case.patient_id == PatientDB.id)
                .join(PackageDB, Case.package_master_id == PackageDB.id)
                .join(CategoryDB, PackageDB.category_id == CategoryDB.id)
                .join(SchemeType, CategoryDB.scheme_type_id == SchemeType.id)
                .join(StatusMaster, Case.status == StatusMaster.id)
                .join(HospitalDB, Case.hospital_id == HospitalDB.id)
                .outerjoin(SubCategoryDB, PackageDB.sub_category_id == SubCategoryDB.id)
            )

            needs_discharge_query = True  # Always include discharge date
            if needs_discharge_query:
                # Subquery to fetch the discharge date - only created when needed
                discharge_date_subquery = (
                    db.query(
                        CaseStepAlias.case_id,
                        CaseStepAlias.case_step_date.label("discharge_date"),
                    )
                    .join(
                        PackageStepMasterAlias,
                        CaseStepAlias.package_step_master_id == PackageStepMasterAlias.id,
                    )
                    .join(
                        StepMasterAlias,
                        PackageStepMasterAlias.step_id == StepMasterAlias.id,
                    )
                    .filter(StepMasterAlias.type == "Discharge")
                    .subquery()
                )
                
                query = query.outerjoin(
                    discharge_date_subquery,
                    Case.id == discharge_date_subquery.c.case_id,
                )
                
                # Apply discharge date filters
                if discharge == "due":
                    query = query.filter(
                        func.date(discharge_date_subquery.c.discharge_date)
                        == func.current_date()
                    )
                elif discharge == "overdue":
                    query = query.filter(
                        func.date(discharge_date_subquery.c.discharge_date)
                        < func.current_date()
                    )

            # Apply scheme type filter
            if scheme_type_id:
                query = query.filter(SchemeType.id.in_(scheme_type_id))

            # Apply patient name filter (less selective)
            if patient_name and patient_name.strip():
                query = query.filter(
                    func.concat(PatientDB.first_name, " ", PatientDB.last_name).like(
                        f"{patient_name}%"
                    )
                )

            # Select the columns needed for the result
            # This is done after filtering to minimize data transfer
            from sqlalchemy import literal
            
            query = query.with_entities(
                Case.id,
                Case.govt_case_id,
                Case.hospital_file_no,
                func.concat(PatientDB.first_name, " ", PatientDB.last_name).label(
                    "patient_name"
                ),
                PackageDB.procedure_code,
                PackageDB.procedure_name,
                SchemeType.id.label("scheme_type_id"),
                SchemeType.type.label("scheme_type"),
                PackageDB.package_amount,
                CategoryDB.code.label("category_code"),
                CategoryDB.name.label("category"),
                SubCategoryDB.code.label("sub_category_code"),
                SubCategoryDB.name.label("sub_category"),
                Case.pre_auth_approved_amount,
                Case.claim_no,
                HospitalDB.name.label("hospital_name"),
                HospitalDB.short_name.label("hospital_short_name"),
                StatusMaster.name.label("status"),
                func.date_format(Case.created_at, "%d/%m/%Y").label("created_at"),
                func.date_format(Case.updated_at, "%d/%m/%Y").label("updated_at"),
                # Only include discharge_date if the subquery was created
                *(
                    [func.date_format(
                        discharge_date_subquery.c.discharge_date, "%d/%m/%Y"
                    ).label("discharge_date")] if needs_discharge_query else [literal(None).label("discharge_date")]
                ),
            )

            # Default order by updated date
            query = query.order_by(Case.updated_at.desc())

            # Optimize count query - only run when pagination is needed
            if page is not None and size is not None:
                # Use a simpler count query structure
                count_query = query.with_entities(func.count(Case.id))
                
                total_count = count_query.scalar()

                # Early return if no results found
                if total_count == 0:
                    self.logger.info("No cases found matching the criteria")
                    return [], 0

                # Apply pagination
                offset = (page - 1) * size
                query = query.offset(offset).limit(size)
                self.logger.debug(
                    "Applied pagination: page=%d, size=%d, offset=%d", page, size, offset
                )
            else:
                # If no pagination, we don't need the count
                total_count = None

            # Execute the query and fetch the results
            self.logger.debug("Executing main query to fetch case data")
            cases = query.all()

            # If we didn't get the count earlier but need it now
            if total_count is None:
                total_count = len(cases)

            # Process the results
            MJP_JAY_SCHEME_ID = 1  # Replace with the actual ID for MJP-JAY

            cases_serializable = []
            for case in cases:
                case_dict = self.row_to_dict(case)
                if case_dict["scheme_type_id"] == MJP_JAY_SCHEME_ID:
                    case_dict["sub_category_code"] = (
                        f"{case_dict['category_code']}{case_dict.get('sub_category_code', '')}"
                    )
                    case_dict["combination_code"] = (
                        f"{case_dict.get('sub_category_code', '')}.{case_dict['procedure_code']}"
                    )
                else:
                    case_dict["combination_code"] = case_dict["procedure_code"]
                cases_serializable.append(case_dict)

            self.logger.info(f"Retrieved {len(cases_serializable)} cases")
            return cases_serializable, total_count
        except Exception as e:
            self.logger.error(f"Error occurred while retrieving cases: {e}", exc_info=True)
            raise

    def create_case(self, case_data, db: Session):
        """Create a new case."""
        self.logger.info("Starting case creation process")
        try:
            with transaction(db):
                (
                    relative_name,
                    relative_contact,
                    pre_auth_date,
                    treatment_date,
                    discharge_date,
                ) = self._extract_case_dates(case_data)
                case_data = self._prepare_case_data(case_data, db)
                new_case = self._create_new_case(case_data, db)
                self._create_patient_user_if_needed(case_data, db)
                self._create_case_patient_relative_mapping(
                    new_case, relative_name, relative_contact, db
                )
                self._create_case_steps(
                    new_case,
                    pre_auth_date,
                    treatment_date,
                    discharge_date,
                    db,
                    new_case.id,
                )
                self._create_case_audit_trail(
                    new_case.id, "Case Created", case_data, db
                )

                self.logger.info("Case creation process completed successfully")
                return new_case
        except Exception as e:
            self.logger.error(f"Error occurred during case creation: {e}")
            raise

    def _extract_case_dates(self, case_data):
        """Extract dates from case data."""
        self.logger.debug("Extracting case dates")
        relative_name = case_data.pop("relative_name")
        relative_contact = case_data.pop("relative_contact")
        pre_auth_date = case_data.pop("pre_auth_date")
        treatment_date = case_data.pop("treatment_date")
        discharge_date = case_data.pop("discharge_date")
        return (
            relative_name,
            relative_contact,
            pre_auth_date,
            treatment_date,
            discharge_date,
        )

    def _prepare_case_data(self, case_data, db):
        """Prepare case data for creation."""
        self.logger.debug("Preparing case data")
        case_data = {
            key: value for key, value in case_data.items() if value is not None
        }
        initial_status = os.getenv("INITIAL_CASE_STATUS")
        initial_status_key = os.getenv("STATUS_KEY")
        initial_status_id = StatusMasterService().get_status_id(
            db, status_name=initial_status, key=initial_status_key
        )
        case_data.update(
            {
                "status": initial_status_id,
                "step_status": initial_status_id,
                "case_status_date": datetime.now(),
            }
        )
        if (
            "hospital_file_no" in case_data
            and case_data["hospital_file_no"] is not None
        ):
            self._check_existing_case(case_data, db)
        return case_data

    def _check_existing_case(self, case_data, db):
        """Check if a case with the same hospital file number already exists."""
        self.logger.debug(
            "Checking for existing case with the same hospital file number"
        )
        hospital_file_no = case_data["hospital_file_no"]
        patient_id = case_data["patient_id"]
        hospital_id = case_data["hospital_id"]
        (
            is_exist,
            existing_case,
        ) = CaseService().is_case_exists_by_hospital_file_no_and_hospital_id(
            hospital_file_no, hospital_id
        )
        if is_exist and existing_case.patient_id != patient_id:
            self.logger.warning(
                f"Case with hospital file number {hospital_file_no} already exists for a different patient"
            )
            CaseExceptions().raise_case_already_exists_with_same_hospital_file_number_and_hospital_exception(
                hospital_file_no
            )

    def _create_new_case(self, case_data, db):
        """Create and add a new case to the database."""
        self.logger.debug("Creating new case")
        new_case = Case(**case_data)
        db.add(new_case)
        db.flush()
        db.refresh(new_case)
        self.logger.info(f"New case created with ID {new_case.id}")
        return new_case

    def _create_patient_user_if_needed(self, case_data, db, patient_id=None):
        """Create a patient user if hospital file number is provided."""
        if "hospital_file_no" in case_data:
            self.logger.debug("Creating patient user if needed")
            if patient_id is None:
                patient_id = case_data["patient_id"]
            patient = PatientService().get_patient_or_raise(
                patient_id=patient_id, db=db
            )
            payload = {
                "first_name": patient.first_name,
                "last_name": patient.last_name,
                "password": case_data["hospital_file_no"],
                "mobile_number": patient.phone_number,
                "email": PatientUserHelper.generate_unique_email(
                    first_name=patient.first_name, last_name=patient.last_name, db=db
                ),
            }
            self._create_user(payload, db)

    def _create_user(self, payload, db):
        """Create a new user in the database."""
        self.logger.debug("Creating new user")
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        password = payload.pop("password")
        hashed_password = pwd_context.hash(password)
        payload["password"] = hashed_password
        payload["is_patient"] = True
        user = UserDB(**payload)
        db.add(user)
        db.flush()
        db.refresh(user)
        self.logger.info(f"New user created with ID {user.id}")

    def _create_case_patient_relative_mapping(
        self, new_case, relative_name, relative_contact, db
    ):
        """Create a mapping between case and patient relative."""
        self.logger.debug("Creating case-patient relative mapping")
        case_patient_relative_data = {
            "case_id": new_case.id,
            "relative_name": relative_name,
            "relative_contact": relative_contact,
        }
        case_patient_relative = CasePatientRelativeMapping(**case_patient_relative_data)
        db.add(case_patient_relative)
        db.flush()
        db.refresh(case_patient_relative)
        self.logger.info(
            f"Case-patient relative mapping created for case ID {new_case.id}"
        )

    def _create_case_steps(
        self, new_case, pre_auth_date, treatment_date, discharge_date, db, case_id
    ):
        """Create steps for the case based on the package steps."""
        self.logger.debug(f"Creating case steps for case ID {case_id}")
        package_steps = (
            db.query(PackageStepMasterDB)
            .filter_by(package_id=new_case.package_master_id)
            .all()
        )
        self.logger.debug(f"Package steps retrieved: {package_steps}")
        notification_array = []
        for package_step in package_steps:
            case_step_data = self._prepare_case_step_data(
                package_step,
                new_case,
                pre_auth_date,
                treatment_date,
                discharge_date,
                db,
            )
            self.logger.debug(f"Prepared case step data: {case_step_data}")
            new_case_step = CaseStep(**case_step_data)
            db.add(new_case_step)
            db.flush()
            self.logger.debug(f"New case step created: {new_case_step}")
            
            # Create case step documents before checking for required documents
            self._create_case_step_documents(new_case_step, package_step, db, case_id)
            self.logger.debug(
                f"Case step documents created for case step ID {new_case_step.id}"
            )
            
            # Check if the step has required documents before creating notifications
            has_required_documents = CaseStepDocumentHelper().has_required_documents(db, new_case_step.id)
            if has_required_documents:
                if new_case_step.status == 1:
                    notification_payload = self._prepare_notification_payload(
                        case_id,
                        new_case_step.id,
                        new_case_step.status,
                        new_case_step.status_master.name,
                        RoleIds.DATA_ENTRY_OPERATOR,
                    )
                    notification_array.append(notification_payload)
                self.logger.debug(f"Step status: {new_case_step.status}")
                self.logger.debug(f"Step name: {new_case_step.status_master.name}")
                self.logger.debug(f"Case Step Date: {new_case_step.case_step_date}")
                self.logger.debug(f"Current Date: {datetime.now().date()}")
                if (
                    new_case_step.status in [3, 7, 11]
                    and new_case_step.case_step_date.date() <= datetime.now().date()
                ):
                    notification_payload = self._prepare_notification_payload(
                        case_id,
                        new_case_step.id,
                        new_case_step.status,
                        new_case_step.status_master.name,
                        RoleIds.DATA_ENTRY_OPERATOR,
                    )
                    notification_array.append(notification_payload)
            else:
                self.logger.info(
                    f"Skipping notification creation for step ID: {new_case_step.id} as it has no required documents"
                )
        
            db.flush()
        if notification_array:
            new_case.notifications = notification_array
            self.logger.debug(f"Notifications created for case ID {new_case.id}")

        self.logger.info(f"Case steps created for case ID {new_case.id}")

    def _prepare_notification_payload(
        self,
        case_id: int,
        case_step_id: int,
        status_id: int,
        notification_info: str,
        role_id: int,
    ):
        """Create a notification for a case step."""
        from app.notification.models.notification import NotificationDB

        notification_payload = {
            "case_id": case_id,
            "case_step_id": case_step_id,
            "notification_info": notification_info,
            "status_id": status_id,
            "role_id": role_id,
        }

        self.logger.info(f"Notification Payload: {notification_payload}")

        return NotificationDB(**notification_payload)

    def _prepare_case_step_data(
            self, package_step, new_case, pre_auth_date, treatment_date, discharge_date, db
        ):
            """Prepare data for creating a case step."""
            self.logger.debug("Preparing case step data")
            case_step_data = {
                "case_id": new_case.id,
                "package_step_master_id": package_step.id,
            }
            step_order = package_step.steps.order
            initial_status = os.getenv("INITIAL_CASE_STATUS")
            initial_status_key = os.getenv("STATUS_KEY")
            self.logger.debug(f"Step order: {step_order}")
            match step_order:
                case 1:
                    self.logger.debug("Step 1")
                    case_step_data.update(
                        {
                            "case_step_date": pre_auth_date,
                            "step_status_date": datetime.now(),
                            "status": StatusMasterService().get_status_id(
                                db, status_name=initial_status, key=initial_status_key
                            ),
                        }
                    )
                case 2:
                    self.logger.debug("Step 2")
                    case_step_data.update(
                        {
                            "case_step_date": pre_auth_date,
                            "step_status_date": datetime.now(),
                            "status": StatusMasterService().get_status_id(
                                db, status_name="PreAuth Pending", key="pre_auth"
                            ),
                        }
                    )
                case 3:
                    self.logger.debug("Step 3")
                    case_step_data.update(
                        {
                            "case_step_date": treatment_date,
                            "status": StatusMasterService().get_status_id(
                                db,
                                status_name="Treatment document Pending",
                                key="treatment",
                            ),
                        }
                    )
                case 4:
                    self.logger.debug("Step 4")
                    case_step_data.update(
                        {
                            "case_step_date": discharge_date,
                            "status": StatusMasterService().get_status_id(
                                db,
                                status_name="Discharge document Pending",
                                key="discharge",
                            ),
                        }
                    )
    
                case 5:
                    self.logger.debug("Step 5")
                    case_step_data.update(
                        {
                            "case_step_date": discharge_date,
                            "status": StatusMasterService().get_status_id(
                                db,
                                status_name="Post-Investigation document Pending",
                                key="post_investigation",
                            ),
                        }
                    )
            return case_step_data
    
    def _create_case_step_documents(self, new_case_step, package_step, db, case_id):
        """Create documents for each case step."""
        self.logger.debug("Creating case step documents")
        package_step_documents = (
            db.query(PackageStepDocumentDB)
            .filter_by(package_step_id=package_step.id)
            .all()
        )

        for package_step_document in package_step_documents:
            case_step_document_data = {
                "case_step_id": new_case_step.id,
                "document_master_id": package_step_document.document_id,
                "is_required": package_step_document.document_master.is_required,
                "status": StatusMasterService().get_status_id(
                    db, status_name="Pending", key="case_step_document"
                ),
            }
            new_case_step_document = CaseStepDocument(**case_step_document_data)
            db.add(new_case_step_document)
            db.flush()
            db.refresh(new_case_step_document)

        self.logger.info(
            f"Case step documents created for case step ID {new_case_step.id}"
        )

    def _create_case_audit_trail(self, case_id, action, case_data, db):
        """Create an audit trail for the case creation."""
        self.logger.debug("Creating case audit trail")
        created_by = case_data.get("created_by", case_data.get("updated_by"))
        case_audit_trail_data = {
            "case_id": case_id,
            "action": action,
            "case_step_id": None,
            "case_step_document_id": None,
            "data": None,
            "created_by": created_by,
            "created_at": datetime.now(),
        }
        case_audit_trail = CaseAuditTrailDB(**case_audit_trail_data)
        db.add(case_audit_trail)
        db.flush()
        self.logger.info(f"Case audit trail created for case ID {case_id}")

    def update_case(self, case_id: int, case_data: dict, db: Session):
        """
        Update a case.

        Args:
            case_id (int): The ID of the case to update.
            case_data (dict): The updated case data.
            db (Session): The database session.

        Returns:
            tuple: A tuple containing a boolean indicating the success of the update and the updated case.
        """
        self.logger.info("Starting case update process for case ID %s", case_id)
        try:
            with transaction(db):
                case = self._get_case_by_id(case_id, db)
                self._check_hospital_file_no(case, case_data, db)
                self._create_patient_user_if_needed(case_data, db, case.patient_id)
                self._validate_and_update_dates(case_id, case_data, db)
                self._close_and_create_notifications(case_id, case_data, db)
                updated_case = self._update_case_data(case_id, case_data, db)
                self._update_case_patient_relative(case_id, case_data, db)
                self._update_case_steps(case_id, case_data, db)
                self._create_case_audit_trail(case_id, "Case Updated", case_data, db)
                self.logger.info(
                    "Case update process completed successfully for case ID %s", case_id
                )
                return True, updated_case
        except Exception as e:
            self.logger.error("Error occurred during case update: %s", str(e), exc_info=True)
            raise
        
    def _close_and_create_notifications(self, case_id, prev_case_data, db):
        """
        Close all notifications and create a new one for the updated case.
        
        Args:
            case_id (int): The ID of the case.
            prev_case_data (dict): The previous case data.
            case_data (dict): The updated case data.
            db (Session): The database session.
            user_id (int): The ID of the user who updated the case.
        """
        notifications = db.query(NotificationDB).filter_by(case_id=case_id).all()
        get_case_step = db.query(CaseStep).filter_by(case_id=case_id).all()
        notification_array = []
        for case_step in get_case_step:
            # Check if the case step has required documents
            has_required_documents = CaseStepDocumentHelper().has_required_documents(db, case_step.id)
            if not has_required_documents:
                self.logger.info('Skipping notification for case step %s as it has no required documents', case_step.id)
                continue
                
            if (
                case_step.case_step_date.date() and case_step.case_step_date.date() != prev_case_data.get("discharge_date").date() and case_step.status == StatusMasterContants.DISCHARGE_DOCUMENT_PENDING
            ):
                self.logger.info('close notification for discharge %s',case_step.id )
                close_discharge_notification = [notification for notification in notifications if notification.case_step_id == case_step.id and notification.notification_status =="Open"]
                case_logger.debug("close_discharge_notification: %s", close_discharge_notification)
                if len(close_discharge_notification) > 0:
                    self.logger.info('notifications %s',close_discharge_notification[0])
                    # close_discharge_notification[0].notification_status = "Close"
                    db.delete(close_discharge_notification[0])
                    
                    '''
                        Create New Notification for Discharge.
                    '''
                    case_logger.debug("Comparing dates: prev_discharge_date=%s, current_date=%s", 
                                     prev_case_data.get("discharge_date").date(), datetime.now().date())
                    if prev_case_data.get("discharge_date").date()== datetime.now().date():
                        case_logger.info("Creating new notification for discharge")
                        new_discharge_notification = NotificationDB(
                            **self._prepare_notification_payload(
                                case_id,
                                case_step.id,
                                case_step.status,
                                close_discharge_notification[0].notification_info,
                                RoleIds.DATA_ENTRY_OPERATOR,
                            )
                        )
                        db.add(new_discharge_notification)
                else:
                    if prev_case_data.get("discharge_date").date()== datetime.now().date():
                        case_logger.info("Creating new notification for discharge")
                        new_discharge_notification = NotificationDB(
                            **self._prepare_notification_payload(
                                case_id,
                                case_step.id,
                                case_step.status,
                                db.query(StatusMaster).filter_by(id=case_step.status).first().name,
                                RoleIds.DATA_ENTRY_OPERATOR,
                            )
                        )
                        db.add(new_discharge_notification)
            if (
                case_step.case_step_date and case_step.case_step_date.date() != prev_case_data.get("treatment_date").date() and case_step.status == StatusMasterContants.TREATMENT_DOCUMENT_PENDING
            ):
                self.logger.info('close notification for treatment %s',case_step.id )
                close_treatment_notification = [notification for notification in notifications if notification.case_step_id == case_step.id and notification.notification_status =="Open"]
                if len(close_treatment_notification) > 0:
                    self.logger.info('notifications %s',close_treatment_notification[0])
                    # close_treatment_notification[0].notification_status = "Close"
                    db.delete(close_treatment_notification[0])
                    '''
                        Create New Notification for Treatment.
                    '''
                    case_logger.debug("Comparing dates: prev_treatment_date=%s, current_date=%s", 
                                     prev_case_data.get("treatment_date").date(), datetime.now().date())
                    if prev_case_data.get("treatment_date").date() == datetime.now().date():
                        case_logger.info("Creating new notification for treatment")
                        new_treatment_notification = NotificationDB(
                            **self._prepare_notification_payload(
                                case_id,
                                case_step.id,
                                case_step.status,
                                close_treatment_notification[0].notification_info,
                                RoleIds.DATA_ENTRY_OPERATOR,
                            )
                        )
                        db.add(new_treatment_notification)
                else:
                    case_logger.info("Creating new notification for treatment")
                    new_treatment_notification = NotificationDB(
                        **self._prepare_notification_payload(
                            case_id,
                            case_step.id,
                            case_step.status,
                            db.query(StatusMaster).filter_by(id=case_step.status).first().name,
                            RoleIds.DATA_ENTRY_OPERATOR,
                        )
                    )
                    db.add(new_treatment_notification)

            if (
                case_step.case_step_date and case_step.case_step_date.date() != prev_case_data.get("pre_auth_date").date() and case_step.status == StatusMasterContants.PRE_AUTH_PENDING
            ):
                self.logger.info('close notification for preauth %s',case_step.id )
                close_pre_auth_notification = [notification for notification in notifications if notification.case_step_id == case_step.id and notification.notification_status =="Open"]
                if len(close_pre_auth_notification) > 0:
                    self.logger.info('notifications %s',close_pre_auth_notification[0])
                    # close_pre_auth_notification[0].notification_status = "Close"
                    db.delete(close_pre_auth_notification[0])
                    
                    '''
                        Create New Notification for Pre Auth.
                    '''
                    case_logger.debug("Comparing dates: prev_pre_auth_date=%s, current_date=%s", 
                                     prev_case_data.get("pre_auth_date").date(), datetime.now().date())
                    if prev_case_data.get("pre_auth_date").date()== datetime.now().date():

                        new_pre_auth_notification = NotificationDB(
                            **self._prepare_notification_payload(
                                case_id,
                                case_step.id,
                                case_step.status,
                                close_pre_auth_notification[0].notification_info,
                                RoleIds.DATA_ENTRY_OPERATOR,
                            )
                        )
                        db.add(new_pre_auth_notification)
                else:
                    new_pre_auth_notification = NotificationDB(
                        **self._prepare_notification_payload(
                            case_id,
                            case_step.id,
                            case_step.status,
                            db.query(StatusMaster).filter_by(id=case_step.status).first().name,
                            RoleIds.DATA_ENTRY_OPERATOR,
                        )
                    )
                    db.add(new_pre_auth_notification)
            db.flush()
        return True
              


    def _get_case_by_id(self, case_id: int, db: Session):
        """Retrieve case by ID and check existence."""
        self.logger.info("Checking if case exists by ID")
        case = self.get_case_or_raise(db=db, case_id=case_id)
        return case

    def _check_hospital_file_no(self, case, case_data: dict, db: Session):
        """Check for existing case with the same hospital file number."""
        if (
            "hospital_file_no" in case_data
            and case_data["hospital_file_no"] is not None
        ):
            hospital_file_no = case_data["hospital_file_no"]
            (
                is_exist,
                existing_case,
            ) = CaseService().is_case_exists_by_hospital_file_no_and_hospital_id(
                hospital_file_no, case.hospital_id
            )
            self.logger.info(
                "Checking if case exists with hospital file number and hospital ID"
            )
            if is_exist and existing_case.patient_id != case.patient_id:
                self.logger.error(
                    "Case already exists with same hospital file number and hospital ID"
                )
                CaseExceptions().raise_case_already_exists_with_same_hospital_file_number_and_hospital_exception(
                    hospital_file_no
                )

    def _validate_and_update_dates(self, case_id: int, case_data: dict, db: Session):
        """Validate and update dates based on the case steps."""
        self.logger.info("Validating and updating dates based on case steps")
        case_steps = db.query(CaseStep).filter_by(case_id=case_id).all()
        current_pre_auth_date = next(
            (
                step.case_step_date
                for step in case_steps
                if step.case_step_package_step_master.steps.order == 1
            ),
            None,
        )
        current_treatment_date = next(
            (
                step.case_step_date
                for step in case_steps
                if step.case_step_package_step_master.steps.order == 3
            ),
            None,
        )

        pre_auth_date = case_data.pop("pre_auth_date", None)
        treatment_date = case_data.pop("treatment_date", None)
        discharge_date = case_data.pop("discharge_date", None)

        if treatment_date and not pre_auth_date:
            pre_auth_date = current_pre_auth_date
        if discharge_date and not treatment_date:
            treatment_date = current_treatment_date

        if (
            treatment_date
            and pre_auth_date
            and treatment_date.date() < pre_auth_date.date()
        ):
            self.logger.error("Treatment date is before pre-auth date")
            CaseExceptions().raise_treatment_date_before_pre_auth_exception()
        if (
            discharge_date
            and treatment_date
            and discharge_date.date() < treatment_date.date()
        ):
            self.logger.error("Discharge date is before treatment date")
            CaseExceptions().raise_discharge_date_before_treatment_date_exception()

        case_data["pre_auth_date"] = pre_auth_date
        case_data["treatment_date"] = treatment_date
        case_data["discharge_date"] = discharge_date

    def _update_case_data(self, case_id: int, case_data: dict, db: Session):
        """Update the case data."""
        self.logger.info("Updating case data")
        if "case_status_date" not in case_data and "status" in case_data:
            case_data["case_status_date"] = datetime.now()
        updated_case = db.query(Case).filter(Case.id == case_id).first()
        
        for attr, value in case_data.items():
            setattr(updated_case, attr, value)
        db.flush()
        db.refresh(updated_case)
        return updated_case

    def _update_case_patient_relative(self, case_id: int, case_data: dict, db: Session):
        """Update the case patient relative information if provided."""
        relative_name = case_data.pop("relative_name", None)
        relative_contact = case_data.pop("relative_contact", None)
        if relative_name is not None or relative_contact is not None:
            self.logger.info("Updating case patient relative information")
            case_patient_relative = (
                db.query(CasePatientRelativeMapping).filter_by(case_id=case_id).first()
            )
            if not case_patient_relative:
                self.logger.error("Case Patient Relative not found")
                CaseExceptions().raise_case_patient_relative_not_found_exception(
                    case_id
                )
            case_patient_relative_payload = {
                "relative_name": relative_name,
                "relative_contact": relative_contact,
            }
            for attr, value in case_patient_relative_payload.items():
                setattr(case_patient_relative, attr, value)
            db.refresh(case_patient_relative)

    def _update_case_steps(self, case_id: int, case_data: dict, db: Session):
        """Update the dates for the appropriate case steps."""
        self.logger.info("Updating case step dates")
        case_steps = db.query(CaseStep).filter_by(case_id=case_id).all()
        for case_step in case_steps:
            step_order = case_step.case_step_package_step_master.steps.order
            if step_order == 1 and case_data["pre_auth_date"] is not None:
                case_step.case_step_date = case_data["pre_auth_date"]
            elif step_order == 2 and case_data["pre_auth_date"] is not None:
                case_step.case_step_date = case_data["pre_auth_date"]
            elif step_order == 3 and case_data["treatment_date"] is not None:
                case_step.case_step_date = case_data["treatment_date"]
            elif step_order == 4 and case_data["discharge_date"] is not None:
                case_step.case_step_date = case_data["discharge_date"]
        db.flush()

    def delete_case(self, case_id: int, db: Session):
        """Delete a case."""
        self.logger.info(f"Attempting to delete case with ID {case_id}")
        try:
            case = self.get_case_or_raise(db=db, case_id=case_id)
            self.logger.debug(f"Case found: {case}")
            self.delete(db, Case, case_id)
            self.logger.info(f"Case with ID {case_id} deleted successfully")
            return True
        except Exception as e:
            self.logger.error(
                f"Error occurred while deleting case with ID {case_id}: {e}"
            )
            raise

    def is_case_exists(self, case_id: int):
        """Check if a case exists."""
        try:
            is_exist, _ = self.get_case_by_id(db=self.db, case_id=case_id)
            return is_exist
        except Exception as e:
            self.logger.error("Error checking if case exists with id=%s: %s", case_id, str(e), exc_info=True)
            raise

    def get_cases_by_patient_id(self, patient_id: int):
        """Retrieve a cases by patient ID."""
        try:
            _, cases = self.get_case_by_attribute(
                db=self.db, value=patient_id, attribute=Case.patient_id
            )
            return cases
        except Exception as e:
            self.logger.error("Error retrieving cases by patient_id=%s: %s", patient_id, str(e), exc_info=True)
            raise

    def is_case_exists_by_govt_case_id(self, govt_case_id: str):
        """Check if a case exists by govt_case_id."""
        try:
            _, cases = self.get_case_by_attribute(
                db=self.db, value=govt_case_id, attribute=Case.govt_case_id
            )
            return _
        except Exception as e:
            self.logger.error("Error checking if case exists with govt_case_id=%s: %s", govt_case_id, str(e), exc_info=True)
            raise

    def filter_cases_by_attribute(self, attribute: str, value: str, db: Session):
        """Filter cases based on a specific attribute."""
        try:
            cases = self.filter_by_attribute(db, Case, attribute, value)
            return cases
        except Exception as e:
            self.logger.error("Error filtering cases by %s=%s: %s", attribute, value, str(e), exc_info=True)
            raise

    def add_next_treatment_date(
        self, case_id: int, next_treatment_date: str, db: Session, user_id: int
    ):
        try:
            # Fetch the case and validate
            case = db.query(Case).filter(Case.id == case_id).first()
            if not case:
                CaseExceptions().raise_case_not_found_exception(case_id)

            # Validate the next treatment date
            is_treatment_date_exists = self.is_treatment_date_exists(
                case_id=case.id, treatment_date=next_treatment_date
            )
            if is_treatment_date_exists:
                CaseExceptions().raise_same_treatment_date_already_exists_exception(
                    treatment_date=next_treatment_date
                )

            # Fetch the package steps for the case
            package_steps = (
                db.query(PackageStepMasterDB)
                .filter_by(package_id=case.package_master_id)
                .all()
            )

            # Find the treatment step in the package steps
            treatment_step = next(
                (step for step in package_steps if step.steps.order == 3), None
            )

            if not treatment_step:
                raise ValueError("Treatment step not found in package")

            # Create a new step for the next treatment date
            new_case_step_data = {
                "case_id": case_id,
                "case_step_date": next_treatment_date,
                "package_step_master_id": treatment_step.id,
                "status": StatusMasterService().get_status_id(
                    db, status_name="Treatment document Pending", key="treatment"
                ),
            }
            new_case_step = CaseStepService().create_case_step(db, new_case_step_data)

            # Get the documents associated with this step
            package_step_documents = (
                db.query(PackageStepDocumentDB)
                .filter_by(package_step_id=treatment_step.id)
                .all()
            )

            for package_step_document in package_step_documents:
                # Create CaseStepDocument for each document type in the package step
                case_step_document_data = {
                    "case_step_id": new_case_step.id,
                    "document_master_id": package_step_document.document_id,
                    "is_required": package_step_document.document_master.is_required,
                    "status": StatusMasterService().get_status_id(
                        db, status_name="Pending", key="case_step_document"
                    ),
                }
                CaseStepDocumentService().create_case_step_document(
                    db, case_step_document_data
                )

            # Audit trail info save when next treatment date is added
            audit_trail_data = {
                "case_id": case_id,
                "action": "Next treatment date added",
                "case_step_id": new_case_step.id,
                "case_step_document_id": None,
            }
            CaseAuditTrailService().create_case_audit_trail(
                db=db, audit_trail=audit_trail_data, user_id=user_id
            )

            db.commit()
            return case
        except Exception as e:
            self.logger.error("Error adding next treatment date for case_id=%s: %s", case_id, str(e), exc_info=True)
            raise

    def is_case_exists_by_hospital_file_no_and_hospital_id(
        self, hospital_file_no: str, hospital_id: int
    ):
        try:
            case = (
                self.db.query(Case)
                .filter_by(hospital_file_no=hospital_file_no, hospital_id=hospital_id)
                .first()
            )
            if not case:
                return False, None
            return True, case
        except Exception as e:
            self.logger.error("Error checking if case exists with hospital_file_no=%s and hospital_id=%s: %s", hospital_file_no, hospital_id, str(e), exc_info=True)
            raise

    def get_treatment_dates(self, db: Session, case_id: int):
        is_exists, case = self.get_case_by_id(db, case_id=case_id)
        # Fetch all treatment steps for the given case ID
        treatment_steps = (
            db.query(CaseStep)
            .join(
                PackageStepMasterDB,
                CaseStep.package_step_master_id == PackageStepMasterDB.id,
            )
            .join(StepMaster, PackageStepMasterDB.step_id == StepMaster.id)
            # Assuming order 3 is for treatment
            .filter(CaseStep.case_id == case_id, StepMaster.order == 3)
            .order_by(desc(CaseStep.case_step_date))
            .all()
        )

        # Extract the treatment dates from the treatment steps
        treatment_dates = [
            {
                "case_step_id": step.id,
                "treatment_date": step.case_step_date.date().strftime("%d-%m-%Y"),
            }
            for step in treatment_steps
        ]

        return treatment_dates

    def get_combination_code_from_case(self, case, MJP_JAY_SCHEME_ID):
        """
        Returns the combination code based on the case's package and category details.

        :param case: Case object containing package, procedure_code, and category information
        :param MJP_JAY_SCHEME_ID: ID for MJP-JAY scheme
        :return: Dictionary with combination_code and other relevant details
        """
        procedure_code = case.package.procedure_code
        category_code = case.package.categories.code
        scheme_type_id = case.package.categories.scheme_type_id
        # Check if the scheme_type_id matches MJP_JAY_SCHEME_ID
        if scheme_type_id == MJP_JAY_SCHEME_ID:
            sub_category_code = (
                case.package.sub_categories.code if case.package.sub_categories else ""
            )
            # Create combination_code using category_code and sub_category_code
            combination_code = f"{category_code}{sub_category_code}.{procedure_code}"
        else:
            # If not MJP-JAY, set combination_code to procedure_code only
            combination_code = procedure_code

        return combination_code

    def is_treatment_date_exists(self, case_id: int, treatment_date: datetime):
        """
        Checks if the given treatment date exists for the case.
        """
        try:
            treatment_steps = (
                self.db.query(CaseStep)
                .join(
                    PackageStepMasterDB,
                    CaseStep.package_step_master_id == PackageStepMasterDB.id,
                )
                .join(StepMaster, PackageStepMasterDB.step_id == StepMaster.id)
                # Assuming order 3 is for treatment
                .filter(
                    CaseStep.case_id == case_id,
                    StepMaster.order == 3,
                    CaseStep.case_step_date == treatment_date.date(),
                )
                .first()
            )
            return bool(treatment_steps)
        except Exception as e:
            self.logger.error("Error checking if treatment date exists for case_id=%s: %s", case_id, str(e), exc_info=True)
            raise

    def check_case_exist_with_hospital_and_schemes(
        self, db: Session, hospital_id: int, schemes: list
    ):
        try:
            cases = (
                db.query(Case)
                .join(HospitalDB, Case.hospital_id == HospitalDB.id)
                .join(PackageDB, Case.package_master_id == PackageDB.id)
                .join(CategoryDB, PackageDB.category_id == CategoryDB.id)
                .filter(
                    HospitalDB.id == hospital_id, CategoryDB.scheme_type_id.in_(schemes)
                )
                .all()
            )
            return bool(cases)
        except Exception as e:
            self.logger.error("Error checking if case exists with hospital_id=%s, schemes=%s: %s", hospital_id, schemes, str(e), exc_info=True)
            raise

    def get_old_claim_paid_cases(self, db: Session, threshold_days: int = 90):
        """
        Retrieve cases with 'Claim Paid' status that are older than the threshold days
        and organize their document GUIDs by case steps.
        
        Args:
            db (Session): The database session.
            threshold_days (int, optional): The threshold in days. Defaults to 90.
            
        Returns:
            list: A list of dictionaries containing case information and document GUIDs organized by steps.
        """
        try:
            # Get the status ID for 'Claim Paid'
            claim_paid_status = db.query(StatusMaster).filter_by(name="Claim Paid").first()
            if not claim_paid_status:
                self.logger.error("Status 'Claim Paid' not found in the database")
                return []
                
            # Calculate the threshold date
            threshold_date = datetime.now() - timedelta(days=threshold_days)
            
            # Query cases with 'Claim Paid' status and claim_status_date older than threshold
            cases = (
                db.query(Case)
                .filter(
                    Case.status == claim_paid_status.id,
                    Case.claim_status_date <= threshold_date
                )
                .all()
            )
            
            result = []
            for case in cases:
                case_data = {
                    "case_id": case.id,
                    # "govt_case_id": case.govt_case_id,
                    # "patient_id": case.patient_id,
                    # "claim_status_date": case.claim_status_date,
                    "documents_by_step": {}
                }
                
                # Get all case steps for this case
                case_steps = db.query(CaseStep).filter(CaseStep.case_id == case.id).all()
                
                for step in case_steps:
                    # Get the step type from package_step_master
                    package_step = db.query(PackageStepMasterDB).filter(
                        PackageStepMasterDB.id == step.package_step_master_id
                    ).first()
                    
                    if package_step:
                        step_master = db.query(StepMaster).filter(
                            StepMaster.id == package_step.step_id
                        ).first()
                        
                        if step_master:
                            step_type = step_master.type.name  # This will be like 'PRE_INVESTIGATION'
                            
                            # Get all documents for this step
                            documents = db.query(CaseStepDocument).filter(
                                CaseStepDocument.case_step_id == step.id
                            ).all()
                            
                            # Extract GUIDs
                            guids = [doc.guid for doc in documents if doc.guid]
                            
                            # Add to the result using the step type as key
                            case_data["documents_by_step"][step_type.lower()] = guids
                
                result.append(case_data)
                
            return result
            
        except Exception as e:
            self.logger.error("Error in get_old_claim_paid_cases: %s", str(e))
            self.logger.error("Traceback: %s", traceback.format_exc())
            raise

    def backup_old_claim_paid_case(self, db: Session, case_id: int, username: str):
        """
        Backup documents for a specific old claim paid case.
        
        Args:
            db (Session): The database session.
            case_id (int): The case ID to backup.
            username (str): Username of the person requesting the backup.
            
        Returns:
            Dict: Response from the backup API or None if case not found.
        """
        try:
            # Get the status ID for 'Claim Paid'
            claim_paid_status = db.query(StatusMaster).filter_by(name="Claim Paid").first()
            if not claim_paid_status:
                self.logger.error("Status 'Claim Paid' not found in the database")
                return None
                
            # Query the specific case
            case = (
                db.query(Case)
                .filter(
                    Case.id == case_id,
                    Case.status == claim_paid_status.id
                )
                .first()
            )
            
            if not case:
                self.logger.error(f"Case with ID {case_id} not found or not in 'Claim Paid' status")
                return None
                
            # Get all case steps for this case
            case_steps = db.query(CaseStep).filter(CaseStep.case_id == case.id).all()
            
            documents_by_step = {}
            
            for step in case_steps:
                # Get the step type from package_step_master
                package_step = db.query(PackageStepMasterDB).filter(
                    PackageStepMasterDB.id == step.package_step_master_id
                ).first()
                
                if package_step:
                    step_master = db.query(StepMaster).filter(
                        StepMaster.id == package_step.step_id
                    ).first()
                    
                    if step_master:
                        step_type = step_master.type.name.lower()  # This will be like 'pre_investigation'
                        
                        # Get all documents for this step
                        documents = db.query(CaseStepDocument).filter(
                            CaseStepDocument.case_step_id == step.id
                        ).all()
                        
                        # Extract GUIDs
                        guids = [doc.guid for doc in documents if doc.guid]
                        
                        # Add to the result using the step type as key
                        documents_by_step[step_type] = guids
            
            # Import here to avoid circular imports
            from app.cases.services.document_backup import DocumentBackupService
            
            # Send the backup request
            backup_service = DocumentBackupService()
            result = backup_service.backup_case_documents(
                case_id=case.id,
                documents_by_step=documents_by_step,
                username=username
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in backup_old_claim_paid_case for case_id={case_id}: %s", str(e))
            self.logger.error("Traceback: %s", traceback.format_exc())
            raise

    def backup_all_old_claim_paid_cases(self, db: Session, threshold_days: int = 90, username: str = "system"):
        """
        Backup documents for all old claim paid cases.
        
        Args:
            db (Session): The database session.
            threshold_days (int, optional): The threshold in days. Defaults to 90.
            username (str, optional): Username of the person requesting the backup. Defaults to "system".
            
        Returns:
            Dict: Summary of backup results.
        """
        try:
            # Get old claim paid cases
            cases = self.get_old_claim_paid_cases(db, threshold_days)
            
            if not cases:
                return {"status": "success", "message": "No old claim paid cases found to backup", "total": 0}
            
            # Import here to avoid circular imports
            from app.cases.services.document_backup import DocumentBackupService
            backup_service = DocumentBackupService()
            
            results = {
                "total": len(cases),
                "successful": 0,
                "failed": 0,
                "details": []
            }
            
            for case in cases:
                try:
                    case_id = case["case_id"]
                    documents_by_step = case["documents_by_step"]
                    
                    # Skip cases with no documents
                    if not any(documents_by_step.values()):
                        results["details"].append({
                            "case_id": case_id,
                            "status": "skipped",
                            "message": "No documents found"
                        })
                        continue
                    
                    # Send the backup request
                    backup_result = backup_service.backup_case_documents(
                        case_id=case_id,
                        documents_by_step=documents_by_step,
                        username=username
                    )
                    
                    results["successful"] += 1
                    results["details"].append({
                        "case_id": case_id,
                        "status": "success",
                        "response": backup_result
                    })
                    
                except Exception as e:
                    results["failed"] += 1
                    results["details"].append({
                        "case_id": case["case_id"],
                        "status": "failed",
                        "error": str(e)
                    })
                    self.logger.error(f"Error backing up case {case['case_id']}: {str(e)}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error in backup_all_old_claim_paid_cases: %s", str(e))
            self.logger.error("Traceback: %s", traceback.format_exc())
            raise


class StatusMasterService(BaseService):
    """
    Service class for managing status master data.
    """

    def __init__(self) -> None:
        self.db = SessionLocal()
        self.logger = setup_logger(log_file="status_master.log")  # Use logger instance

    def get_all_status(self, db: Session, **kwargs):
        """Retrieve all statuse."""
        try:
            kwargs = {key: value for key, value in kwargs.items() if value is not None}
            if not bool(kwargs):
                statuses = db.query(StatusMaster).except_all(
                    db.query(StatusMaster).filter_by(key="case_step_document")
                )
            else:
                statuses = (
                    db.query(StatusMaster)
                    .filter_by(**kwargs)
                    .except_all(
                        db.query(StatusMaster).filter_by(key="case_step_document")
                    )
                )

            total_count = statuses.count()
            statuses = statuses.all()
            # if not statuses:
            #     raise StatusException().raise_statuses_not_found_exception()
            return statuses, total_count
        except Exception as e:
            self.logger.error("Error in get_status_id: %s", str(e))
            self.logger.error("Traceback: %s", traceback.format_exc())  # Log traceback
            raise

    def get_status_id(self, db: Session, status_name, key: Optional[str] = None):
        """Retrieve the status ID by status name and optional key."""
        try:
            if not key:
                status = db.query(StatusMaster).filter_by(name=status_name).first()
            else:
                status = (
                    db.query(StatusMaster).filter_by(name=status_name, key=key).first()
                )
            if not status:
                raise StatusException().raise_statuses_not_found_exception()
            return status.id
        except Exception as e:
            self.logger.error("Error in get_status_id: %s", str(e))
            self.logger.error("Traceback: %s", traceback.format_exc())  # Log traceback
            raise

    def get_status_ids(self, db: Session, status_names: List[str]) -> List[int]:
        """Fetch status IDs based on status names."""
        try:
            results = (
                db.query(StatusMaster.id)
                .filter(StatusMaster.name.in_(status_names))
                .all()
            )
            return [result[0] for result in results]
        except Exception as e:
            self.logger.error("Error in get_status_ids: %s", str(e))
            self.logger.error("Traceback: %s", traceback.format_exc())  # Log traceback
            raise


class CasePatientRelativeService(BaseService):
    """
    Service class for managing case patient relative data.
    """

    def __init__(self) -> None:
        self.db = SessionLocal()
        self.logger = setup_logger(name=__name__, log_file="case_patient_relative.log")

    def create_case_patient_relative(
        self, db: Session, case_patient_relative_data: dict
    ):
        """Create a new case patient relative."""
        try:
            # Create and add the case patient relative
            new_case_patient_relative = self.create(
                db, CasePatientRelativeMapping, **case_patient_relative_data
            )
            db.commit()
            self.logger.info("Created case patient relative for case_id=%s", 
                            case_patient_relative_data.get('case_id'))
            return new_case_patient_relative
        except Exception as e:
            db.rollback()  # Rollback the transaction on error
            self.logger.error("Error creating case patient relative: %s", str(e), exc_info=True)
            raise

    def update_case_patient_relative(
        self, db: Session, case_id: int, relative_name: str, relative_contact: str
    ):
        """Update the case patient relative."""
        try:
            # Retrieve the case patient relative
            case_patient_relative = (
                db.query(CasePatientRelativeMapping).filter_by(case_id=case_id).first()
            )
            if not case_patient_relative:
                self.logger.warning("Case patient relative not found for case_id=%s", case_id)
                raise NotFoundException(details="Case Patient Relative")

            # Update the relative name and contact
            self.update(
                db,
                CasePatientRelativeMapping,
                case_patient_relative.id,
                relative_name=relative_name,
                relative_contact=relative_contact,
            )

            db.commit()
            self.logger.info("Updated case patient relative for case_id=%s", case_id)
            return case_patient_relative
        except Exception as e:
            db.rollback()  # Rollback the transaction on error
            self.logger.error("Error updating case patient relative for case_id=%s: %s", 
                             case_id, str(e), exc_info=True)
            raise