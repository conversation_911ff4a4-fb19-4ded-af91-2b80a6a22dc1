import os
import requests
from typing import Dict, List, Optional
import logging
from dotenv import load_dotenv
from utils.logger import setup_logger
# Load environment variables
load_dotenv()

# Configure logging
logger = setup_logger(log_file="document_backup.log")

class DocumentBackupService:
    """Service for backing up documents to the structured document backup API."""
    
    def __init__(self):
        """Initialize the DocumentBackupService with configuration from environment variables."""
        self.document_component_url = os.getenv("DOCUMENT_COMPONENT_URL", "http://127.0.0.1:8001/")
        self.tenant_identifier = os.getenv("TENANT_IDENTIFIER", "arogya_yojna")
        
        # Ensure the document component URL ends with a slash
        if not self.document_component_url.endswith('/'):
            self.document_component_url += '/'
            
        # Full endpoint URL
        self.backup_endpoint = f"{self.document_component_url}document/structured-backup"
        
    def backup_documents(self, parent_id: int, documents_by_category: Dict[str, List[str]], 
                         requested_by: str) -> Dict:
        """
        Send a request to backup documents to the structured document backup API.
        
        Args:
            parent_id (int): ID of the parent entity (case_id)
            documents_by_category (Dict[str, List[str]]): Dictionary mapping categories to lists of document GUIDs
            requested_by (str): Username of the person requesting the backup
            
        Returns:
            Dict: Response from the API
        """
        try:
            # Prepare request payload
            payload = {
                "parent_id": parent_id,
                "documents_by_category": documents_by_category,
                "requested_by": requested_by,
                "operation": "move"
            }
            
            # Prepare headers
            headers = {
                "Content-Type": "application/json",
                "tenant-identifier": self.tenant_identifier
            }
            
            # Log the request
            logger.info(f"Sending backup request for parent_id={parent_id}, requested_by={requested_by}")
            
            # Make the API call
            response = requests.post(
                self.backup_endpoint,
                json=payload,
                headers=headers
            )
            
            # Check if the request was successful
            response.raise_for_status()
            
            # Return the response data
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error backing up documents: {str(e)}")
            raise Exception(f"Failed to backup documents: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error in document backup: {str(e)}")
            raise
            
    def backup_case_documents(self, case_id: int, documents_by_step: Dict[str, List[str]], 
                              username: str) -> Dict:
        """
        Backup documents for a specific case.
        
        Args:
            case_id (int): The case ID
            documents_by_step (Dict[str, List[str]]): Dictionary mapping step types to lists of document GUIDs
            username (str): Username of the person requesting the backup
            
        Returns:
            Dict: Response from the API
        """
        # Map step types to expected category names if needed
        # In this case, the step types from StepType enum match the expected categories
        return self.backup_documents(
            parent_id=case_id,
            documents_by_category=documents_by_step,
            requested_by=username
        )
