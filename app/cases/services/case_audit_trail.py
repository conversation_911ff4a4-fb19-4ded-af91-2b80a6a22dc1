from datetime import datetime
from xml.dom import NotFoundErr
from app.cases.models.audit_trail_case import CaseAuditTrailDB
from app.database.database import SessionLocal
from sqlalchemy.orm import Session
from utils.db import BaseService
from utils.logger import setup_logger


class CaseAuditTrailService(BaseService):
    def __init__(self) -> None:
        self.db = SessionLocal()
        self.logger = setup_logger(name=__name__, log_file="case_audit_trail.log")

    def create_case_audit_trail(self, db: Session, user_id: int, audit_trail: dict):
        """Create a new case audit trail."""
        try:
            # Create a new case audit trail instance and add it to the database
            case_audit_trail = self._prepare_case_audit_trail_data(user_id, audit_trail)
            
            self.logger.info("Creating case audit trail for case_id=%s, action=%s", 
                            audit_trail['case_id'], audit_trail['action'])
            data = self.create(db, CaseAuditTrailDB, **case_audit_trail)
            
            return data
        except Exception as e:
            self.logger.error("Failed to create case audit trail: %s", str(e), exc_info=True)
            raise
    
    def _prepare_case_audit_trail_data(self, user_id: int, audit_trail: dict):
        case_audit_trail_data = {
            "case_id": audit_trail['case_id'],
            "case_step_id": audit_trail['case_step_id'],
            "case_step_document_id": audit_trail['case_step_document_id'],
            "action": audit_trail['action'],
            "data": audit_trail.get('data', None),
            "created_by": user_id,
            "created_at": datetime.now(),
        }
        return case_audit_trail_data

    def create_case_audit_trail_by_transaction(self, db: Session, user_id: int, audit_trail: dict):
        try:
            self.logger.info("Creating case audit trail in transaction for case_id=%s, action=%s", 
                            audit_trail['case_id'], audit_trail['action'])
            case_audit_trail_data = self._prepare_case_audit_trail_data(user_id, audit_trail)
            return self.transaction_create(db, CaseAuditTrailDB, **case_audit_trail_data)
        except Exception as e:
            self.logger.error("Failed to create case audit trail in transaction: %s", str(e), exc_info=True)
            raise