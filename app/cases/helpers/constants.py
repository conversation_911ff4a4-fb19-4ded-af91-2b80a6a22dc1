# Define role IDs
class RoleIds:
    ADMIN = 1
    CLAIM = 2
    DATA_ENTRY_OPERATOR = 3


class NotificationStatus:
    OPEN = "Open"
    CLOSED = "Close"

# Define pending and submitted statuses
PENDING_STATUSES = {
    1: "Pre-Investigation Pending",
    2: "PreAuth Pending",
    3: "Treatment document Pending",
    4: "Discharge document Pending",
    5: "Post-Investigation document Pending",
}
SUBMITTED_STATUSES = {
    1: "Pre-Investigation Submitted",
    2: "PreAuth Submitted",
    3: "Treatment document Submitted",
    4: "Discharge document Submitted",
    5: "Post-Investigation document Submitted",
}


class StatusMasterContants:
    PRE_AUTH_PENDING = 3
    TREATMENT_DOCUMENT_PENDING = 7
    DISCHARGE_DOCUMENT_PENDING = 11
   