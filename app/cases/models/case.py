from enum import Enum
from app.database.database import Base
from sqlalchemy import Column, Date, ForeignKey, Integer, String, Boolean, DateTime
from datetime import datetime
from sqlalchemy.orm import relationship


class StatusMaster(Base):
    "Status Master"

    __tablename__ = "status_master"

    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    name = Column(String(50), nullable=False)
    key = Column(String(50), nullable=False)

    case = relationship(
        "Case",
        back_populates="case_status",
        foreign_keys="[Case.status]"
    )
    step_case = relationship(
        "Case",
        back_populates="stepstatus",
        foreign_keys="[Case.step_status]"
    )
    case_step = relationship(
        "CaseStep", back_populates="status_master")
    case_step_documents = relationship(
        "CaseStepDocument", back_populates="status_master")


class Case(Base):
    """Case model."""

    __tablename__ = "case"

    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    govt_case_id = Column(String(255), unique=True, nullable=True, index=True)
    patient_id = Column(Integer, ForeignKey("patient.id"), index=True)
    package_master_id = Column(
        Integer, ForeignKey('package_master.id'), index=True)
    hospital_id = Column(Integer, ForeignKey('hospital_master.id'), index=True)
    hospital_file_no = Column(String(255), nullable=True)
    claim_no = Column(String(255), nullable=True)
    pre_auth_approved_amount = Column(Integer, nullable=True)
    # To track pre auth approved date on government site
    pre_auth_status_date = Column(DateTime, nullable=True)
    # To track claim approved/rejected date on government site
    claim_status_date = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    created_by = Column(Integer, ForeignKey("users.id"))
    updated_by = Column(Integer, ForeignKey("users.id"))
    # To track case status on government site and case status date
    status = Column(Integer, ForeignKey('status_master.id'), index=True)
    case_status_date = Column(DateTime, nullable=True)
    # To track step status in our system
    step_status = Column(Integer, ForeignKey('status_master.id'), index=True)
    package = relationship("PackageDB", back_populates="cases")
    patient = relationship("PatientDB", back_populates="cases")
    case_steps = relationship("CaseStep", back_populates="case")
    relatives = relationship(
        "CasePatientRelativeMapping", back_populates="case")
    audit_trails = relationship("CaseAuditTrailDB", back_populates="case")
    hospital = relationship("HospitalDB", back_populates="cases")
    case_status = relationship(
        "StatusMaster",
        back_populates="case",
        foreign_keys="[Case.status]"
    )
    stepstatus = relationship(
        "StatusMaster",
        back_populates="step_case",
        foreign_keys="[Case.step_status]"
    )

    notifications = relationship("NotificationDB", back_populates="case")

class CasePatientRelativeMapping(Base):
    """Mapping of a patient's relative to a case."""

    __tablename__ = "case_patient_relative_mapping"

    id = Column(Integer, primary_key=True, autoincrement=True)
    case_id = Column(Integer, ForeignKey(
        "case.id", ondelete="CASCADE"), nullable=False, index=True)
    relative_name = Column(String(255), nullable=False)
    relative_contact = Column(String(255), nullable=False)

    case = relationship("Case", back_populates="relatives")

    def __repr__(self):
        return f"<PatientRelativeCaseMapping(id={self.id}, case_id={self.case_id}, relative_name={self.relative_name}, relative_contact={self.relative_contact})>"
