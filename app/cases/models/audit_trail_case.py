from app.database.database import Base
from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, String, <PERSON>olean, DateTime
from datetime import datetime
from sqlalchemy.orm import relationship


class CaseAuditTrailDB(Base):
    """
    Represents an audit trail for case management actions.
    """
    __tablename__ = "case_audit_trail"

    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    case_id = Column(Integer, ForeignKey('case.id'),
                     nullable=False, index=True)
    case_step_id = Column(Integer, ForeignKey('case_step.id'), nullable=True)
    case_step_document_id = Column(Integer, ForeignKey(
        'case_step_document.id'), nullable=True)
    action = Column(String(25), nullable=False)
    data = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=datetime.now, nullable=False)
    created_by = Column(Integer, Foreign<PERSON>ey("users.id"), nullable=True)

    case = relationship("Case", back_populates="audit_trails")
    case_step = relationship("CaseStep")
    case_step_document = relationship("CaseStepDocument")
    user = relationship("UserDB")

    def __repr__(self):
        return (f"<CaseAuditTrailDB(id={self.id}, case_id={self.case_id}, case_step_id={self.case_step_id}, "
                f"case_step_document_id={self.case_step_document_id}, action={self.action}, data={self.data}, "
                f"created_at={self.created_at}, created_by={self.created_by})>")
