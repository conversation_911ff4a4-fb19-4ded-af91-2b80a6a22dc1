from sqlalchemy import Column, <PERSON><PERSON><PERSON>, Integer, String, DateTime, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.schema import ForeignKeyConstraint

from app.database.database import Base
from datetime import datetime


class CaseStepDocument(Base):
    __tablename__ = "case_step_document"

    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    case_step_id = Column(Integer, ForeignKey(
        "case_step.id"), index=True)
    guid = Column(String(36), unique=True, index=True, nullable=True)
    file_name = Column(String(255), nullable=True)
    version= Column(String(5), nullable=True)
    comment = Column(String(1000), nullable=True)
    document_master_id = Column(
        Integer, ForeignKey('document_master.id'), index=True)
    is_required = Column(Boolean, default=True)
    status = Column(Integer, ForeignKey('status_master.id'), index=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now)
    uploaded_by = Column(Integer, ForeignKey('users.id'), index=True)

    case_step = relationship("CaseStep", back_populates="case_step_documents")
    document_master = relationship(
        "DocumentMaster", back_populates="case_step_documents")
    status_master = relationship(
        "StatusMaster", back_populates="case_step_documents")
    users = relationship("UserDB",back_populates="case_step_documents")
    __table_args__ = (
        ForeignKeyConstraint(["case_step_id"], ["case_step.id"]),
        ForeignKeyConstraint(["document_master_id"], ["document_master.id"]),
    )
