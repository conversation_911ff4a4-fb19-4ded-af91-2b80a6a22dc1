from enum import Enum
from app.database.database import Base
from sqlalchemy import (
    Column,
    Foreign<PERSON>ey,
    Integer,
    DateTime,
    ForeignKeyConstraint,
)

from sqlalchemy.orm import relationship



class CaseStep(Base):
    """Case step model."""

    __tablename__ = "case_step"

    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    case_id = Column(Integer, ForeignKey("case.id"), index=True)
    case_step_date = Column(DateTime, nullable=True)
    step_status_date = Column(DateTime, nullable=True)
    package_step_master_id = Column(
        Integer, ForeignKey("package_step_master.id"), index=True)
    status = Column(Integer, ForeignKey('status_master.id'),
                    index=True, nullable=True)
    case = relationship("Case", back_populates="case_steps")
    case_step_documents = relationship(
        "CaseStepDocument", back_populates="case_step")
    case_step_package_step_master = relationship(
        "PackageStepMasterDB", back_populates="case_step")
    status_master = relationship(
        "StatusMaster", back_populates="case_step")
    __table_args__ = (
        ForeignKeyConstraint(["case_id"], ["case.id"]),
        ForeignKeyConstraint(["package_step_master_id"],
                             ["package_step_master.id"]),
    )
