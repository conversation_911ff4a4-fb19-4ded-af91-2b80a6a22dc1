"""This module contains the exception errors case."""


class CaseNotFoundError:
    """
    Exception raised when the case is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "case_not_found"
    LOC = ["path", "case"]
    MSG = "Case not found."


class CasesNotFoundError:
    """
    Exception raised when the case is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "cases_not_found"
    LOC = ["path", "case"]
    MSG = "Cases not found."


class CaseAlreadyExistsError:
    """
    Exception raised when the case already exists.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "case_already_exists"
    LOC = ["path", "case"]
    MSG = "Case already exists."


class CaseAlreadExistsValueError(ValueError):
    """Exception raised when a case already exists."""

    def __init__(self):
        self.msg = "Case already exists"
        super().__init__(self.msg)


class CaseNotExistsValueError(ValueError):
    """Exception raised when a case does not exist."""

    def __init__(self):
        self.msg = "Case does not exist"
        super().__init__(self.msg)


class CaseNoAlreadyExistsError:

    TYPE = "govt_case_id_already_exists"
    LOC = ["body", "govt_case_id"]
    MSG = "Case number already exists."


class TreatmentDateBeforePreAuthError:
    TYPE = "treatment_date_before_pre_auth"
    LOC = ["body", "treatment_date"]
    MSG = "Treatment date cannot be before pre-auth date."


class DischargeDateBeforeTreatmentDateError:
    TYPE = "discharge_date_before_treatment_date"
    LOC = ["body", "discharge_date"]
    MSG = "Discharge date cannot be before treatment date."


class CaseAlreadyExistsWithSameHospitalFileNumberAndHospitalError:
    TYPE = "case_already_exists_with_same_hospital_file_number_and_hospital"
    LOC = ["body", "hospital_file_no"]
    MSG = "Case already exists with same hospital file number and hospital."


class SameTreatmentDateAlreadyExistsError:
    TYPE = "same_treatment_date_already_exists"
    LOC = ["body", "next_treatment_date"]
    MSG = "Same treatment date already exists for this case."


class StatusesNotFoundError:
    """
    Exception raised when the status is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "statuses_not_found"
    LOC = ["path", "status"]
    MSG = "Status not found."


class CasePatientRelativeNotFoundError:
    """
    Exception raised when the case patient relative is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "case_patient_relative_not_found"
    LOC = ["body", "case_patient_relative"]
    MSG = "Case patient relative not found."
