"""
This module contains custom exception classes for handling errors in the Arogya Yojana service.
"""


class CaseStepNotFoundError:
    """
    Exception raised when a case step master is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "case_step_not_found"
    LOC = ["path", "case_step_id"]
    MSG = "Case step not found"
