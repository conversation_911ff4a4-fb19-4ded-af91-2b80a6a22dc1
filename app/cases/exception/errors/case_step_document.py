"""
This module contains custom exception classes for handling errors in the Arogya Yojana service.
"""


class CaseStepDocumentNotFoundError:
    """
    Exception raised when a case step document is not found.

    Attributes:
        TYPE (str): The type of the error.
        LOC (list): The location of the error.
        MSG (str): The error message.
    """

    TYPE = "case_step_document_not_found"
    LOC = ["path", "case_step_doc_id"]
    MSG = "Case step document not found"
