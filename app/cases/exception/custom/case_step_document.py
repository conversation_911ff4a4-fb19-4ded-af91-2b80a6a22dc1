"""
This module contains classes for generating step master-related error messages.
"""

from utils.exception import CustomRequestValidationException

from ..errors.case_step_document import CaseStepDocumentNotFoundError


class CaseStepDocumentExceptions(CustomRequestValidationException):
    """A class that generates case step-related error messages."""

    def raise_case_step_document_not_found_exception(self, id):

        self.add_validation_error(
            loc=CaseStepDocumentNotFoundError.LOC,
            msg=CaseStepDocumentNotFoundError.MSG,
            type=CaseStepDocumentNotFoundError.TYPE,
            input=id,
        )

        self.raise_validation_exception()
