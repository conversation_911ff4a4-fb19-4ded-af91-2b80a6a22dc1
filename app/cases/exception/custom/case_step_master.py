"""
This module contains classes for generating step master-related error messages.
"""

from utils.exception import CustomRequestValidationException
from ..errors.case_step_master import CaseStepNotFoundError


class CaseStepExceptions(CustomRequestValidationException):
    """A class that generates case step-related error messages."""

    def raise_case_step_not_found_exception(self, id):
        
        self.add_validation_error(
            loc=CaseStepNotFoundError.LOC,
            msg=CaseStepNotFoundError.MSG,
            type=CaseStepNotFoundError.TYPE,
            input=id,
        )
        
        self.raise_validation_exception()
