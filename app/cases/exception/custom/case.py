from utils.exception import (
    CustomRequestValidationException,
)

from ..errors.case import *


class CaseExceptions(CustomRequestValidationException):

    def raise_cases_not_found_exception(self):
        self.add_validation_error(
            loc=CasesNotFoundError.LOC,
            msg=CasesNotFoundError.MSG,
            error_type=CasesNotFoundError.TYPE,
        )
        self.raise_validation_exception()

    def raise_case_not_found_exception(self, id):

        self.add_validation_error(
            loc=CaseNotFoundError.LOC,
            msg=CaseNotFoundError.MSG,
            error_type=CaseNotFoundError.TYPE,
            input_value=id,
        )
        self.raise_validation_exception()

    def raise_case_already_exists_exception(self, id):

        self.add_validation_error(
            loc=CaseAlreadyExistsError.LOC,
            msg=CaseAlreadyExistsError.MSG,
            error_type=CaseAlreadyExistsError.TYPE,
            input_value=id,
        )
        self.raise_validation_exception()

    def raise_govt_case_id_already_exists_exception(self, govt_case_id):
        """Raise a CaseNoAlreadyExistsError exception."""

        self.add_validation_error(
            loc=CaseNoAlreadyExistsError.LOC,
            msg=CaseNoAlreadyExistsError.MSG,
            error_type=CaseNoAlreadyExistsError.TYPE,
            input_value=govt_case_id,
        )
        self.raise_validation_exception()

    def raise_treatment_date_before_pre_auth_exception(self):

        self.add_validation_error(
            loc=TreatmentDateBeforePreAuthError.LOC,
            msg=TreatmentDateBeforePreAuthError.MSG,
            error_type=TreatmentDateBeforePreAuthError.TYPE,
        )
        self.raise_validation_exception()

    def raise_discharge_date_before_treatment_date_exception(self):

        self.add_validation_error(
            loc=DischargeDateBeforeTreatmentDateError.LOC,
            msg=DischargeDateBeforeTreatmentDateError.MSG,
            error_type=DischargeDateBeforeTreatmentDateError.TYPE,
        )
        self.raise_validation_exception()

    def raise_case_already_exists_with_same_hospital_file_number_and_hospital_exception(
        self, hospital_file_number
    ):

        self.add_validation_error(
            loc=CaseAlreadyExistsWithSameHospitalFileNumberAndHospitalError.LOC,
            msg=CaseAlreadyExistsWithSameHospitalFileNumberAndHospitalError.MSG,
            error_type=CaseAlreadyExistsWithSameHospitalFileNumberAndHospitalError.TYPE,
            input_value=hospital_file_number,
        )
        self.raise_validation_exception()

    def raise_same_treatment_date_already_exists_exception(self, treatment_date):

        self.add_validation_error(
            loc=SameTreatmentDateAlreadyExistsError.LOC,
            msg=SameTreatmentDateAlreadyExistsError.MSG,
            error_type=SameTreatmentDateAlreadyExistsError.TYPE,
            input_value=str(treatment_date),
        )
        self.raise_validation_exception()
        
    def raise_case_patient_relative_not_found_exception(self, id):

        self.add_validation_error(
            loc=CasePatientRelativeNotFoundError.LOC,
            msg=CasePatientRelativeNotFoundError.MSG,
            error_type=CasePatientRelativeNotFoundError.TYPE,
            input_value=id,
        )
        self.raise_validation_exception()


class StatusException(CustomRequestValidationException):
    def raise_statuses_not_found_exception(self):

        self.add_validation_error(
            loc=StatusesNotFoundError.LOC,
            msg=StatusesNotFoundError.MSG,
            error_type=StatusesNotFoundError.TYPE,
        )
        self.raise_validation_exception()


