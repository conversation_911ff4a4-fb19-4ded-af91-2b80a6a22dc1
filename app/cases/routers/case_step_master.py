from fastapi import APIRouter, Depends, Query, status
from fastapi.security import OA<PERSON>2P<PERSON>wordBearer
from sqlalchemy.orm import Session

from app.cases.services.case_step_master import CaseStepService
from app.cases.validations.case_step_master import (
    CaseStepCreate,
    CaseStepRetrievedResponse,
    CaseStepUpdate,
)

# Function to get a database session
from app.database.database import SessionL<PERSON>al, get_db
from app.services.user import AuthService
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler, ExtractUserAndAddToDict
from utils.schema import Error
from app.cases.helpers.logger import case_logger

# Create an API router
router = APIRouter()

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Create an instance of the CaseStepService
case_step_service = CaseStepService()


# Endpoint to get all case steps
# @router.get("/case-steps", response_model=CaseStepListResponse)
@router.get("/case-steps")
def get_all_case_steps(
    case_id: int = Query(),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    try:
        user = AuthService().get_user_from_token(token)
        kwargs = {"case_id": case_id, "user": user}
        case_steps = case_step_service.get_all_case_steps(db, **kwargs)
        return generate_response(
            data=case_steps,
            status_code=status.HTTP_200_OK,
            message="Case steps retrieved successfully",
        )
    except Exception as e:
        case_logger.error("Error retrieving case steps: %s", str(e), exc_info=True)
        return ExceptionHandler().handle_exception(e)


# Endpoint to get a specific case step by ID
# @router.get(
#     "/case-steps/{case_step_id}", response_model=Union[Error, CaseStepRetrievedResponse]
# )
@router.get("/case-steps/{case_step_id}")
def get_case_step(
    case_step_id: int,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    try:
        user = AuthService().get_user_from_token(token)
        kwargs = {"user": user}
        data = case_step_service.get_case_step_details_by_id(db, case_step_id, **kwargs)
        if data:
            return generate_response(
                data=data,
                status_code=status.HTTP_200_OK,
                message="Case step retrieved successfully",
            )
    except Exception as e:
        case_logger.error("Error retrieving case step details for ID %s: %s", case_step_id, str(e), exc_info=True)
        return ExceptionHandler().handle_exception(e)


# Endpoint to create a new case step
@router.post("/case-steps", response_model=CaseStepRetrievedResponse)
async def create_case_step(case_step: CaseStepCreate, db: Session = Depends(get_db)):
    try:
        created_case_step = case_step_service.create_case_step(case_step, db)
        return generate_response(
            data=created_case_step,
            status_code=status.HTTP_201_CREATED,
            message="Case step created successfully",
        )
    except Exception as e:
        case_logger.error("Error creating case step: %s", str(e), exc_info=True)
        return ExceptionHandler().handle_exception(e)


@router.patch("/case-steps/{case_step_id}/submit")
async def submit_case_step(
    case_step_id: int,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    try:
        case_step_data = {}
        case_step_data_new = (
            ExtractUserAndAddToDict.extract_user_and_add_to_dict_updated(
                token, case_step_data
            )
        )
        is_updated, updated_case_step = case_step_service.submit_case_step(
            db, case_step_id, case_step_data_new
        )
        if is_updated:
            return generate_response(
                status_code=status.HTTP_200_OK,
                message="Case-Stage level documents status updated successfully.",
            )
        else:
            return generate_response(custom_response=updated_case_step.dict())
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to update a case step by ID


@router.patch("/case-steps/{case_step_id}")
async def update_case_step(
    case_step_id: int,
    case_step_data: CaseStepUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    try:
        case_step_data_old = case_step_data.model_dump()
        case_step_data_new = (
            ExtractUserAndAddToDict.extract_user_and_add_to_dict_updated(
                token, case_step_data_old
            )
        )
        updated_case_step = case_step_service.update_case_step(
            db, case_step_id, case_step_data_new
        )
        return generate_response(
            status_code=status.HTTP_200_OK,
            message="Case step updated successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to delete a case step by ID
@router.delete("/case-steps/{case_step_id}")
async def delete_case_step(case_step_id: int, db: Session = Depends(get_db)):
    try:
        deleted_case_step = case_step_service.delete_case_step(case_step_id, db)
        return generate_response(
            status_code=status.HTTP_200_OK, message="Case step deleted successfully"
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# @router.get("/case-steps/approved-documents", response_model=CaseStepListResponse)
@router.get("/case-steps-approved-documents")
def get_approved_case_step_documents(
    case_id: int = Query(), db: Session = Depends(get_db)
):
    try:
        case_steps = case_step_service.get_stepwise_approved_documents(db, case_id)
        return generate_response(
            data=case_steps,
            status_code=status.HTTP_200_OK,
            message="Approved case step documents retrieved successfully",
        )
    except Exception as e:
        case_logger.error("Error retrieving approved case step documents for case ID %s: %s", case_id, str(e), exc_info=True)
        return ExceptionHandler().handle_exception(e)
