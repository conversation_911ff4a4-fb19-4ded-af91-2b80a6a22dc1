from datetime import datetime
from typing import List, Literal, Optional, Union

from fastapi import APIRouter, Depends, Query, status
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.orm import Session

from app.cases.services.case import CaseService, StatusMasterService
from app.cases.validations.case import (
    CaseC<PERSON>,
    CaseRetrievedResponse,
    CaseRetrieveResponse,
    CaseUpdate,
    NextTreatmentDateRequest,
)
from app.services.user import AuthService
from utils.common import get_db
from utils.generate_response import generate_response
from utils.helpers import Exception<PERSON>and<PERSON>, ExtractUserAndAddToDict

from ..helpers.logger import case_logger

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
import pytz

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


# Create an API router
router = APIRouter()

# Create an instance of the CaseService
case_service = CaseService()


# Endpoint to get all cases
# @router.get("/cases", response_model=CaseListResponse)
@router.get("/cases")
async def get_all_cases(
    patient_id: List[int] = Query(None),
    case_status: List[int] = Query(None),
    hospital_id: List[int] = Query(None),
    package_master_id: List[int] = Query(None),
    scheme_type_id: List[int] = Query(None),
    id: str = Query(None, min_length=1),
    govt_case_id: str = Query(None, min_length=3),
    claim_no: str = Query(None, min_length=3),
    patient_name: str = Query(None, min_length=3),
    hospital_file_no: str = Query(None, min_length=3),
    page: int = Query(1, gt=0),
    size: int = Query(10, gt=0),
    is_case_history_required: bool = Query(False),
    discharge: Literal["due", "overdue"] = Query(None),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    try:
        user = AuthService().get_user_from_token(token)
        assigned_hospital_ids = [hospital.id for hospital in user.hospitals]
        user_roles = [
            {"role_id": role.id, "role_name": role.name} for role in user.roles
        ]
        if hospital_id:
            hospital_id = [
                hospital_id
                for hospital_id in hospital_id
                if hospital_id in assigned_hospital_ids
            ]
        else:
            hospital_id = assigned_hospital_ids
        kwargs = {
            "id": id,
            "patient_id": patient_id,
            "status": case_status,
            "govt_case_id": govt_case_id,
            "hospital_id": hospital_id,
            "package_master_id": package_master_id,
            "claim_no": claim_no,
            "scheme_type_id": scheme_type_id,
            "patient_name": patient_name,
            "page": page,
            "size": size,
            "user_roles": user_roles,
            "is_case_history_required": is_case_history_required,
            "hospital_file_no": hospital_file_no,
            "discharge": discharge,
        }

        cases, total_count = case_service.get_all_cases(db, **kwargs)
        return generate_response(
            data=cases,
            total_count=total_count,
            status_code=status.HTTP_200_OK,
            message="Cases retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to get a specific case by ID
# @router.get("/cases/{case_id}")
@router.get("/cases/{case_id}", response_model=Union[CaseRetrievedResponse])
def get_case(
    case_id: int, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)
):
    try:
        user = AuthService().get_user_from_token(token)
        kwargs = {"user": user}
        content = case_service.get_case(db, case_id, **kwargs)
        return generate_response(
            data=content,
            status_code=status.HTTP_200_OK,
            message="Case retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Endpoint to create a new case
# @router.post("/cases", response_model=CaseRetrievedResponse, status_code=status.HTTP_201_CREATED)
@router.post("/cases", status_code=status.HTTP_201_CREATED)
async def create_case(
    case: CaseCreate, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)
):
    try:
        case_data_old = case.model_dump()
        case_data_new = ExtractUserAndAddToDict.extract_user_and_add_to_dict(
            token, case_data_old
        )
        created_case = case_service.create_case(case_data_new, db)
        return generate_response(
            status_code=status.HTTP_201_CREATED,
            message="Case created successfully,Kindly upload documents.",
        )
    except Exception as e:
        case_logger.error("An error occurred while creating a case: %s", e, exc_info=True)
        return ExceptionHandler().handle_exception(e)


# Endpoint to update a case by ID
@router.put("/cases/{case_id}")
async def update_case(
    case_id: int,
    case: CaseUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    case_logger.info("Entering update_case function")
    try:
        case_logger.info("Dumping case data")
        case_data_old = case.model_dump(exclude_unset=True)
        case_logger.info("Extracting user and adding to dict")
        case_data_new = ExtractUserAndAddToDict.extract_user_and_add_to_dict_updated(
            token, case_data_old
        )
        case_logger.info("Calling case_service.update_case function")
        is_updated, updated_case = case_service.update_case(case_id, case_data_new, db)
        case_logger.info("Case updated successfully")
        if is_updated:
            case_logger.info("Returning updated case data")
            return generate_response(
                status_code=status.HTTP_200_OK,
                message="Case updated successfully",
            )
        else:
            case_logger.info("Returning custom response")
            return generate_response(custom_response=updated_case.dict())
    except Exception as e:
        case_logger.error("An error occurred while updating the case: %s", e, exc_info=True)
        return ExceptionHandler().handle_exception(e)


# Endpoint to delete a case by ID
@router.delete("/cases/{case_id}")
async def delete_case(case_id: int, db: Session = Depends(get_db)):
    try:
        is_deleted = case_service.delete_case(case_id, db)
        return generate_response(
            status_code=status.HTTP_200_OK, message="Case deleted successfully"
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.get(
    "/status_master",
    responses={
        200: {"description": "Statuses retrieved successfully"},
        404: {"description": "Status not found"},
        500: {"description": "Internal server error"},
    },
)
def get_all_status(key: Optional[str] = None, db: Session = Depends(get_db)):
    try:
        statuses, total_count = StatusMasterService().get_all_status(db, key=key)
        return generate_response(
            data=statuses,
            total_count=total_count,
            status_code=status.HTTP_200_OK,
            message="Statuses retrieved successfully",
        )
    except Exception as e:
        case_logger.error("An error occurred while retrieving statuses: %s", e, exc_info=True)
        return ExceptionHandler().handle_exception(e)


@router.post("/cases/{case_id}/next-treatment-date")
def add_next_treatment_date(
    case_id: int,
    request: NextTreatmentDateRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    try:
        # Get the current user from the token
        current_user = AuthService().get_user_from_token(token)
        case_service = CaseService()
        updated_case = case_service.add_next_treatment_date(
            case_id=case_id,
            next_treatment_date=request.next_treatment_date,
            db=db,
            user_id=current_user.id,
        )
        return generate_response(
            data=updated_case,
            status_code=status.HTTP_200_OK,
            message="Next treatment date added successfully.",
        )
    except Exception as e:
        case_logger.error("An error occurred while adding next treatment date: %s", e, exc_info=True)
        return ExceptionHandler().handle_exception(e)


@router.get("/case/{case_id}", response_model=Union[CaseRetrieveResponse])
def get_case_details(case_id: int, db: Session = Depends(get_db)):
    """
    Retrieve a case's details by ID.

    Args:
        case_id (int): The ID of the case.
        db (Session): The database session.

    Returns:
        Union[CaseRetrieveResponse]: The response containing the case details.
    """
    try:
        # Retrieve the case details from the case service
        content = case_service.get_case_details(db, case_id)

        return generate_response(
            data=content,
            status_code=status.HTTP_200_OK,
            message="Case retrieved successfully",
        )
    except Exception as e:
        # Handle any exceptions and return an error response
        case_logger.error("An error occurred while retrieving case details: %s", e, exc_info=True)
        return ExceptionHandler().handle_exception(e)


# @router.get("/cases/{case_id}/treatment-dates", response_model=List[TreatmentDateListResponse])
@router.get("/cases/{case_id}/treatment-dates")
def fetch_treatment_dates(case_id: int, db: Session = Depends(get_db)):
    try:
        treatment_dates = case_service.get_treatment_dates(db, case_id)
        return generate_response(
            data=treatment_dates,
            status_code=status.HTTP_200_OK,
            message="Treatment dates retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.get("/server-time")
def get_server_time():
    # Get the current date and time from the server
    current_time = datetime.now()

    # Format the time to a more readable format (optional)
    formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")

    # Return the formatted time in the response
    return {"server_time": formatted_time}


@router.get("/cases/claim-paid/old")
def get_old_claim_paid_cases(
    threshold_days: int = Query(90, description="Number of days threshold (default 90)"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Retrieve cases with 'Claim Paid' status that are older than the threshold days
    and organize their document GUIDs by case steps.
    
    Args:
        threshold_days (int): The threshold in days. Defaults to 90.
        db (Session): The database session.
        token (str): Authentication token.
        
    Returns:
        JSON response with case information and document GUIDs organized by steps.
    """
    try:
        # Authenticate the user
        user = AuthService().get_user_from_token(token)
        
        # Get old claim paid cases
        cases = case_service.get_old_claim_paid_cases(db, threshold_days)
        
        return generate_response(
            data=cases,
            status_code=status.HTTP_200_OK,
            message=f"Retrieved {len(cases)} cases with 'Claim Paid' status older than {threshold_days} days"
        )
    except Exception as e:
        case_logger.error("An error occurred while retrieving old claim paid cases: %s", e, exc_info=True)
        return ExceptionHandler().handle_exception(e)


@router.post("/cases/claim-paid/{case_id}/backup")
def backup_claim_paid_case(
    case_id: int,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Backup documents for a specific case with 'Claim Paid' status.
    
    Args:
        case_id (int): The ID of the case to backup.
        db (Session): The database session.
        token (str): Authentication token.
        
    Returns:
        JSON response with backup result.
    """
    try:
        # Authenticate the user
        user = AuthService().get_user_from_token(token)
        
        # Backup the case
        result = case_service.backup_old_claim_paid_case(db, case_id, user.email)
        
        if result is None:
            return generate_response(
                status_code=status.HTTP_404_NOT_FOUND,
                message=f"Case with ID {case_id} not found or not in 'Claim Paid' status"
            )
        
        return generate_response(
            data=result,
            status_code=status.HTTP_200_OK,
            message=f"Successfully backed up documents for case ID {case_id}"
        )
    except Exception as e:
        case_logger.error(f"An error occurred while backing up case {case_id}: %s", e, exc_info=True)
        return ExceptionHandler().handle_exception(e)


@router.post("/cases/claim-paid/backup-all")
def backup_all_claim_paid_cases(
    threshold_days: int = Query(90, description="Number of days threshold (default 90)"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Backup documents for all cases with 'Claim Paid' status that are older than the threshold days.
    
    Args:
        threshold_days (int): The threshold in days. Defaults to 90.
        db (Session): The database session.
        token (str): Authentication token.
        
    Returns:
        JSON response with backup results summary.
    """
    try:
        # Authenticate the user
        user = AuthService().get_user_from_token(token)
        
        # Backup all old claim paid cases
        results = case_service.backup_all_old_claim_paid_cases(db, threshold_days, user.email)
        
        # Check if results contains the 'message' key, which indicates no cases were found
        if 'message' in results and 'total' in results and results['total'] == 0:
            return generate_response(
                data=results,
                status_code=status.HTTP_200_OK,
                message=results['message']
            )
        
        # Otherwise, format the message with successful and total counts
        return generate_response(
            data=results,
            status_code=status.HTTP_200_OK,
            message=f"Backup process completed for {results.get('successful', 0)} of {results.get('total', 0)} cases"
        )
    except Exception as e:
        case_logger.error("An error occurred while backing up old claim paid cases: %s", e, exc_info=True)
        return ExceptionHandler().handle_exception(e)

# Function to run the backup process automatically
def scheduled_backup_old_claim_paid_cases():
    db = get_db().__next__()  # Get a database session
    try:
        case_logger.info("Starting scheduled backup of old claim paid cases (threshold_days=90, username=system)")
        # Use system as the username and 90 days as the threshold
        results = case_service.backup_all_old_claim_paid_cases(db, threshold_days=90, username="system")
        
        if 'message' in results and 'total' in results and results['total'] == 0:
            case_logger.info("No old claim paid cases found to backup")
        else:
            case_logger.info(f"Backup process completed for {results.get('successful', 0)} of {results.get('total', 0)} cases")
            
    except Exception as e:
        case_logger.error(f"An error occurred during scheduled backup of old claim paid cases: {e}", exc_info=True)
    finally:
        db.close()

# Configure the scheduler
timezone = pytz.timezone("Asia/Kolkata")
backup_scheduler = BackgroundScheduler()
# Schedule to run daily at 12:00 AM (midnight)
backup_scheduler.add_job(
    scheduled_backup_old_claim_paid_cases, 
    CronTrigger(hour=0, minute=0, timezone=timezone),
    id='backup_old_claim_paid_cases'
)
# Start the scheduler
backup_scheduler.start()
