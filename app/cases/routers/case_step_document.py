from typing import Union

from fastapi import APIRouter, Depends, status
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.orm import Session

from app.cases.services.case_step_document import CaseStepDocumentService
from app.cases.validations.case_step_master import (
    CaseStepDocumentApprove,
    CaseStepDocumentReject,
    CaseStepDocumentResponse,
    CaseStepDocumentRetrieveResponse,
    CaseStepDocumentUpdate,
    CaseStepDocumentUpdateBase,
)

# Function to get a database session
from app.database.database import SessionLocal, get_db
from app.services.user import AuthService
from utils.generate_response import generate_response
from utils.helpers import Exception<PERSON>andler, ExtractUserAndAddToDict

# Create an API router
router = APIRouter()

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Create an instance of the CaseStepDocumentService
case_step_document_service = CaseStepDocumentService()


# Define the route to submit a document
@router.post(
    "/case-step-documents/{case_step_document_id}/submit",
    status_code=status.HTTP_200_OK,
)
def submit_case_step_document(
    case_step_document_id: int,
    case_step_document: CaseStepDocumentUpdateBase,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    """
    Submit a case step document by ID.

    Args:
        case_step_document_id (int): The ID of the case step document.
        db (Session, optional): The database session. Defaults to Depends(get_db).
        token (str, optional): The OAuth2 token. Defaults to Depends(oauth2_scheme).

    Returns:
        Union[dict, Response]: The updated case step document if successful, or a response with an error message if not found.

    """
    try:
        case_step_doc_old = case_step_document.dict()
        case_step_doc_new = (
            ExtractUserAndAddToDict.extract_user_and_add_to_dict_updated(
                token, case_step_doc_old
            )
        )
        user = AuthService().get_user_from_token(token)
        case_step_doc_new["uploaded_by"] = user.id
        updated_case_step = case_step_document_service.submit_case_step_document(
            db, case_step_document_id, case_step_doc_new
        )
        return generate_response(
            data=updated_case_step,
            status_code=status.HTTP_200_OK,
            message="Case step document submitted successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Define the route to approve a document
@router.post(
    "/case-step-documents/{case_step_document_id}/approve",
    status_code=status.HTTP_200_OK,
)
def approve_case_step_document(
    case_step_document_id: int,
    data: CaseStepDocumentApprove,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    """
    Approve a case step document by ID.

    Args:
        case_step_document_id (int): The ID of the case step document.
        db (Session, optional): The database session. Defaults to Depends(get_db).
        token (str, optional): The OAuth2 token. Defaults to Depends(oauth2_scheme).

    Returns:
        Union[dict, Response]: The updated case step document if successful, or a response with an error message if not found.

    """
    try:
        user = AuthService().get_user_from_token(token)
        data = case_step_document_service.approve_case_step_document(
            db, case_step_document_id, data=data, user=user
        )
        return generate_response(
            data=data,
            status_code=status.HTTP_200_OK,
            message="Case step document approved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Define the route to reject a document
@router.post(
    "/case-step-documents/{case_step_document_id}/reject",
    status_code=status.HTTP_200_OK,
)
def reject_case_step_document(
    case_step_document_id: int,
    data: CaseStepDocumentReject,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    """
    Reject a case step document by ID.

    Args:
        case_step_document_id (int): The ID of the case step document.
        db (Session, optional): The database session. Defaults to Depends(get_db).
        token (str, optional): The OAuth2 token. Defaults to Depends(oauth2_scheme).

    Returns:
        Union[dict, Response]: The updated case step document if successful, or a response with an error message if not found.

    """
    try:
        user = AuthService().get_user_from_token(token)
        data = case_step_document_service.reject_case_step_document(
            db, case_step_document_id, data=data, user=user
        )
        return generate_response(
            status_code=status.HTTP_200_OK,
            message="Case step document rejected successfully",
            data=data,
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Define the route to get a case step document by ID
@router.get(
    "/case_step_documents/{case_step_document_id}",
    status_code=status.HTTP_200_OK,
    response_model=CaseStepDocumentRetrieveResponse,
)
def get_case_step_document(
    case_step_document_id: int,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    """
    Get a case step document by ID.

    Args:
        case_step_document_id (int): The ID of the case step document.
        db (Session, optional): The database session. Defaults to Depends(get_db).
        token (str, optional): The OAuth2 token. Defaults to Depends(oauth2_scheme).

    Returns:
        Union[dict, Response]: The case step document if found, or a response with an error message if not found.

    """
    try:
        # Call the service method to get the case step document
        data = case_step_document_service.get_case_step_document_by_id(
            case_step_document_id, db
        )
        if data:
            return generate_response(
                data=data,
                status_code=status.HTTP_200_OK,
                message="Case step document retrieved successfully",
            )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


# Define the route to update a case step document
@router.patch(
    "/case-step-documents/{case_step_document_id}", status_code=status.HTTP_200_OK
)
def update_case_step_document(
    case_step_document_id: int,
    case_step_document: CaseStepDocumentUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
):
    """
    Update a case step document by ID.

    Args:
        case_step_document_id (int): The ID of the case step document.
        case_step_document (CaseStepDocumentUpdate): The updated case step document data.
        db (Session, optional): The database session. Defaults to Depends(get_db).
        token (str, optional): The OAuth2 token. Defaults to Depends(oauth2_scheme).

    Returns:
        Union[dict, Response]: The updated case step document if successful, or a response with an error message if not found.

    """
    try:
        case_step_doc_old = case_step_document.dict()
        case_step_doc_new = (
            ExtractUserAndAddToDict.extract_user_and_add_to_dict_updated(
                token, case_step_doc_old
            )
        )
        is_updated, updated_case_step = (
            case_step_document_service.update_case_step_document(
                db, case_step_document_id, case_step_doc_new
            )
        )
        if is_updated:
            return generate_response(
                data=updated_case_step,
                status_code=status.HTTP_200_OK,
                message="Case step document updated successfully",
            )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)
