from datetime import datetime
from typing import List, Optional

import emoji
from pydantic import (
    BaseModel,
    ValidationError,
    condecimal,
    field_validator,
    root_validator,
    validator,
)

from app.cases.models.case import Case
from app.master.models.package import PackageDB
from app.master.services.hospital import HospitalDB
from app.master.validations.category import CategoryBaseReadResponse
from app.master.validations.sub_category import SubCategoryBaseReadResponse
from app.patients.models.patient import PatientDB
from app.patients.validations.patient import AddressReadResponse, PatientBaseResponse
from app.validations.base import CustomBaseModel
from utils.response import BaseResponse


class NoEmojiBaseModel(CustomBaseModel):
    @validator("*", pre=True, always=True)
    def check_no_emojis(cls, value):
        if isinstance(value, str):
            # Check for emojis
            if emoji.emoji_count(value) > 0:
                raise ValueError("Emojis are not allowed.")
        return value


class CaseBase(NoEmojiBaseModel):
    """
    Base class for creating a case.
    """

    govt_case_id: Optional[str] = None
    hospital_file_no: Optional[str] = None
    pre_auth_date: Optional[datetime] = None
    treatment_date: Optional[datetime] = None
    discharge_date: Optional[datetime] = None
    relative_name: str
    relative_contact: str

    @validator("govt_case_id")
    def govt_case_id_must_be_valid_string(cls, v, values):
        if (
            v and not v.strip()
        ):  # Check if the value is empty or contains only whitespace
            raise ValueError("Case number cannot contain only spaces.")
        elif v and len(v) > 100:
            raise ValueError(f"Case number must not exceed 100 characters.")
        elif v and (v.strip() != v or v.lstrip() != v or v.rstrip() != v):
            raise ValueError(f"Case number cannot have spaces in start or end.")
        return v

    @validator("treatment_date")
    def validate_treatment_date(cls, v, values):
        """
        Check if the treatment_date is valid.
        """
        if (
            v
            and values.get("pre_auth_date")
            and v.date() < values["pre_auth_date"].date()
        ):
            raise ValueError("Treatment date cannot be before pre-authorization date.")
        return v

    @validator("discharge_date")
    def validate_discharge_date(cls, v, values):
        """
        Check if the discharge_date is valid.
        """
        if (
            v
            and values.get("treatment_date")
            and v.date() < values["treatment_date"].date()
        ):
            raise ValueError("Discharge date cannot be before treatment date.")
        return v

    @field_validator("relative_name", "relative_contact", mode="before")
    def check_empty_spaces(cls, v, info):
        if (
            v and not v.strip()
        ):  # Check if the value is empty or contains only whitespace
            raise ValueError(
                f"{info.field_name.replace('_', ' ').capitalize()} cannot contain only spaces."
            )
        return v

    @validator("hospital_file_no")
    def check_hospital_file_no(cls, v):
        if v and len(v) > 100:
            raise ValueError(f"Hospital file number must not exceed 100 characters.")
        elif v and (v.strip() != v or v.lstrip() != v or v.rstrip() != v):
            raise ValueError(
                f"Hospital file number cannot have spaces in start or end."
            )
        return v

    @validator("relative_contact")
    def validate_relative_contact(cls, v):
        if not v.isdigit() or len(v) != 10:
            raise ValueError("Relative contact must be a 10-digit number.")
        return v


class CaseCreate(CaseBase):
    """
    Represents a schema for creating a case.
    """

    patient_id: int
    package_master_id: int
    hospital_id: int
    case_status_date: Optional[datetime] = None

    @validator("govt_case_id")
    def govt_case_id_must_be_unique(cls, v):
        """Ensure that the govt_case_id is unique."""
        cls.check_object_exists(
            verbose_name="Case", field="govt_case_id", value=v, model=Case
        )
        return v

    @validator("hospital_id")
    def check_hospital_exists(cls, v):
        """
        Check if the hospital exists.
        """
        cls.check_object_exists(
            verbose_name="Hospital", field="id", value=v, model=HospitalDB
        )
        return v

    @validator("patient_id")
    def check_patient_exist(cls, v):
        """
        Check if the patient exists.
        """
        cls.check_foreign_key_exists(
            verbose_name="Patient", field="id", value=v, model=PatientDB
        )
        return v

    @validator("package_master_id")
    def check_package_master_exists(cls, v):
        """
        Check if the package master exists.
        """
        cls.check_foreign_key_exists(
            verbose_name="Package Master", field="id", value=v, model=PackageDB
        )
        return v


class CaseUpdate(CaseBase):
    """
    Represents a schema for updating a case.
    """

    claim_no: Optional[str] = None
    pre_auth_approved_amount: Optional[int] = None
    pre_auth_status_date: Optional[datetime] = None
    claim_status_date: Optional[datetime] = None
    case_status_date: Optional[datetime] = None
    status: Optional[int] = None

    class Config:
        extra = "forbid"

    @field_validator(
        "relative_name", "relative_contact", "hospital_file_no", mode="before"
    )
    def check_empty_spaces(cls, v, info):
        if (
            v and not v.strip()
        ):  # Check if the value is empty or contains only whitespace
            raise ValueError(
                f"{info.field_name.replace('_', ' ').capitalize()} cannot contain only spaces."
            )

        return v

    @validator("claim_no")
    def check_claim_no(cls, v):
        if (
            v and not v.strip()
        ):  # Check if the value is empty or contains only whitespace
            raise ValueError("Claim number cannot contain only spaces.")
        if v and len(v) > 100:
            raise ValueError(f"Claim number must not exceed 100 characters.")
        elif v and (v.strip() != v or v.lstrip() != v or v.rstrip() != v):
            raise ValueError(f"Claim number cannot have spaces in start or end.")
        return v


class CaseOut(BaseModel):
    id: int
    govt_case_id: str
    patient_name: str
    procedure_code: str
    procedure_name: str
    scheme_type: str
    package_amount: int
    category: str
    sub_category: Optional[str]
    pre_auth_approved_amount: Optional[int]
    claim_no: Optional[str]
    status: str

    class Config:
        from_attributes = True


class CaseListResponse(BaseResponse):
    """Represents a case list response schema."""

    data: Optional[List[CaseOut]] = None


# For Get Case By id API Response


class PackageMaster(BaseModel):
    id: int
    procedure_code: str
    procedure_name: str
    package_amount: int
    categories: Optional[CategoryBaseReadResponse] = []
    sub_categories: Optional[SubCategoryBaseReadResponse] = []

    @root_validator(pre=True)
    def combine_codes(cls, values):
        scheme_type = values.categories.scheme_type
        if scheme_type and scheme_type.id == 1:
            v = values.procedure_code
            category_code = values.categories.code
            sub_category_code = values.sub_categories.code
            combination_code = f"{category_code}{sub_category_code}.{v}"
            values.procedure_code = combination_code
        return values


class Patient(PatientBaseResponse):
    address: List[AddressReadResponse] = []


# class CaseResponse(CaseBase):
#     """Represents a schema for a case response."""

#     id: int
#     pre_auth_approved_amount: Optional[int] = None
#     claim_no: Optional[str] = None
#     package: PackageMaster
#     patient: Patient
#     case_steps: Optional[List[CaseStepResponse]] = None


# class CaseRetrievedResponse(BaseResponse):
#     """Represents a case retrieved response schema."""

#     data: CaseResponse


class Base(BaseModel):
    id: int
    name: str


class HospitalBase(Base):
    """Base class for hospital master data."""

    short_name: Optional[str] = None
    latitude: Optional[
        condecimal(max_digits=18, decimal_places=15)
    ] = None  # type: ignore
    longitude: Optional[
        condecimal(max_digits=18, decimal_places=15)
    ] = None  # type: ignore


class StatusMaster(Base):
    """Base class for status master data."""

    pass


class SchemeTypeMaster(BaseModel):
    """Base class for scheme type master data."""

    id: int
    type: str


class CasePatientRelativeMappingSchema(BaseModel):

    relative_name: str
    relative_contact: str

    class Config:
        from_attributes = True


class CaseResponse(BaseModel):
    package_master_id: int
    hospital_file_no: Optional[str] = None
    claim_no: Optional[str] = None
    pre_auth_approved_amount: Optional[int] = None
    govt_case_id: Optional[str] = None
    patient: Patient
    package: PackageMaster
    hospital: HospitalBase
    case_status: StatusMaster
    relative: Optional[CasePatientRelativeMappingSchema]
    scheme: SchemeTypeMaster

    class Config:
        from_attributes = True


class CaseSchema(CaseResponse):
    id: int
    pre_auth_date: Optional[datetime] = None
    treatment_date: Optional[datetime] = None
    discharge_date: Optional[datetime] = None
    pre_auth_status_date: Optional[datetime] = None
    claim_status_date: Optional[datetime] = None
    case_status_date: Optional[datetime] = None
    stepstatus: Optional[StatusMaster] = None
    is_any_treatment_document_uploaded: Optional[bool] = None
    is_any_discharge_document_uploaded: Optional[bool] = None

    class Config:
        from_attributes = True


class DocumentBase(BaseModel):
    guid: Optional[str]
    document_name: str
    is_required: bool
    is_geotag_required: bool


class CaseStepDocument(DocumentBase):
    id: int
    status: StatusMaster
    file_name: Optional[str]
    version: Optional[str]
    comment: Optional[str]

    class Config:
        from_attributes = True


class CaseStepBase(BaseModel):
    position: int
    name: str
    case_step_date: Optional[datetime]
    status: Optional[str]


class CaseStep(CaseStepBase):
    id: int
    step_status_date: Optional[datetime]
    documents: List[CaseStepDocument]
    status: Optional[StatusMaster] = None
    is_any_document_uploaded: Optional[bool] = None

    class Config:
        from_attributes = True


class CaseResponse(BaseModel):
    case: CaseSchema
    steps: List[CaseStep]

    class Config:
        from_attributes = True


class CaseRetrievedResponse(BaseResponse):
    """Represents a case retrieved response schema."""

    data: CaseResponse


class NextTreatmentDateRequest(BaseModel):
    next_treatment_date: datetime

    @validator("next_treatment_date")
    def next_treatment_date_cannot_be_in_the_past(cls, v, values):
        today = datetime.today().date()
        if v.date() < today:
            raise ValueError("Next treatment date cannot be in the past.")
        return v

    class Config:
        from_attributes = True


class CaseRetrieveResponse(BaseResponse):
    data: CaseSchema


class TreatmentDateResponse(BaseModel):
    case_step_id: int
    treatment_date: datetime

    class Config:
        from_attributes = True


class TreatmentDateListResponse(BaseResponse):
    data: TreatmentDateResponse
