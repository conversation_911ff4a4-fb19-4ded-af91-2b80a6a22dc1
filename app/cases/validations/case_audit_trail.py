from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, validator


class CaseAuditTrailSchema(BaseModel):
    """Represents a case step document base schema."""
        
    case_id: int
    case_step_id: Optional[int] = None
    case_step_document_id: Optional[int] = None
    action: str
    data: Optional[str] = None
    created_by: Optional[int] = None
    created_at: Optional[datetime] = None
