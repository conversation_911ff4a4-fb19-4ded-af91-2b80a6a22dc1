from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, validator

from app.cases.models import StatusMaster
from app.master.models.package import StepMaster
from app.master.models.scheme_type import SchemeType
from app.validations.base import CustomBaseModel
from utils.response import BaseResponse


class CaseStepBase(CustomBaseModel):
    """Represents a case step base schema."""

    step_master_id: int
    scheme_id: int

    class Config:
        from_attributes = True


class CaseStepCreate(CaseStepBase):
    """Represents a case step creation schema."""

    @validator("step_master_id")
    def step_master_id_must_exist(cls, step_master_id):
        """Validates if the step master ID exists."""

        cls.check_foreign_key_exists(
            verbose_name="Step Master",
            field="id",
            value=step_master_id,
            model=StepMaster,
        )
        return step_master_id

    @validator("scheme_id")
    def scheme_id_must_exist(cls, scheme_id):
        """Validates if the scheme ID exists."""

        cls.check_foreign_key_exists(
            verbose_name="Scheme Type", field="id", value=scheme_id, model=SchemeType
        )

        return scheme_id


class CaseStepUpdate(CustomBaseModel):
    status: Optional[int] = None
    case_step_date: Optional[datetime] = None

    @validator("case_step_date")
    def case_step_date_must_not_be_in_the_past(cls, case_step_date):
        """Validates if the case step date is not in the past."""
        if case_step_date and case_step_date.date() < datetime.now().date():
            raise ValueError("Case step date cannot be in the past.")
        return case_step_date

    @validator("status")
    def status_must_exist(cls, status):
        """Validates if the status exists."""

        cls.check_foreign_key_exists(
            verbose_name="Status Master", field="id", value=status, model=StatusMaster
        )
        return status


class CaseStepDocumentUpdateBase(BaseModel):
    """Represents a case step document base schema."""

    guid: Optional[str] = None
    file_name: Optional[str] = None
    version: Optional[str] = None


class CaseStepDocumentUpdate(BaseModel):
    """Represents a case step document base schema."""

    guid: Optional[str] = None
    status: Optional[int] = None


class StatusMaster(BaseModel):
    """Represents a status master schema."""

    id: int
    name: str


class CaseStepDocumentResponse(BaseModel):
    """Represents a case step document response schema."""

    id: int
    guid: Optional[str] = None
    document_name: str
    is_required: bool
    is_geotag_required: bool
    status: Optional[StatusMaster] = None


class CaseStepResponse(BaseModel):
    """Represents a case step response schema."""

    id: int
    position: int
    name: str
    case_step_date: Optional[datetime] = None
    case_status_date: Optional[datetime] = None
    documents: List[CaseStepDocumentResponse]
    status: Optional[StatusMaster] = None


class CaseStepRetrievedResponse(BaseResponse):
    """Represents a case step retrieved response schema."""

    data: CaseStepResponse


class CaseStepListResponse(BaseResponse):
    """Represents a case step list response schema."""

    data: Optional[List[CaseStepResponse]] = None


class CaseStepDocumentApprove(BaseModel):
    """Represents a case step document reject schema."""

    comment: Optional[str] = None

    @validator("comment")
    def comment_must_not_be_empty(cls, v):
        if v and not v.strip():
            raise ValueError("Comment cannot be empty.")
        if v and len(v) > 1000:
            raise ValueError("Comment must not exceed 1000 characters.")
        return v


class CaseStepDocumentReject(BaseModel):
    """Represents a case step document reject schema."""

    comment: str

    @validator("comment")
    def comment_must_not_be_empty(cls, v):
        if v and not v.strip():
            raise ValueError("Comment cannot be empty.")
        if v and len(v) > 1000:
            raise ValueError("Comment must not exceed 1000 characters.")
        return v


class CaseStepDocumentRetrieveResponse(BaseResponse):
    data: CaseStepDocumentResponse
