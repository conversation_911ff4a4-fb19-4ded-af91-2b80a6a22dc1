from utils.exception import CustomExceptionDetailsBase


class NotificationsNotFoundError:

    TYPE = "notifications_not_found"
    LOC = ["", ""]
    MSG = "No notifications found."


class NotificationExceptions(CustomExceptionDetailsBase):

    def raise_notifications_not_found_exception(self):
        self.add_detail(
            NotificationsNotFoundError.TYPE,
            NotificationsNotFoundError.LOC,
            NotificationsNotFoundError.MSG,
            None
        )
        self.raise_not_found_exception()
