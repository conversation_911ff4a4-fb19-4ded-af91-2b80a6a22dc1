GET_CASE_ID_BY_USER_ID = """
SELECT c.id AS case_id
FROM `case` c
JOIN user_hospital uh ON c.hospital_id = uh.hospital_id
WHERE
"""

GET_CASE_STEP_PENDING_TREATMENT_AND_DISCHARGE = """
            SELECT *
            FROM case_step
            WHERE case_step_date >= CURDATE()
                AND case_step_date < DATE_ADD(CURDATE(), INTERVAL 1 DAY)
                AND status IN (3, 7, 11);
    """
    
FETCH_CASE_STEPS = """
                        SELECT *
                        FROM notifications
                        WHERE case_id = :case_id
                        AND status_id = :status_id
                        LIMIT 1;
                    """
                    
FETCH_ROLE = """
                        SELECT id
                        FROM roles
                        WHERE name = "Data Entry Operator"
                        LIMIT 1;
                    """
                    
FETCH_STATUS_NAME = """
                        SELECT name
                        FROM status_master
                        WHERE id = :status_id
                        LIMIT 1;
                    """
                    
CREATE_PENDING_NOTIFICATIONS = """
                            INSERT INTO notifications (
                                case_id,
                                case_step_id,
                                notification_info,
                                notification_status,
                                status_id,
                                role_id,
                                created_at

                            )
                            VALUES (
                                :case_id,
                                :case_step_id,
                                :notification_info,
                                :notification_status,
                                :status_id,
                                :role_id,
                                NOW()

                            )
                    """