import pandas as pd
import datetime
from apscheduler.schedulers.blocking import BlockingScheduler
from apscheduler.triggers.cron import CronTrigger
import pytz



# Get today's date
today = datetime.date.today()
formatted_date = today.strftime("%Y-%m-%d")

def get_data(table_name, engine):
    query = "SELECT * FROM {}".format(table_name)

    # Execute the query and load the data into a DataFrame
    data = pd.read_sql_query(query, engine)

    # Display the DataFrame
    return data


def generate_notifications(engine, formatted_date):
    case = get_data('`case`', engine)
    ''' Check the notification master whether we have the notification for the case or not.'''
    notification_master = get_data('notification_master', engine)
    notification_master.drop(columns=['id'], inplace=True)
    
    case = case.dropna(subset=['status'])
    case_df = case[['id', 'status', 'updated_by']]
    case_df = case_df.rename(columns={'id': 'case_id','status':'case_status'})

    '''
        Check this status is present in notification_master or not.
        If status not present then remove that status from case_df.
    '''
    status_df = case_df.merge(notification_master, left_on='case_status', right_on='status_id', how='left')

    # Filter out rows where status_id is NaN (i.e., unmatched rows)
    case_dataframe = status_df[~status_df['status_id'].isna()]

    # Drop the extra columns added during the merge
    case_dataframe = case_dataframe.drop(columns=['status_id', 'role_id'])
    
    '''Get the status name from status_master table'''
    query = "SELECT id, name FROM status_master"
    status_data = pd.read_sql_query(query, engine)
    
    ''' Get the status name from status_master table and merge it with case_dataframe'''
    case_df_new = case_df.merge(status_data, left_on='case_status', right_on='id', how='left')

    # Rename the 'name' column to 'status'
    case_df_new = case_df_new.rename(columns={'name': 'step_status'}).drop(['id'], axis=1)
    
    ''' Get the role name from role_master table'''
    query = "SELECT user_id,role_id FROM user_role"
    user_role_df = pd.read_sql_query(query, engine)
    
    ''' Merge the user_role_df with case_df_new to get the role name of the user who updated the case.'''
    case_df_new = case_df_new.merge(user_role_df, left_on='updated_by', right_on='user_id', how='left')
    
    unique_case_ids = case_df_new['case_id'].unique()
    
    ''' Get the step who is case_step date is matching with current date'''

    query = "SELECT * FROM case_step"
    case_step_dates = pd.read_sql_query(query, engine)

    # Convert case_step_date and case_status_date to datetime format
    case_step_dates['case_step_date'] = pd.to_datetime(case_step_dates['case_step_date'])
    case_step_dates['case_status_date'] = pd.to_datetime(case_step_dates['case_status_date'])

    filtered_case_step_dates = case_step_dates[case_step_dates['case_id'].isin(unique_case_ids)]


    ''' Filter the DataFrame based on today's date. '''
    filtered_case_step_dates = filtered_case_step_dates[(filtered_case_step_dates['case_step_date'].dt.date == today) | (filtered_case_step_dates['case_status_date'].dt.date == today)]

    filtered_case_step_dates['status'] = filtered_case_step_dates['status'].astype(int)

    filtered_case_step_dates = filtered_case_step_dates.rename(columns={'id': 'case_step_id'})

    filtered_case_step_dates = filtered_case_step_dates.merge(status_data, left_on='status', right_on='id', how='left')

    ''' Create Final DataFrame by merging the filtered_case_step_dates with case_df_new'''
    case_dataframe = case_df_new[['case_id','case_status','user_id','role_id']]
    case_dataframe = case_dataframe.rename(columns={'case_status': 'status_id'})
    merged_df = pd.merge(filtered_case_step_dates, case_dataframe, on='case_id', how='left')

    ''' Rename the columns '''
    merged_df = merged_df[['case_id','status_id', 'case_step_id', 'name','role_id']].rename(columns={'name': 'notification_info'})
    merged_df['notification_status'] = 'Open'
    merged_df['created_at'] = formatted_date
    
    
    ''' Save the data into notification table'''
    merged_df.to_sql('notifications', con=engine, if_exists='append', index=False)
    
# Create an instance of the scheduler
scheduler = BlockingScheduler()

# Define the timezone
# timezone = pytz.timezone('Asia/Kolkata')

# Schedule the job to run daily at 12:00 AM in the Asia/Kolkata timezone
trigger = CronTrigger(hour=0, minute=0, timezone=timezone)
scheduler.add_job(generate_notifications, trigger, args=(engine, formatted_date))


query = ''' SELECT
    sm.type AS step_type,
    sm.id as step_id,
    cs.case_id,
    cs.id as case_step_id,
    cs.status AS case_step_status,
    csd.guid AS document_guid,
    csd.document_master_id,
    csd.is_required,
    csd.status AS document_status,
    csd.created_at AS document_created_at,
    csd.updated_at AS document_updated_at
FROM
    case_step cs
JOIN
    package_step_master psm ON cs.package_step_master_id = psm.id
JOIN
    step_master sm ON psm.step_id = sm.id
JOIN
    case_step_document csd ON cs.id = csd.case_step_id
WHERE
    cs.case_id = 30
ORDER BY
    sm.order, csd.created_at;
'''

closed_ids = []

def close_pending_notifications(engine, query):
    df1 = get_data('notifications', engine)
    document_status = pd.read_sql(query, engine)
    df2 = document_status

    if len(df1) > 0 and len(df2):
        
        def check_status(case_step_id):
            filtered_df = df2[df2['case_step_id'] == case_step_id]
            if not filtered_df.empty:
                if any((filtered_df['document_status'] == 22) & (filtered_df['document_guid'].isna())):
                    return 'Open'
                if all(filtered_df['document_status'] == 23) and not any(filtered_df['document_guid'].isna()):
                    closed_ids.append(case_step_id)
                    
                    return 'Closed'

            return 'Open'

        # Apply the function to df1
        df1['notification_status'] = df1['case_step_id'].apply(check_status)

        # df1.to_sql('notifications', con=engine, if_exists='replace', index=False)
        # Update the SQL table for each row that needs updating
        with engine.connect() as conn:
            for index, row in df1.iterrows():
                stmt = f"""
                UPDATE notifications
                SET notification_status = '{row['notification_status']}'
                WHERE case_step_id = {row['case_step_id']}
                """
                conn.execute(stmt)
                
        # Create a new row if any row is marked as 'Closed'
        new_rows = []

        for index, row in df1.iterrows():
            if row['case_step_id'] in closed_ids:
                step_type = df2[df2['case_step_id'] == row['case_step_id']]['step_type'].iloc[0]
                new_row = row.copy()
                new_row['notification_info'] = f"{step_type} Submitted"
                new_row['notification_status'] = 'Open'
                new_row['created_at'] = formatted_date
                new_row['role_id'] = 4
                new_rows.append(new_row)
        

        # Append the new rows to df1
        if new_rows:
            new_rows_df = pd.DataFrame(new_rows)
        new_rows_df.drop(columns=['id'], inplace=True)
        new_rows_df.to_sql('notifications', con=engine, if_exists='append', index=False)
  
closed_rejected_ids = []      
def close_rejected_notifications(engine, query):
    df1 = get_data('notifications', engine)
    document_status = pd.read_sql(query, engine)
    df2 = document_status

    if len(df1) > 0 and len(df2):
                
        # Function to check the conditions

        def check_status(case_step_id):
            filtered_df = df2[df2['case_step_id'] == case_step_id]
            if not filtered_df.empty:
        
                if all(filtered_df['document_status'].isin([25])) and not any(filtered_df['document_guid'].isna()):
                    closed_rejected_ids.append(case_step_id)

                    return 'Closed'
            return 'Open'
        df1['notification_status'] = df1['case_step_id'].apply(check_status)
        df1
        with engine.connect() as conn:
            for index, row in df1.iterrows():
                stmt = f"""
                UPDATE notifications
                SET notification_status = '{row['notification_status']}'
                WHERE case_step_id = {row['case_step_id']}
                """
                conn.execute(stmt)

 
closed_approved_ids = []      
def close_approved_notifications(engine, query):
    df1 = get_data('notifications', engine)
    document_status = pd.read_sql(query, engine)
    df2 = document_status

    if len(df1) > 0 and len(df2):
                
        # Function to check the conditions

        def check_status(case_step_id):
            filtered_df = df2[df2['case_step_id'] == case_step_id]
            if not filtered_df.empty:
        
                if all(filtered_df['document_status'].isin([24])) and not any(filtered_df['document_guid'].isna()):
                    closed_approved_ids.append(case_step_id)

                    return 'Closed'
            return 'Open'
        df1['notification_status'] = df1['case_step_id'].apply(check_status)
        df1
        with engine.connect() as conn:
            for index, row in df1.iterrows():
                stmt = f"""
                UPDATE notifications
                SET notification_status = '{row['notification_status']}'
                WHERE case_step_id = {row['case_step_id']}
                """
                conn.execute(stmt)
