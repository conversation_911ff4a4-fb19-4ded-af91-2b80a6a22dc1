from datetime import timedelta

from app.cases.models.case import Case
from app.database.database import Base, SessionLocal, engine
from app.models.authorisation import RoleDB, UserRoleDB
from app.notification.execute.notification import get_notification_for_user
from app.notification.models.notification import NotificationDB, NotificationMasterDB
from app.patients.models.patient import PatientDB
from app.services.authorisation.user_roles import UserRolesService
from sqlalchemy.orm import Session
from utils.db import BaseService
from utils.logger import setup_logger
from app.models.user import UserHospitalDB

notif_logger = setup_logger("notification.log")


class NotificationService(BaseService):
    def __init__(self):
        # Initialize any required variables or dependencies here
        self.db = SessionLocal()



    def fetch_notifications(
        self,
        db: Session,
        user_id: int,
        skip_count: int,
        limit: int,
        patient_name: str = None,
        case_id: int = None,  # Optional parameter
    ):
        try:
            notif_logger.debug(
                f"Start fetching notifications for user_id={user_id}, skip_count={skip_count}, limit={limit}, patient_name='{patient_name}', case_id={case_id}"
            )

            # Get user roles and determine if the user is an admin
            user_roles, is_admin = self._get_user_roles_and_admin_status(db, user_id)
            if not user_roles:
                notif_logger.warning(f"No user roles found for user_id={user_id}")
                return None

            # Get case IDs based on user role
            case_ids = self._get_case_ids_for_user(db, user_id, is_admin)

            # Build the notifications query
            notifications_query = self._build_notifications_query(
                db, case_ids, is_admin, user_roles, patient_name, case_id
            )

            # Fetch paginated notifications
            notifications = (
                notifications_query.order_by(NotificationDB.created_at.asc())
                .offset((skip_count - 1) * limit)
                .limit(limit)
                .all()
            )

            # Get total count of notifications
            total_count = self._get_total_count(
                db, case_ids, is_admin, user_roles, patient_name, case_id
            )

            # Format the response
            notifications_response = self._format_notifications_response(notifications)

            return notifications_response, total_count
        except Exception as e:
            notif_logger.error(str(e))
            raise

    def _get_user_roles_and_admin_status(self, db: Session, user_id: int):
        """Fetch user roles and determine if the user is an admin."""
        user_roles = db.query(UserRoleDB).filter(UserRoleDB.user_id == user_id).all()
        if not user_roles:
            return None, False

        get_role = db.query(RoleDB).filter(RoleDB.id == user_roles[0].role_id).all()
        is_admin = get_role[0].name == "Admin" if get_role else False
        return user_roles, is_admin

    def _get_case_ids_for_user(self, db: Session, user_id: int, is_admin: bool):
        """Fetch case IDs for the user based on their role."""
        if is_admin:
            user_hospitals = (
                db.query(UserHospitalDB.hospital_id)
                .filter(UserHospitalDB.user_id == user_id)
                .all()
            )
            case_ids = (
                db.query(Case.id)
                .filter(Case.hospital_id.in_([hospital_id for (hospital_id,) in user_hospitals]))
                .all()
            )
        else:
            case_ids = get_notification_for_user(db, user_id)

        return [row[0] for row in case_ids]

    def _build_notifications_query(
        self,
        db: Session,
        case_ids: list,
        is_admin: bool,
        user_roles: list,
        patient_name: str,
        case_id: int,
    ):
        """Build the query for fetching notifications."""
        from sqlalchemy import case, func
        from app.master.models.package import PackageDB
        from app.master.models.category import CategoryDB
        from app.master.models.sub_category import SubCategoryDB

        MJP_JAY_SCHEME_ID = 1  # Update with the actual ID value

        # Create a verbose_code expression directly in the query
        verbose_code_expr = case(
            (CategoryDB.scheme_type_id == MJP_JAY_SCHEME_ID,
                case(
                    (SubCategoryDB.code != None,
                        func.concat(
                            CategoryDB.code,
                            SubCategoryDB.code,
                            ".",
                            PackageDB.procedure_code
                        )
                    ),
                    else_=func.concat(
                        CategoryDB.code,
                        ".",
                        PackageDB.procedure_code
                    )
                )
            ),
            else_=PackageDB.procedure_code
        ).label("verbose_code")

        # Base query
        notifications_query = (
            db.query(
                NotificationDB,
                Case,
                PatientDB.first_name,
                PatientDB.last_name,
                PackageDB.procedure_name,
                verbose_code_expr,
            )
            .join(Case, NotificationDB.case_id == Case.id)
            .join(PatientDB, Case.patient_id == PatientDB.id)
            .join(PackageDB, Case.package_master_id == PackageDB.id)
            .join(CategoryDB, PackageDB.category_id == CategoryDB.id)
            .outerjoin(SubCategoryDB, PackageDB.sub_category_id == SubCategoryDB.id)
            .filter(NotificationDB.notification_status == "Open", NotificationDB.case_id.in_(case_ids))
            .order_by(NotificationDB.created_at.asc())  # Add this line to order by created_at
        )

        # Apply additional filters
        if not is_admin:
            notifications_query = notifications_query.filter(
                NotificationDB.role_id.in_([role.role_id for role in user_roles])
            )
        if case_id:
            notifications_query = notifications_query.filter(NotificationDB.case_id == case_id)
        if patient_name:
            name_parts = patient_name.strip().split(" ")
            if len(name_parts) > 1:
                first_name, last_name = name_parts[0], " ".join(name_parts[1:])
                notifications_query = notifications_query.filter(
                    PatientDB.first_name.ilike(f"%{first_name}%"),
                    PatientDB.last_name.ilike(f"%{last_name}%")
                )
            else:
                notifications_query = notifications_query.filter(
                    PatientDB.first_name.ilike(f"%{patient_name.strip()}%")
                )

        return notifications_query

    def _get_total_count(
        self,
        db: Session,
        case_ids: list,
        is_admin: bool,
        user_roles: list,
        patient_name: str,
        case_id: int,
    ):
        """Get the total count of notifications."""
        from sqlalchemy import func
        total_count_query = (
            db.query(func.count(NotificationDB.id))
            .join(Case, NotificationDB.case_id == Case.id)
            .join(PatientDB, Case.patient_id == PatientDB.id)
            .filter(NotificationDB.notification_status == "Open", NotificationDB.case_id.in_(case_ids))
        )

        # Apply additional filters
        if not is_admin:
            total_count_query = total_count_query.filter(
                NotificationDB.role_id.in_([role.role_id for role in user_roles])
            )
        if case_id:
            total_count_query = total_count_query.filter(NotificationDB.case_id == case_id)
        if patient_name:
            name_parts = patient_name.strip().split(" ")
            if len(name_parts) > 1:
                first_name, last_name = name_parts[0], " ".join(name_parts[1:])
                total_count_query = total_count_query.filter(
                    PatientDB.first_name.ilike(f"%{first_name}%"),
                    PatientDB.last_name.ilike(f"%{last_name}%")
                )
            else:
                total_count_query = total_count_query.filter(
                    PatientDB.first_name.ilike(f"%{patient_name.strip()}%")
                )

        return total_count_query.scalar()

    def _format_notifications_response(self, notifications):
        """Format the notifications into a response-friendly structure."""
        notifications_response = []
        for notification, case, first_name, last_name, procedure_name, verbose_code in notifications:
            patient_name = f"{first_name} {last_name}".strip()
            notifications_response.append(
                {
                    "id": notification.id,
                    "position": notification.case_step.case_step_package_step_master.steps.order,
                    "case_id": notification.case_id,
                    "case_step_id": notification.case_step_id,
                    "case_step_doc_id": notification.case_step_doc_id,
                    "notification_info": notification.notification_info,
                    "notification_status": notification.notification_status,
                    "patient_name": patient_name,
                    "procedure_name": procedure_name,
                    "verbose_code": verbose_code,
                    "created_at": notification.created_at,
                }
            )
        return notifications_response


    def _get_notification_count(self, db: Session, user_id: int) -> int:
        """
        Get the count of open notifications for a user.
        
        Args:
            db: Database session
            user_id: ID of the user to get notifications for
            
        Returns:
            int: Count of open notifications
            
        Raises:
            Exception: If there's an error during the process
        """
        try:
            notif_logger.info("Getting notification count for user_id=%s", user_id)
            
            # Get user roles
            user_roles = self._get_user_roles(db, user_id)
            if not user_roles:
                return 0
                
            # Get hospital IDs for the user
            hospital_ids = self._get_user_hospital_ids(db, user_id)
            notif_logger.debug("Found %d hospitals for user_id=%s", len(hospital_ids), user_id)
            
            # Get case IDs for the hospitals
            case_subquery = self._build_case_subquery(db, hospital_ids)
            
            # Count open notifications
            notification_count = self._count_open_notifications(db, case_subquery)
            notif_logger.info("Found %d notifications for user_id=%s", notification_count, user_id)
            
            return notification_count
        except Exception as e:
            notif_logger.error("Error getting notification count: %s", str(e), exc_info=True)
            raise
            
    def _get_user_roles(self, db: Session, user_id: int) -> list:
        """Get user roles for a user."""
        user_roles = db.query(UserRoleDB).filter(UserRoleDB.user_id == user_id).all()
        if not user_roles:
            notif_logger.warning("No user roles found for user_id=%s", user_id)
        return user_roles
        
    def _get_user_hospital_ids(self, db: Session, user_id: int) -> list:
        """Get hospital IDs for a user."""
        user_hospitals = (
            db.query(UserHospitalDB.hospital_id)
            .filter(UserHospitalDB.user_id == user_id)
            .all()
        )
        return [hospital_id for (hospital_id,) in user_hospitals]
        
    def _build_case_subquery(self, db: Session, hospital_ids: list):
        """Build a subquery for case IDs based on hospital IDs."""
        return (
            db.query(Case.id)
            .filter(Case.hospital_id.in_(hospital_ids))
            .subquery()
        )
        
    def _count_open_notifications(self, db: Session, case_subquery) -> int:
        """Count open notifications based on a case subquery."""
        return (
            db.query(NotificationDB)
            .filter(
                NotificationDB.notification_status == "Open",
                NotificationDB.case_id.in_(db.query(case_subquery))
            )
            .count()
        )
            
    def fetch_notification_count(self, db: Session, user_id: int):
        try:
            user_roles = (
                db.query(UserRoleDB).filter(UserRoleDB.user_id == user_id).all()
            )
            if not user_roles:
                notif_logger.warning("No user roles found for user_id=%s", user_id)
                return 0

            get_role = db.query(RoleDB).filter(RoleDB.id == user_roles[0].role_id).all()
            is_admin = get_role[0].name == "Admin" if get_role else False

            if is_admin:
                return self._get_notification_count(db, user_id)

            # Get case IDs efficiently
            case_ids_query = get_notification_for_user(db, user_id)
            case_ids = [row[0] for row in case_ids_query]
            
            if not case_ids:
                notif_logger.info("No cases found for user_id=%s", user_id)
                return 0

            # Use a more efficient count query
            role_ids = [role.role_id for role in user_roles]
            notification_count = (
                db.query(NotificationDB)
                .filter(
                    NotificationDB.notification_status == "Open",
                    NotificationDB.case_id.in_(case_ids),
                    NotificationDB.role_id.in_(role_ids)
                )
                .count()
            )

            notif_logger.info("Found %d notifications for user_id=%s", notification_count, user_id)

            return notification_count
        except Exception as e:
            notif_logger.error("Error fetching notification count: %s", str(e), exc_info=True)
            raise

    def get_notification_by_attributes(self, db: Session, **kwargs):
        try:
            notification = (
                db.query(NotificationDB)
                .filter(
                    *[
                        getattr(NotificationDB, attribute) == value
                        for attribute, value in kwargs.items()
                    ]
                )
                .first()
            )
            return notification
        except Exception as e:
            notif_logger.error(str(e))
            raise

    def create_notification(self, notification_data: dict, db: Session):
        """Create a new notification."""
        try:
            notification = self.create(db, NotificationDB, **notification_data)
            return notification
        except Exception as e:
            notif_logger.error(str(e))
            raise

    def create_notification_automic(self, notification_data: dict, db: Session):
        """Create a new notification."""
        try:
            notification = self.transaction_create(
                db, NotificationDB, **notification_data
            )
            return notification
        except Exception as e:
            notif_logger.error(str(e))
            raise

    def update_notification(
        self, notification_id: int, notification_data: dict, db: Session
    ):
        """Update a notification."""
        try:
            notification = self.update(
                db, NotificationDB, notification_id, **notification_data
            )
            return notification
        except Exception as e:
            notif_logger.error(str(e))

    def update_notification_automic(
        self, notification_id: int, notification_data: dict, db: Session
    ):
        """Update a notification."""
        try:
            notification = self.transaction_update(
                db, NotificationDB, notification_id, **notification_data
            )
            return notification
        except Exception as e:
            notif_logger.error(str(e))
            raise


from datetime import datetime

from sqlalchemy.exc import NoResultFound
from sqlalchemy.orm import Session


class NotificationHelperService:
    def notification_exists(self, case_step_id: int, db: Session) -> bool:
        try:
            db.query(NotificationDB).filter_by(case_step_id=case_step_id).one()
            return True
        except NoResultFound:
            return False

    def create_notification(
        self, case_step_id: int, notification_info: str, role_id: int, db: Session
    ):
        new_notification = NotificationDB(
            case_step_id=case_step_id,
            notification_info=notification_info,
            role_id=role_id,
            created_at=datetime.now(),
        )
        db.add(new_notification)
        db.flush()
        db.refresh(new_notification)
        notif_logger.info(f"Notification with case_step_id {case_step_id} created.")

    def delete_notification(self, case_step_id: int, db: Session):
        notification = (
            db.query(NotificationDB).filter_by(case_step_id=case_step_id).one()
        )
        db.delete(notification)
        notif_logger.info(f"Notification with case_step_id {case_step_id} deleted.")

    def create_or_delete_notification(
        self, case_step_id: int, notification_info: str, role_id: int, db: Session
    ):
        if self.notification_exists(case_step_id, db):
            self.delete_notification(case_step_id, db)
        else:
            self.create_notification(case_step_id, notification_info, role_id, db)
