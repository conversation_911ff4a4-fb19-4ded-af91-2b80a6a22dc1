from fastapi import <PERSON>Rout<PERSON>, Depends, status
from fastapi.responses import J<PERSON><PERSON>esponse
from fastapi.security import OAuth2<PERSON><PERSON><PERSON><PERSON>earer
from sqlalchemy.orm import Session

# Function to get a database session
from app.database.database import Base, SessionLocal, engine, get_db
from app.notification.exception.custom.notification import NotificationExceptions
from app.notification.services.notification import NotificationService
from app.services.user import AuthService
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler

# Create an API router
router = APIRouter()
notification_service = NotificationService()

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


@router.get("/notifications")
def get_notifications(
    db: Session = Depends(get_db), token: str = Depends(oauth2_scheme),
    page: int = 1,
    size: int = 8,
    patient_name: str = None,
    case_id:int = None
):
    """
    Get all notifications from the table.

    Args:
        db (Session): The database session.

    Returns:
        JSONResponse: The JSON response containing the notifications.

    """
    try:
        user = AuthService().get_user_from_token(token)
        notifications, count = notification_service.fetch_notifications(
            db, user_id=user.id, skip_count=page, limit=size, patient_name=patient_name,case_id=case_id
        )
        return generate_response(
            data=notifications,
            total_count=count,
            status_code=status.HTTP_200_OK,
            message="Notifications retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)


@router.get("/notifications/count")
def get_notifications_count(
    db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)
):
    """
    Get notifications count from the table.

    Args:
        db (Session): The database session.

    Returns:
        JSONResponse: The JSON response containing the notifications.

    """
    try:
        user = AuthService().get_user_from_token(token)
        notification_count = notification_service.fetch_notification_count(
            db, user_id=user.id
        )
        return generate_response(
            data={"notification_count": notification_count},
            status_code=status.HTTP_200_OK,
            message="Notification count retrieved successfully",
        )
    except Exception as e:
        return ExceptionHandler().handle_exception(e)
