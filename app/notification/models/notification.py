from app.database.database import Base
from sqlalchemy import Column, <PERSON><PERSON><PERSON>, Integer, String, Boolean, DateTime
from datetime import datetime
from sqlalchemy.orm import relationship



class NotificationDB(Base):
    __tablename__ = 'notifications'

    id = Column(Integer, primary_key=True, index=True)
    case_id = Column(Integer, ForeignKey('case.id'))
    case_step_id = Column(Integer, ForeignKey('case_step.id'),  nullable=True)
    case_step_doc_id = Column(Integer, ForeignKey('case_step_document.id'), nullable=True)
    notification_info = Column(String(200))
    notification_status = Column(String(25), default='Open')
    status_id = Column(Integer, ForeignKey('status_master.id'), nullable=True)

    role_id = Column(Integer, ForeignKey('roles.id'), nullable=True)
    created_at = Column(DateTime, default=datetime.now)

    case = relationship("Case", back_populates="notifications")
    case_step = relationship("CaseStep")
    case_step_doc = relationship("CaseStepDocument")
    role = relationship("RoleDB")
    
  
class NotificationMasterDB(Base):
    __tablename__ = 'notification_master'

    id = Column(Integer, primary_key=True, index=True)
    status_id = Column(Integer, ForeignKey('status_master.id'), nullable=True)
    role_id = Column(Integer, ForeignKey('roles.id'), nullable=True)
    
    
    status = relationship("StatusMaster")
    role = relationship("RoleDB")
   
class NotificationStatusSequence(Base):
    __tablename__ = 'notification_status_sequence'

    id = Column(Integer, primary_key=True, index=True)
    status_id = Column(Integer, ForeignKey('status_master.id'), nullable=True)
    sequence = Column(Integer)
    step_id = Column(Integer, ForeignKey('step_master.id'), nullable=True)
    