from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi import Request

from app.services.user import AuthService
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi import Request
from app.services.user import AuthService


EXCLUDED_PATHS = [
    "/login",
    "/signup",
    "/login/otp",
    "/login/otp/verify",
    "/health"
]  # Add paths you want to exclude from token check

class UserExtractionMiddleware(BaseHTTPMiddleware):
    """
    Custom middleware class for extracting user from the token in the request.
    """

    async def dispatch(self, request: Request, call_next):
        """
        Middleware method that intercepts the request and extracts the user from the token.

        Args:
            request (Request): The incoming request object.
            call_next (Callable): The next middleware or endpoint to call.

        Returns:
            Response: The response object.

        Raises:
            JSONResponse: If the request is unauthorized (missing Authorization header).
        """
        path = request.url.path.rstrip("/")
        if path not in EXCLUDED_PATHS:
            token = self._get_token_from_request(request)
            user = self._get_user_from_token(token)
            if not user:
                return self.get_unauthorized_error_response()
            request.state.user = user
            
            
            
        return await call_next(request)

    def _get_token_from_request(self, request: Request) -> str:
        """
        Extracts the token from the Authorization header in the request.

        Args:
            request (Request): The incoming request object.

        Returns:
            str: The token string.

        Raises:
            JSONResponse: If the request is unauthorized (missing Authorization header).
        """
        token = request.headers.get("Authorization")
        if not token:
            return self.get_unauthorized_error_response()
        return token

    def _get_user_from_token(self, token: str):
        """
        Retrieves the user from the token.

        Args:
            token (str): The token string.

        Returns:
            User: The user object.

        Raises:
            JSONResponse: If the request is unauthorized (invalid token).
        """
        user = AuthService().get_user_from_token(token)
        if not user:
            return self.get_unauthorized_error_response()
        return user
    
    def get_unauthorized_error_response(self):
        """
        Returns an unauthorized error response.

        Returns:
            JSONResponse: The JSON response object.
        """
        return self.get_unauthorized_error_response()
