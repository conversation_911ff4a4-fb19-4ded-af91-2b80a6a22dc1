from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi import Request
import re
from app.services.user import AuthService
from utils.logger import setup_logger
import time
import os
import jwt

# Simple logger setup without environment-specific configuration
logger = setup_logger(log_file="auth.log")

# Cache AuthService instance to avoid creating it for each request
auth_service = AuthService()

# Define error response function to avoid duplication
def create_auth_error_response(msg, loc=["header", "Authorization"], error_type="auth_error"):
    return JSONResponse(
        status_code=401,
        content={"detail": [{"loc": loc, "msg": msg, "type": error_type}]}
    )

EXCLUDED_PATH_PATTERNS = [
    r"^/login/?$",
    r"^/signup/?$",
    r"^/login/otp/?$",
    r"^/login/otp/verify/?$",
    r"^/docs/?$",
    r"^/redoc/?$",
    r"^/openapi\.json/?$",
    r"^/password-reset/[^/]+/?$",  # Adjust this pattern to match your specific path structure
    r"^/patient/login",
    r"/health$",
    r"^/packages/upload/?$",  # Temporarily added for testing
    r"^/users/?$",  # Temporarily added for testing database connection fix
]


class CustomMiddleware(BaseHTTPMiddleware):
    """
    Custom middleware class for handling authorization in the application.
    """

    async def dispatch(self, request: Request, call_next):
        """
        Middleware method that intercepts the request and performs authorization checks.

        Args:
            request (Request): The incoming request object.
            call_next (Callable): The next middleware or endpoint to call.

        Returns:
            Response: The response object.

        Raises:
            JSONResponse: If the request is unauthorized (missing Authorization header).
        """
        # Extract path and client IP only once
        path = request.url.path.rstrip("/")
        client_ip = request.client.host

        # Log only essential information at debug level instead of info
        logger.debug(f"Request: {request.method} {path} from {client_ip}")

        # Check if the path is excluded from authentication
        if not any(re.match(pattern, path) for pattern in EXCLUDED_PATH_PATTERNS):
            token = request.headers.get("Authorization")
            if not token:
                logger.warning(f"Unauthorized access: {path}")
                return create_auth_error_response("Authentication token is required to access this resource")

            token = token.replace("Bearer ", "")

            # Use the cached AuthService instance
            user = auth_service.get_user_from_token(token)
            if user is None:
                # Try to decode the token to check if it's valid
                try:
                    secret_key = os.getenv("SECRET_KEY")
                    payload = jwt.decode(token, secret_key, algorithms=["HS256"])
                    user_id = payload.get("user_id")

                    # If we can decode the token but user is None, it means the user is inactive
                    logger.warning(f"Inactive user attempted access: {path}, user_id: {user_id}")
                    return create_auth_error_response(
                        "Your account has been deactivated. Please contact the administrator for assistance.",
                        loc=["user", "is_active"]
                    )
                except Exception:
                    # If we can't decode the token, it's invalid or expired
                    logger.warning(f"Invalid token: {path}")
                    return create_auth_error_response("Your session has expired or is invalid. Please log in again.")

            # Store user in request state without excessive logging
            request.state.user = user

        # Proceed to the next middleware or endpoint
        start_time = time.time()
        response = await call_next(request)

        # Log response time for performance monitoring but at debug level
        duration = time.time() - start_time
        if duration > 1.0:  # Only log slow responses at info level
            logger.info(f"Slow response: {path} took {duration:.2f}s, status={response.status_code}")
        else:
            logger.debug(f"Response: {path} in {duration:.2f}s, status={response.status_code}")

        return response