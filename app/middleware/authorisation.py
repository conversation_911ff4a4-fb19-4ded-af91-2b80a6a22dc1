from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi import Request

from app.services.user import AuthService
from app.services.authorisation.user_roles import UserRolesService


EXCLUDED_PATHS = [
    "/login",
    "/signup",
    "/login/otp",
    "/login/otp/verify",
    "/health"
]


class TokenExtractor:
    """
    Utility class for extracting the token from the request headers.
    """

    @staticmethod
    def extract(request: Request) -> str:
        """
        Extracts the token from the Authorization header in the request.

        Args:
            request (Request): The incoming request object.

        Returns:
            str: The extracted token.

        Raises:
            ValueError: If the Authorization header is missing.
        """
        token = request.headers.get("Authorization")
        if not token:
            raise ValueError("Missing Authorization header")
        return token


class UserExtractor:
    """
    Utility class for extracting the user from the token.
    """

    @staticmethod
    def extract(token: str):
        """
        Extracts the user from the provided token.

        Args:
            token (str): The token string.

        Returns:
            User: The extracted user object.

        Raises:
            ValueError: If the token is invalid.
        """
        user = AuthService().get_user_from_token(token)
        if not user:
            raise ValueError("Invalid token")
        return user


class PermissionChecker:
    """
    Utility class for checking user permissions.
    """

    @staticmethod
    def has_permission(endpoint_url, method, user_id):
        """
        Checks if the user has permission to access the specified endpoint.

        Args:
            endpoint_url (str): The URL of the endpoint.
            method (str): The HTTP method of the request.
            user_id (int): The ID of the user.

        Returns:
            bool: True if the user has permission, False otherwise.
        """
        return UserRolesService().get_user_permissions(
            user_id=user_id, endpoint_url=endpoint_url, endpoint_method=method
        )


class AuthorisationMiddleware(BaseHTTPMiddleware):
    """
    Custom middleware class for extracting user from the token in the request.
    """

    async def dispatch(self, request: Request, call_next):
        """
        Middleware method that extracts the user from the token in the request.

        Args:
            request (Request): The incoming request object.
            call_next (Callable): The next middleware or endpoint to call.

        Returns:
            Response: The response from the next middleware or endpoint.

        Raises:
            JSONResponse: If the user is not authorized or forbidden to access the endpoint.
        """
        path = request.url.path.rstrip("/")
        method = request.method
        if path not in EXCLUDED_PATHS:
            try:
                token = TokenExtractor.extract(request)
                user = UserExtractor.extract(token)
                request.state.user = user
                if not PermissionChecker.has_permission(path, method, user.id):
                    return self.get_forbidden_error_response()
                request.state.method = method
            except ValueError:
                return self.get_unauthorized_error_response()

        return await call_next(request)

    @staticmethod
    def get_unauthorized_error_response():
        """
        Returns a JSON response indicating that the user is not authorized.

        Returns:
            JSONResponse: The unauthorized error response.
        """
        return JSONResponse(status_code=403, content={"message": "User not authorized"})

    @staticmethod
    def get_forbidden_error_response():
        """
        Returns a JSON response indicating that the user is forbidden to access the endpoint.

        Returns:
            JSONResponse: The forbidden error response.
        """
        return JSONResponse(status_code=403, content={"detail": "User not authorized to access this endpoint."})