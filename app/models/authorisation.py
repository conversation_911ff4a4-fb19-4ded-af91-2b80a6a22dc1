from app.database.database import Base
from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON>, Integer, String, Text, Index
from sqlalchemy import <PERSON>um<PERSON>, Integer, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.ext.associationproxy import association_proxy

from app.models.user import UserDB
from enum import Enum
from sqlalchemy import Enum as SQLAlchemyEnum

# Base=declarative_base()


class PermissionDB(Base):
    """
    Represents a permission in the database.
    """
    __tablename__ = "permissions"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False)
    description = Column(Text)
    groups = relationship(
        "GroupDB", secondary="group_permission", back_populates="permissions")


class RoleDB(Base):
    """
    Represents a role in the database.
    """
    __tablename__ = "roles"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False)
    description = Column(Text)
    groups = relationship(
        "GroupDB", secondary="group_role", back_populates="roles")
    users = relationship("UserDB", secondary="user_role",
                         back_populates="roles")


class HttpMethod(Enum):
    GET = 'GET'
    POST = 'POST'
    PUT = 'PUT'
    PATCH = 'PATCH'
    DELETE = 'DELETE'

class EndpointDB(Base):
    """
    Represents an endpoint in the database.
    """
    __tablename__ = "endpoints"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), unique=True, nullable=False)
    url = Column(String(255), nullable=False)
    description = Column(Text)
    permission_id = Column(Integer, ForeignKey('permissions.id'), index=True)
    method = Column(SQLAlchemyEnum(HttpMethod), nullable=False)

    # Define indexes
    __table_args__ = (
        Index('idx_name_permission_id', 'name', 'permission_id'),
    )

class GroupDB(Base):
    """
    Represents a group in the database.
    """
    __tablename__ = "groups"
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(100), unique=True, nullable=False)
    description = Column(Text)
    permissions = relationship(
        "PermissionDB", secondary="group_permission", back_populates="groups")
    roles = relationship("RoleDB", secondary="group_role",
                         back_populates="groups")
    # Define indexes
    __table_args__ = (
        Index('idx_name', 'name', unique=True),
    )


class GroupRoleDB(Base):
    """
    Represents a group-role relationship in the database.
    """
    __tablename__ = "group_role"
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)

    group_id = Column(Integer, ForeignKey('groups.id'), index=True)
    role_id = Column(Integer, ForeignKey('roles.id'), index=True)

    # Define indexes
    __table_args__ = (
        Index('idx_group_role', 'group_id', 'role_id', unique=True),
    )


class GroupPermissionDB(Base):
    __tablename__ = 'group_permission'
    id = Column(Integer, primary_key=True)
    group_id = Column(Integer, ForeignKey('groups.id'))
    permission_id = Column(Integer, ForeignKey('permissions.id'))

    # Define indexes
    __table_args__ = (
        Index('idx_group_permission', 'group_id',
              'permission_id', unique=True),
    )


class UserRoleDB(Base):
    """
    Represents a user-role relationship in the database.
    """
    __tablename__ = "user_role"
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)

    user_id = Column(Integer, ForeignKey('users.id'), index=True)
    role_id = Column(Integer, ForeignKey('roles.id'), index=True)

    # Define indexes
    __table_args__ = (
        Index('idx_user_role', 'user_id', 'role_id', unique=True),
    )
