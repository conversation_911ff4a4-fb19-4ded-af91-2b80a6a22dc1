from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy import <PERSON>umn, Foreign<PERSON>ey, Integer, String, Boolean, DateTime, func
from datetime import datetime, timedelta
from sqlalchemy.orm import relationship

from app.database.database import Base

class UserDB(Base):
    """
    Represents a user in the database.
    """

    __tablename__ = "users"
    id = Column(Integer, primary_key=True, index=True)
    first_name = Column(String(25), index=True)
    last_name = Column(String(25), index=True)
    email = Column(String(255), index=True, unique=True)
    password = Column(String(250), index=True)
    mobile_number = Column(String(25), index=True)
    is_active = Column(Boolean, default=True)
    is_patient = Column(Boolean, default=False)
    roles = relationship("RoleDB", secondary="user_role", back_populates="users")
    hospitals = relationship(
        "HospitalDB", secondary="user_hospital", back_populates="users"
    )
    case_step_documents = relationship("CaseStepDocument", back_populates="users")
    created_at = Column(DateTime, default=datetime.utcnow())
    updated_at = Column(DateTime, default=datetime.utcnow())

    @hybrid_property
    def full_name(self):
        return func.lower(self.first_name + self.last_name)


class UserHospitalDB(Base):
    """
    Represents the relationship between a user and a hospital in the database.
    """

    __tablename__ = "user_hospital"
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    hospital_id = Column(Integer, ForeignKey("hospital_master.id"), index=True)


class OtpDB(Base):
    """
    Represents an OTP in the database.
    """

    __tablename__ = "otp"
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    mobile_number = Column(String(25), index=True)
    otp = Column(String(6), index=True)
    timestamp_id = Column(Integer, index=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow())
    expire_at = Column(DateTime, default=datetime.utcnow() + timedelta(minutes=3))
