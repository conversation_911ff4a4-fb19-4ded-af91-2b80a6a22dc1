
-- Roles Insertion
INSERT INTO roles (name, description) VALUES
('Admin', 'Has full access to the system'),
('Claims Team', 'Handles insurance claims'),
('Data Entry Operator', 'Enters data into the system');

-- Groups Insertion
INSERT INTO `groups` (name, description) VALUES
('Admins', 'Group for administrators'),
('Claims', 'Group for claims team members'),
('Data Entry Operators', 'Group for data entry operators');

-- Permissions Insertion
INSERT INTO permissions (name, description) VALUES

-- User Permisisons
('User Create', 'Permission to create a user'),
('User Read', 'Permission to read user details'),
('User Update', 'Permission to update a user'),
('User List', 'Permission to list users'),
('User Filter', 'Permission to filter users'),

-- Permission Permissions
('Permissions Read', 'Permission to read permission details'),
('Permissions List', 'Permission to list permissions'),
('Permissions Filter', 'Permission to filter permissions'),

-- Group Permissions
('Groups Read', 'Permission to read group details'),
('Groups List', 'Permission to list groups'),
('Groups Filter', 'Permission to filter groups'),

-- Role Permissions
('Roles Read', 'Permission to read role details'),
('Roles List', 'Permission to list roles'),
('Roles Filter', 'Permission to filter roles'),

-- Scheme Permissions
('Scheme Create', 'Permission to create a scheme'),
('Scheme Read', 'Permission to read scheme details'),
('Scheme Update', 'Permission to update a scheme'),
('Scheme List', 'Permission to list schemes'),
('Scheme Filter', 'Permission to filter schemes'),

-- Hospial Permissions
('Hospital Create', 'Permission to create a hospital'),
('Hospital Read', 'Permission to read hospital details'),
('Hospital Update', 'Permission to update a hospital'),
('Hospital List', 'Permission to list hospitals'),
('Hospital Filter', 'Permission to filter hospitals'),

-- Document Permissions
('Document Create', 'Permission to create a document'),
('Document Read', 'Permission to read document details'),
('Document Update', 'Permission to update a document'),
('Document List', 'Permission to list documents'),
('Document Filter', 'Permission to filter documents'),

-- Category Permissions
('Category Create', 'Permission to create a category'),
('Category Read', 'Permission to read category details'),
('Category Update', 'Permission to update a category'),
('Category List', 'Permission to list categories'),
('Category Filter', 'Permission to filter categories'),

-- Sub Category Permissions
('Sub Category Create', 'Permission to create a sub category'),
('Sub Category Read', 'Permission to read sub category details'),
('Sub Category Update', 'Permission to update a sub category'),
('Sub Category List', 'Permission to list sub categories'),
('Sub Category Filter', 'Permission to filter sub categories'),

-- Package Permissions
('Package Create', 'Permission to create a package'),
('Package Read', 'Permission to read package details'),
('Package Update', 'Permission to update a package'),
('Package List', 'Permission to list packages'),
('Package Filter', 'Permission to filter packages'),

-- Patinet Permissions
('Patient Enrollment', 'Permission to create a patient'),
('Patient Read', 'Permission to read patient details'),
('Patient List', 'Permission to list patients'),
('Patient Delete', 'Permission to delete patients'),

-- Case Permissions
('Case Create', 'Permission to create a case'),
('Case Read', 'Permission to read case details'),
('Case Update', 'Permission to update a case'),
('Case List', 'Permission to list cases'),

-- Case Step Permissions
('Case Step Update', 'Permission to update a case step'),

-- Case Step Document Permissions
('Case Step Document Update', 'Permission to update a case step document');


-- Endpoints Insertion

INSERT INTO endpoints (name, url, description, permission_id, method) VALUES
('User Create', '/users', 'Permission to create a user', (SELECT id FROM permissions WHERE name = 'User Create'), 'POST'),
('User Read', '/users/{id}', 'Permission to read user details', (SELECT id FROM permissions WHERE name = 'User Read'), 'GET'),
('User Update', '/users/{id}', 'Permission to update a user', (SELECT id FROM permissions WHERE name = 'User Update'), 'PUT'),
('User List', '/users', 'Permission to list users', (SELECT id FROM permissions WHERE name = 'User List'), 'GET'),
('User Filter', '/users/filter', 'Permission to filter users', (SELECT id FROM permissions WHERE name = 'User Filter'), 'GET'),

('Permissions Read', '/permissions/{id}', 'Permission to read permission details', (SELECT id FROM permissions WHERE name = 'Permissions Read'), 'GET'),
('Permissions List', '/permissions', 'Permission to list permissions', (SELECT id FROM permissions WHERE name = 'Permissions List'), 'GET'),

('Groups Read', '/groups/{id}', 'Permission to read group details', (SELECT id FROM permissions WHERE name = 'Groups Read'), 'GET'),
('Groups List', '/groups', 'Permission to list groups', (SELECT id FROM permissions WHERE name = 'Groups List'), 'GET'),

('Roles Read', '/roles/{id}', 'Permission to read role details', (SELECT id FROM permissions WHERE name = 'Roles Read'), 'GET'),
('Roles List', '/roles', 'Permission to list roles', (SELECT id FROM permissions WHERE name = 'Roles List'), 'GET'),

('Scheme Create', '/schemes', 'Permission to create a scheme', (SELECT id FROM permissions WHERE name = 'Scheme Create'), 'POST'),
('Scheme Read', '/schemes/{id}', 'Permission to read scheme details', (SELECT id FROM permissions WHERE name = 'Scheme Read'), 'GET'),
('Scheme Update', '/schemes/{id}', 'Permission to update a scheme', (SELECT id FROM permissions WHERE name = 'Scheme Update'), 'PUT'),
('Scheme List', '/schemes', 'Permission to list schemes', (SELECT id FROM permissions WHERE name = 'Scheme List'), 'GET'),
('Scheme Filter', '/schemes/filter', 'Permission to filter schemes', (SELECT id FROM permissions WHERE name = 'Scheme Filter'), 'GET'),

('Hospital Create', '/hospitals', 'Permission to create a hospital', (SELECT id FROM permissions WHERE name = 'Hospital Create'), 'POST'),
('Hospital Read', '/hospitals/{id}', 'Permission to read hospital details', (SELECT id FROM permissions WHERE name = 'Hospital Read'), 'GET'),
('Hospital Update', '/hospitals/{id}', 'Permission to update a hospital', (SELECT id FROM permissions WHERE name = 'Hospital Update'), 'PUT'),
('Hospital List', '/hospitals', 'Permission to list hospitals', (SELECT id FROM permissions WHERE name = 'Hospital List'), 'GET'),
('Hospital Filter', '/hospitals/filter', 'Permission to filter hospitals', (SELECT id FROM permissions WHERE name = 'Hospital Filter'), 'GET'),

('Document Create', '/documents', 'Permission to create a document', (SELECT id FROM permissions WHERE name = 'Document Create'), 'POST'),
('Document Read', '/documents/{id}', 'Permission to read document details', (SELECT id FROM permissions WHERE name = 'Document Read'), 'GET'),
('Document Update', '/documents/{id}', 'Permission to update a document', (SELECT id FROM permissions WHERE name = 'Document Update'), 'PUT'),
('Document List', '/documents', 'Permission to list documents', (SELECT id FROM permissions WHERE name = 'Document List'), 'GET'),
('Document Filter', '/documents/filter', 'Permission to filter documents', (SELECT id FROM permissions WHERE name = 'Document Filter'), 'GET'),

('Category Create', '/categories', 'Permission to create a category', (SELECT id FROM permissions WHERE name = 'Category Create'), 'POST'),
('Category Read', '/categories/{id}', 'Permission to read category details', (SELECT id FROM permissions WHERE name = 'Category Read'), 'GET'),
('Category Update', '/categories/{id}', 'Permission to update a category', (SELECT id FROM permissions WHERE name = 'Category Update'), 'PUT'),
('Category List', '/categories', 'Permission to list categories', (SELECT id FROM permissions WHERE name = 'Category List'), 'GET'),
('Category Filter', '/categories/filter', 'Permission to filter categories', (SELECT id FROM permissions WHERE name = 'Category Filter'), 'GET'),

('Sub Category Create', '/sub-categories', 'Permission to create a sub category', (SELECT id FROM permissions WHERE name = 'Sub Category Create'), 'POST'),
('Sub Category Read', '/sub-categories/{id}', 'Permission to read sub category details', (SELECT id FROM permissions WHERE name = 'Sub Category Read'), 'GET'),
('Sub Category Update', '/sub-categories/{id}', 'Permission to update a sub category', (SELECT id FROM permissions WHERE name = 'Sub Category Update'), 'PUT'),
('Sub Category List', '/sub-categories', 'Permission to list sub categories', (SELECT id FROM permissions WHERE name = 'Sub Category List'), 'GET'),
('Sub Category Filter', '/sub-categories/filter', 'Permission to filter sub categories', (SELECT id FROM permissions WHERE name = 'Sub Category Filter'), 'GET'),

('Package Create', '/packages', 'Permission to create a package', (SELECT id FROM permissions WHERE name = 'Package Create'), 'POST'),
('Package Read', '/packages/{id}', 'Permission to read package details', (SELECT id FROM permissions WHERE name = 'Package Read'), 'GET'),
('Package Update', '/packages/{id}', 'Permission to update a package', (SELECT id FROM permissions WHERE name = 'Package Update'), 'PUT'),
('Package List', '/packages', 'Permission to list packages', (SELECT id FROM permissions WHERE name = 'Package List'), 'GET'),
('Package Filter', '/packages/filter', 'Permission to filter packages', (SELECT id FROM permissions WHERE name = 'Package Filter'), 'GET'),

('Patient Enrollment', '/patients', 'Permission to create a patient', (SELECT id FROM permissions WHERE name = 'Patient Enrollment'), 'POST'),
('Patient Read', '/patients/{id}', 'Permission to read patient details', (SELECT id FROM permissions WHERE name = 'Patient Read'), 'GET'),
('Patient List', '/patients', 'Permission to list patients', (SELECT id FROM permissions WHERE name = 'Patient List'), 'GET'),
('Patient Delete', '/patients/{id}', 'Permission to delete patients', (SELECT id FROM permissions WHERE name = 'Patient Delete'), 'DELETE'),

('Case Create', '/cases', 'Permission to create a case', (SELECT id FROM permissions WHERE name = 'Case Create'), 'POST'),
('Case Read', '/cases/{id}', 'Permission to read case details', (SELECT id FROM permissions WHERE name = 'Case Read'), 'GET'),
('Case Update', '/cases/{id}', 'Permission to update a case', (SELECT id FROM permissions WHERE name = 'Case Update'), 'PUT'),
('Case List', '/cases', 'Permission to list cases', (SELECT id FROM permissions WHERE name = 'Case List'), 'GET'),

('Case Step Update', '/case-steps/{id}', 'Permission to update a case step', (SELECT id FROM permissions WHERE name = 'Case Step Update'), 'PUT'),

('Case Step Document Update', '/case-step-documents/{id}', 'Permission to update a case step document', (SELECT id FROM permissions WHERE name = 'Case Step Document Update'), 'PUT');


-- Group Role Association
INSERT INTO group_role (group_id, role_id)
SELECT g.id, r.id
FROM `groups` g, roles r
WHERE g.name = 'Admin' AND r.name = 'Admin'
UNION ALL
SELECT g.id, r.id
FROM `groups` g, roles r
WHERE g.name = 'Claims' AND r.name = 'Claims Team'
UNION ALL
SELECT g.id, r.id
FROM `groups` g, roles r
WHERE g.name = 'Data Entry' AND r.name = 'Data Entry Operator';


-- Group Permission Association

INSERT INTO group_permission (group_id, permission_id)
SELECT g.id, p.id
FROM `groups` g, permissions p
WHERE g.name = 'Admins'
UNION ALL
SELECT g.id, p.id
FROM `groups` g, permissions p
WHERE g.name = 'Claims' AND p.name IN (
    'Scheme Read',
    'Scheme List',
    'Scheme Filter',
    'Hospital Read',
    'Hospital List',
    'Hospital Filter',
    'Document Read',
    'Document List',
    'Document Filter',
    'Category Create',
    'Category Read',
    'Category List',
    'Category Filter',
    'Sub Category Read',
    'Sub Category List',
    'Sub Category Filter',
    'Package Read',
    'Package List',
    'Package Filter',
    'Patient Read',
    'Patient List',
    'Case Read',
    'Case Update',
    'Case Step Update',
    'Case Step Document Update'
)
UNION ALL
SELECT g.id, p.id
FROM `groups` g, permissions p
WHERE g.name = 'Data Entry Operators' AND p.name IN (
    'Scheme Read',
    'Scheme List',
    'Scheme Filter',
    'Hospital Read',
    'Hospital List',
    'Hospital Filter',
    'Document Read',
    'Document List',
    'Document Filter',
    'Category Create',
    'Category Read',
    'Category List',
    'Category Filter',
    'Sub Category Read',
    'Sub Category List',
    'Sub Category Filter',
    'Package Read',
    'Package List',
    'Package Filter',
    'Patient Read',
    'Patient List',
    'Case Read',
    'Case Update',
    'Case Step Update',
    'Case Step Document Update'
);