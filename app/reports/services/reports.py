import base64
from app.database.database import SessionLocal
from sqlalchemy.orm import Session
from app.cases.models import Case
from app.reports.validations.reports import ReportTypeEnum
from app.master.models import SchemeType
from app.database.database import engine
from datetime import datetime,timedelta
import pandas as pd
import io
from app.reports.helpers.logger import reports_logger


class ReportsService:
    def __init__(self) -> None:
        self.db = SessionLocal()
        self.engine = engine

    def generate_report(self, db: Session, report_type, start_date, end_date, hospitals, categories, sub_categories, procedure_codes):
        current_date = datetime.now().strftime("%d-%b-%Y")
        current_time = datetime.now().strftime("%I:%M %p")
        startdate = start_date.strftime("%d-%b-%Y")
        enddate = end_date.strftime("%d-%b-%Y")
        end_date = end_date.replace(hour=datetime.now().hour, minute=datetime.now().minute, second=datetime.now().second, microsecond=0)

        match report_type:
            case ReportTypeEnum.pmjay_all_hospital_backlog:
                scheme_type_id = db.query(SchemeType).filter(
                    SchemeType.type == "AB-PM-JAY").first().id
                headline = f"{ReportTypeEnum.pmjay_all_hospital_backlog} from {startdate} to {enddate} as generated on {current_date} at {current_time}"
                pivot_table = self.generate_pmjay_all_hospital_backlog(
                    self.engine, start_date, end_date, hospitals, categories, sub_categories, procedure_codes, scheme_type_id)
            case ReportTypeEnum.mjpjay_all_hospital_backlog:
                scheme_type_id = db.query(SchemeType).filter(
                    SchemeType.type == "MJP-JAY").first().id
                headline = f"{ReportTypeEnum.mjpjay_all_hospital_backlog} from {startdate} to {enddate} as generated on {current_date} at {current_time}"
                pivot_table = self.generate_mjpjay_all_hospital_backlog(
                    self.engine, start_date, end_date, hospitals, categories, sub_categories, procedure_codes, scheme_type_id)
            case ReportTypeEnum.mcgm_peripheral_dean:
                scheme_type_id = db.query(SchemeType).filter(
                    SchemeType.type == "MJP-JAY").first().id
                headline = f"{ReportTypeEnum.mcgm_peripheral_dean} from {startdate} to {enddate} as generated on {current_date} at {current_time}"
                pivot_table = self.generate_mcgm_pheripheral_dean_report(
                    self.engine, start_date, end_date, hospitals, categories, sub_categories, procedure_codes, scheme_type_id)
        # pivot_table = self.generate_mjpjay_all_hospital_backlog(
        #     self.engine, start_date, end_date, hospitals, categories, sub_categories, procedure_codes, scheme_type_id)

        # Convert only the numeric columns to integers
        pivot_table = pivot_table.map(
            lambda x: int(x) if isinstance(x, (int, float)) else x)

        pivot_table.reset_index(inplace=True)
        # Convert the DataFrame to a dictionary with the desired format
        result_dict = pivot_table.set_index(
            'hospital_name').to_dict(orient='index')

        # Rename the pivot table index to 'hospital_name'
        pivot_table.rename(
            columns={'hospital_name': 'Hospital Name'}, inplace=True)

        # Export data to an Excel file in memory
        export_data_to_excel = self.export_data_to_excel(
            pivot_table, headline=headline)
        # Convert bytes to base64 encoded string
        base64_encoded_str = base64.b64encode(
            export_data_to_excel).decode('utf-8')
        # Generate the file name
        file_name = f"{report_type}_{start_date.strftime('%Y-%m-%d')}_to_{end_date.strftime('%Y-%m-%d')}.xlsx"

        # with open(f"/home/<USER>/Desktop/uploaded_files/{file_name}", 'wb') as f:
        #     f.write(export_data_to_excel)

        response = {
            "report_data": result_dict,
            "headline": headline,
            "excel_file": {
                "file_name": file_name,
                "file_data": base64_encoded_str
            }
        }
        return response

    def generate_all_hospital_backlog_query(self, engine, start_date, end_date, hospitals=None, categories=None, sub_categories=None, procedure_codes=None, scheme_type_id=None):
        hospitals = hospitals if hospitals is not None else []
        categories = categories if categories is not None else []
        sub_categories = sub_categories if sub_categories is not None else []
        procedure_codes = procedure_codes if procedure_codes is not None else []
    
        # Keep the base query the same to get all combinations
        base_query = """
            SELECT 
                h.name AS hospital_name, 
                s.name AS status_name
            FROM 
                hospital_master h
            CROSS JOIN 
                status_master s
        """
    
        if hospitals:
            base_query += " WHERE h.id IN ({})".format(
                ','.join(map(str, hospitals)))
    
        # Execute the query to get all combinations of hospitals and statuses
        combinations_df = pd.read_sql_query(base_query, engine)
    
        # Updated case query with JOIN structure but handling NULL category_id
        case_query = """
            SELECT 
                h.name AS hospital_name, 
                s.name AS status_name, 
                COUNT(c.id) AS case_count
            FROM 
                `case` c
            JOIN 
                package_master pm ON c.package_master_id = pm.id
            LEFT JOIN 
                category_master cm ON pm.category_id = cm.id 
            JOIN 
                hospital_master h ON c.hospital_id = h.id
            JOIN 
                status_master s ON c.status = s.id
            WHERE 
                c.created_at BETWEEN '{start_date}' AND '{end_date}'
                AND (cm.scheme_type_id = {scheme_type_id} OR pm.category_id IS NULL)
        """.format(start_date=start_date, end_date=end_date, scheme_type_id=scheme_type_id)
    
        # Add additional filters
        if hospitals:
            case_query += " AND c.hospital_id IN ({})".format(
                ','.join(map(str, hospitals)))
    
        if categories:
            case_query += " AND pm.category_id IN ({})".format(
                ','.join(map(str, categories)))
    
        if sub_categories:
            case_query += " AND pm.sub_category_id IN ({})".format(
                ','.join(map(str, sub_categories)))
    
        if procedure_codes:
            case_query += " AND pm.id IN ({})".format(
                ','.join(map(str, procedure_codes)))
    
        # Group by clause
        case_query += " GROUP BY h.name, s.name ORDER BY h.name, s.name"
        
        # Execute the case query
        case_counts_df = pd.read_sql_query(case_query, engine)
        
        # Merge the combinations with the actual case counts to ensure all combinations are present
        merged_df = combinations_df.merge(
            case_counts_df, on=['hospital_name', 'status_name'], how='left').fillna(0)
    
        return merged_df
        
    def generate_mjpjay_all_hospital_backlog(self, engine, start_date, end_date, hospitals=None, categories=None, sub_categories=None, procedure_codes=None, scheme_type_id=None):
        # Execute the query to get all combinations of hospitals and statuses
        merged_df = self.generate_all_hospital_backlog_query(
            engine, start_date, end_date, hospitals, categories, sub_categories, procedure_codes, scheme_type_id)

        # Filter only the specified statuses in the DataFrame
        status_list = [
            'Claim Doctor Pending',
            'Claim Doctor Rejected - Live',
            'Discharge document Submitted',
            'PreAuth Approved',
            'Treatment document Pending',
            'Treatment document Submitted'
        ]

        filtered_df = merged_df[merged_df['status_name'].isin(
            status_list)]

        # Generate pivot table
        pivot_table = pd.pivot_table(
            filtered_df,
            values='case_count',
            index='hospital_name',
            columns='status_name',
            fill_value=0
        )

        # Calculate custom columns
        pivot_table['SU'] = pivot_table.get(
            'Treatment document Pending', 0) + pivot_table.get('Treatment document Submitted', 0)
        pivot_table['TSU'] = pivot_table.get(
            'Treatment document Pending', 0) + pivot_table.get('Treatment document Submitted', 0)

        pivot_table = pivot_table.drop(
            columns=['Treatment document Pending', 'Treatment document Submitted'])

        # Rename columns to match the specified format
        pivot_table = pivot_table.rename(columns={
            'Claim Doctor Pending': 'CDP - Claim Doctor Pending',
            'Claim Doctor Rejected - Live': 'CDR-LIVE - Claim Doctor Rejected - Live',
            'Discharge document Submitted': 'DU - Discharge Document Submitted',
            'PreAuth Approved': 'PA - Pre Auth Approved',
            'SU': 'SU - Treatment Pending + Submitted count',
            'TSU': 'TSU - Treatment Pending + Submitted count'
        })

        # Add a 'Total' column that sums the counts of all statuses for each hospital
        pivot_table['Total'] = pivot_table.sum(axis=1)

        # Calculate the total of each column & Create a new row with the column totals
        pivot_table.loc['Grand Total'] = pivot_table.sum(axis=0)

        # Display the DataFrame
        return pivot_table

    def generate_pmjay_all_hospital_backlog(self, engine, start_date, end_date, hospitals=None, categories=None, sub_categories=None, procedure_codes=None, scheme_type_id=None):
        # Execute the query to get all combinations of hospitals and statuses
        merged_df = self.generate_all_hospital_backlog_query(
            engine, start_date, end_date, hospitals, categories, sub_categories, procedure_codes, scheme_type_id)

        # Filter only the specified statuses in the DataFrame
        status_list = [
            'Claim Doctor Pending',
            'Claim Rejected',
            'Discharge document Submitted',
            'PreAuth Approved'
        ]

        filtered_df = merged_df[merged_df['status_name'].isin(
            status_list)]

        # Generate pivot table
        pivot_table = pd.pivot_table(
            filtered_df,
            values='case_count',
            index='hospital_name',
            columns='status_name',
            fill_value=0
        )

        # Rename columns to match the specified format
        pivot_table = pivot_table.rename(columns={
            'Claim Doctor Pending': 'CDP - Claim Doctor Pending',
            'Claim Rejected': 'Claim Rejected',
            'Discharge document Submitted': 'DU - Discharge Document Submitted',
            'PreAuth Approved': 'PA - Pre Auth Approved'
        })

        # Add a 'Total' column that sums the counts of all statuses for each hospital
        pivot_table['Total'] = pivot_table.sum(axis=1)

        # Calculate the total of each column & Create a new row with the column totals
        pivot_table.loc['Grand Total'] = pivot_table.sum(axis=0)

        # Display the DataFrame
        return pivot_table

    def generate_mcgm_pheripheral_dean_report(
            self, engine, start_date, end_date, hospitals, categories, sub_categories, procedure_codes, scheme_type_id):

        case_query = """
            SELECT 
                h.name AS hospital_name,
                s.name AS status_name,
                SUM(c.pre_auth_approved_amount) AS pre_auth_approved_amount,
                COUNT(c.id) AS case_count
            FROM 
                status_master s
            LEFT JOIN 
                `case` c ON c.status = s.id
                AND c.created_at BETWEEN '{start_date}' AND '{end_date}'
            LEFT JOIN 
                hospital_master h ON c.hospital_id = h.id
            LEFT JOIN 
                package_master pm ON c.package_master_id = pm.id
                AND (
                    pm.category_id IN (
                        SELECT cm.id
                        FROM category_master cm
                        WHERE cm.scheme_type_id = {scheme_type_id}
                    )
                    OR pm.category_id IS NULL
                )
            """.format(start_date=start_date, end_date=end_date, scheme_type_id=scheme_type_id)

        if hospitals:
            case_query += " WHERE h.id IN ({})".format(
                ','.join(map(str, hospitals)))

        if categories:
            case_query += " AND pm.category_id IN ({})".format(
                ','.join(map(str, categories)))

        if sub_categories:
            case_query += " AND pm.sub_category_id IN ({})".format(
                ','.join(map(str, sub_categories)))

        if procedure_codes:
            case_query += " AND c.procedure_code_id IN ({})".format(
                ','.join(map(str, procedure_codes)))

        # Group by clause
        case_query += " GROUP BY h.name, s.name ORDER BY h.name, s.name"
        reports_logger.debug("Executing SQL query for case counts: %s", case_query)
        
        # Execute the query
        case_counts_df = pd.read_sql_query(case_query, engine)
        
        # Generate pivot table
        report_data = []
        for hospital in case_counts_df['hospital_name'].unique():
            hospital_data = case_counts_df[case_counts_df['hospital_name'] == hospital]

            total_cases = hospital_data['case_count'].sum()
            preauth_done = hospital_data[hospital_data['status_name']
                                         == 'PreAuth Approved']['case_count'].sum()
            in_process_at_hospital = hospital_data[hospital_data['status_name'].isin(
                ['Treatment document Pending', 'Treatment document Submitted'])]['case_count'].sum()
            claims_settled = hospital_data[hospital_data['status_name']
                                           == 'Claim Paid']['case_count'].sum()
            at_accounts = hospital_data[hospital_data['status_name']
                                        == 'Claim Approved']['case_count'].sum()
            amt_pending = hospital_data[hospital_data['status_name']
                                        == 'PreAuth Approved']['pre_auth_approved_amount'].sum()

            report_data.append({
                "hospital_name": hospital,
                "Total Cases": total_cases,
                "Preuth Done": preauth_done,
                "In-process AT Hospital": in_process_at_hospital,
                "Claims Settled": claims_settled,
                "AT Accounts": at_accounts,
                "AMT RECVD": 0,
                "AMT Pending-> Pre auth approved amount": amt_pending
            })

        report_df = pd.DataFrame(report_data)
        return report_df

    def export_data_to_excel(self, data, headline):
        """
        Export data to an Excel file in memory.

        Args:
            data (list of dict): The data to be exported in a list of dictionaries.
            headline (str): The headline to be added in the Excel file.

        Returns:
            bytes: The content of the Excel file as bytes.
        """
        try:
            # Create a DataFrame from the flattened data
            df = pd.DataFrame(data)

            # Create an in-memory Excel file
            excel_file = io.BytesIO()

            # Write DataFrame to the Excel file
            with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
                # Add the headline to the Excel file
                worksheet = writer.book.add_worksheet()
                worksheet.merge_range('A1:Z1', headline, cell_format=writer.book.add_format(
                    {'bold': True, 'valign': 'vcenter'}))

                # Dump the data to the Excel file
                df.to_excel(writer, index=False, startrow=2)

            # Get the file data as bytes
            file_data = excel_file.getvalue()
            return file_data
        except Exception as e:
            raise e
