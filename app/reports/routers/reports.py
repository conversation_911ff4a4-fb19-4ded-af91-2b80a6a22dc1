from fastapi import APIRouter, Depends, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import List, Optional
from app.reports.services.reports import ReportsService
from datetime import date
from app.reports.validations.reports import ReportCreate, ReportTypeEnum
from utils.common import get_db
from utils.generate_response import generate_response
from utils.helpers import ExceptionHandler
from app.reports.helpers.logger import reports_logger

router = APIRouter()
# Step 1: Define the Enum


@router.post("/reports")
async def generate_report(data: ReportCreate, db: Session = Depends(get_db),
                          ):
    """
    Generate a report based on the given parameters.

    Args:
    - report_type (str): The type of report to generate.
    - start_date (date): The start date for the report.
    - end_date (date): The end date for the report.
    - hospital (list[int]): The IDs of the hospital to filter the report by.
    - category (list[int]): The IDs of the category to filter the report by.
    - sub_category (list[int]): The IDs of the sub-category to filter the report by.
    - procedure (list[int]): The IDs of the procedure to filter the report by.
    - db (Session, optional): The database session to use. Defaults to Depends(get_db).

    Returns:
        GenerateResponse: The generated report.

    Raises:
        NotFoundException: If no report is found.
    """
    try:
        report_type = data.report_type
        start_date = data.start_date
        end_date = data.end_date
        hospital = data.hospital
        category = data.category
        sub_category = data.sub_category
        procedure = data.procedure
        # Generate the report.
        report = ReportsService().generate_report(
            db=db,
            report_type=report_type,
            start_date=start_date,
            end_date=end_date,
            hospitals=hospital,
            categories=category,
            sub_categories=sub_category,
            procedure_codes=procedure,
        )
        # Return a response with the generated report.
        return generate_response(
            data=report,
            status_code=status.HTTP_200_OK,
            message="Report generated successfully",
        )
    except Exception as e:
        # Log the exception and handle it.
        reports_logger.error("Error generating report: %s", str(e), exc_info=True)
        return ExceptionHandler().handle_exception(e)


@router.get("/report-types")
async def get_report_types():
    """
    API to fetch available report types.
    """
    try:
        report_types = [{"report_type": report_type.value, "value": report_type.value}
                        for report_type in ReportTypeEnum]
        return generate_response(
            data=report_types,
            status_code=status.HTTP_200_OK,
            message="Report types fetched successfully",
        )
    except Exception as e:
        reports_logger.error("Error fetching report types: %s", str(e), exc_info=True)
        return ExceptionHandler().handle_exception(e)
