from typing import Optional, List
from pydantic import BaseModel
from datetime import datetime, date
from fastapi import Query
from enum import Enum


class ReportTypeEnum(str, Enum):
    pmjay_all_hospital_backlog = "PMJAY-All Hospital Backlog"
    mjpjay_all_hospital_backlog = "MJPJAY-All Hospital Backlog"
    mcgm_peripheral_dean = "MCGM Peripheral-Dean"


class ReportCreate(BaseModel):
    """
    Represents a schema for creating a case.
    """
    # The type of report to generate.
    report_type: ReportTypeEnum
    # The start date for the report.
    start_date: datetime
    # The end date for the report.
    end_date: datetime
    # The IDs of the hospital to filter the report by.
    hospital: Optional[List[int]] = None
    # The IDs of the category to filter the report by.
    category: Optional[List[int]] = None
    # The IDs of the sub-category to filter the report by.
    sub_category: Optional[List[int]] = None
    # The IDs of the procedure to filter the report by.
    procedure: Optional[List[int]] = None
