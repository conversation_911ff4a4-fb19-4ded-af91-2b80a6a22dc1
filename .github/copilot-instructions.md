# 🤖 Copilot Agent Mode: Code Quality & Behavior Ruleset

> A ruleset to ensure <PERSON><PERSON><PERSON> behaves like a skilled, responsible developer — writing clean, consistent, maintainable, and context-aware code.

---

## 🧠 GENERAL PRINCIPLES
1. **Understand Context**  
   Generate code that fits the surrounding code, project patterns, and user intent.

2. **Write Maintainable Code**  
   Code should be easy to read, test, extend, and debug.

3. **Prioritize Readability over Brevity**  
   Use clear logic and meaningful names. Favor explicit over implicit behavior.

---

## 🧹 CLEAN CODE PRINCIPLES
1. **Meaningful Naming**  
   Use descriptive names for variables, functions, classes, and files.

2. **Small Functions**  
   Functions should be short and do one thing well.

3. **Single Responsibility**  
   One function/class/module = one responsibility.

4. **Avoid Code Duplication**  
   Reuse logic via abstraction or composition.

5. **Smart Comments**  
   Only comment when necessary. Describe *why*, not *what*.

---

## 🧰 SOFTWARE DESIGN PRINCIPLES
1. **SOLID Principles**  
   Apply core OOP design principles (SRP, OCP, LSP, ISP, DIP).

2. **DRY (Don’t Repeat Yourself)**  
   Avoid duplicating logic and data.

3. **KISS (Keep It Simple, Stupid)**  
   Avoid overengineering. Choose the simplest working design.

4. **YAGNI (You Aren’t Gonna Need It)**  
   Don’t write code for features unless they're needed.

5. **Design for Testability**  
   Functions and modules should be easy to test.

---

## ✅ PYTHON BEST PRACTICES
1. **Follow PEP8**  
   Maintain consistent formatting and style.

2. **Type Hints**  
   Use static typing for all functions and variables.

3. **Docstrings**  
   Document functions, classes, and modules clearly.

4. **Pythonic Constructs**  
   Use `with`, `enumerate`, comprehensions, unpacking, etc., appropriately.

5. **Avoid Anti-Patterns**  
   Don't misuse mutable defaults, ignore exceptions, or overuse globals.

---

## 🔐 SECURITY PRACTICES
1. **No Hardcoded Secrets**  
   Use environment variables or secure configs.

2. **Input Validation**  
   Sanitize all user or external inputs.

3. **Principle of Least Privilege**  
   Avoid giving excessive permissions to scripts or components.

4. **Avoid Known Vulnerabilities**  
   Prevent SQL injection, command injection, XSS, CSRF, etc.

---

## 📈 PERFORMANCE & SCALABILITY
1. **Time and Space Complexity Awareness**  
   Write efficient code, especially in loops or large data processing.

2. **Lazy Evaluation**  
   Use generators and iterators when handling large datasets.

3. **Avoid Premature Optimization**  
   Optimize only when bottlenecks are proven.

---

## 📦 PROJECT STRUCTURE
1. **Organized Module Layout**  
   Keep a logical project structure: `src/`, `tests/`, `configs/`, etc.

2. **Separation of Concerns**  
   Split code cleanly into models, services, routers, utilities, etc.

3. **Minimal Coupling**  
   Minimize interdependencies between modules.

---

## 🧪 TESTING & QUALITY
1. **Write Unit Tests**  
   Cover core logic with `pytest` or `unittest`.

2. **Use Testable Patterns**  
   Avoid tight coupling; prefer pure functions where possible.

3. **Include Edge Case Tests**  
   Especially for data boundaries, invalid input, and fallback paths.

4. **Don't Forget to Test Updated Logic**  
   When modifying code, update or add corresponding tests.

---

## 📚 DOCUMENTATION
1. **Self-Documenting Code**  
   Write code that explains itself via structure and naming.

2. **Clear API Contracts**  
   Document expected inputs, outputs, and side effects.

3. **Usage Examples When Needed**  
   Especially for public modules or libraries.

---

## 🤯 THINK LIKE A HUMAN DEVELOPER
> Go beyond line-editing. Be a responsible problem-solver.

1. **Understand the Full Scope of a Change**  
   - Before coding, consider how it affects models, services, APIs, validations, and tests.

2. **Apply Changes Holistically, Not Partially**  
   - Don’t just update a single file—propagate changes across all related components.

3. **Don’t Leave Code Half-Updated**  
   - Remove old logic, clean up related code, and ensure consistency.

4. **Anticipate Ripple Effects**  
   - If the data source or contract changes, update every dependent layer.

5. **Ask if Intent is Unclear**  
   - Don’t assume — infer from context or request clarification.

6. **Mirror Real Developer Behavior**  
   - Think: “How would a teammate refactor this?”

7. **Update Docs and Tests Accordingly**  
   - Always reflect changes across usage examples, docstrings, and test cases.

8. **Solve the Root Problem, Not Just the Prompt**  
   - Think in terms of solutions, not line-by-line instructions.
