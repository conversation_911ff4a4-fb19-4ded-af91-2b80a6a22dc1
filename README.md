# Arogya Yojana Service API

## Overview

The Arogya Yojana Service API is a comprehensive healthcare management system built with FastAPI. It provides a robust backend for managing patient information, healthcare schemes, hospitals, case management, and reporting. The system is designed to streamline healthcare administration processes and improve service delivery to patients.

## Features

- **User Management**: Complete user authentication and authorization system with role-based access control
- **Patient Management**: Register, update, and manage patient information
- **Case Management**: Create and track patient cases through various steps
- **Document Management**: Upload, store, and retrieve patient documents
- **Master Data Management**: Manage reference data for schemes, packages, hospitals, etc.
- **Address Management**: Hierarchical address management (state, district, sub-district)
- **Notification System**: Send and manage notifications to users
- **Reporting**: Generate various reports for administrative and analytical purposes
- **Health Check**: Endpoints to monitor system health

## Technology Stack

- **Framework**: FastAPI (Python 3.10)
- **Database**: MySQL
- **ORM**: SQLAlchemy
- **Authentication**: JWT (JSON Web Tokens)
- **Documentation**: Swagger UI (OpenAPI)
- **Containerization**: Docker
- **Scheduling**: APScheduler
- **Data Processing**: Pandas, NumPy
- **Testing**: Pytest

## Installation

### Prerequisites

- Python 3.10 or higher
- MySQL 8.0 or higher
- Docker and Docker Compose (for containerized deployment)

### Local Development Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/your-organization/python-arogya-yojna-service.git
   cd python-arogya-yojna-service
   ```

2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r app/requirements.txt
   ```

4. Create a `.env` file in the app directory with the following variables:
   ```
   # Database Configuration
   DB_HOST=localhost
   DB_USER=your_db_user
   DB_PASSWORD=your_db_password
   DB_NAME=arogya_yojana_db
   
   # JWT Configuration
   JWT_SECRET_KEY=your_secret_key
   JWT_ALGORITHM=HS256
   ACCESS_TOKEN_EXPIRE_MINUTES=30
   
   # Application Configuration
   APP_ENV=development
   LOG_LEVEL=INFO
   
   # File Storage Configuration
   UPLOAD_DIRECTORY=./app/files/uploads
   ```

5. Run database migrations:
   ```bash
   alembic upgrade head
   ```

6. Start the application:
   ```bash
   uvicorn app.main:app --host 0.0.0.0 --port 8077 --reload
   ```

7. Access the API documentation at `http://localhost:8077/docs`

## Production Deployment

### Using Docker

1. Build the Docker image:
   ```bash
   docker build -t python_arogya_yojna .
   ```

2. Run using Docker Compose:
   ```bash
   docker-compose up -d
   ```

### Manual Deployment

1. Set up a production-grade server with Python 3.10
2. Clone the repository and install dependencies
3. Configure environment variables for production
4. Set up a reverse proxy (Nginx or similar) with SSL
5. Use a process manager like Supervisor or systemd
6. Run the application with Gunicorn:
   ```bash
   gunicorn -w 4 -k uvicorn.workers.UvicornWorker app.main:app --bind 0.0.0.0:8077
   ```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| DB_HOST | Database host address | localhost |
| DB_USER | Database username | - |
| DB_PASSWORD | Database password | - |
| DB_NAME | Database name | arogya_yojana_db |
| JWT_SECRET_KEY | Secret key for JWT token generation | - |
| JWT_ALGORITHM | Algorithm used for JWT | HS256 |
| ACCESS_TOKEN_EXPIRE_MINUTES | Token expiration time in minutes | 30 |
| APP_ENV | Application environment (development/production) | development |
| LOG_LEVEL | Logging level | INFO |
| UPLOAD_DIRECTORY | Directory for file uploads | ./app/files/uploads |

## API Documentation

The API documentation is automatically generated and available at:
- Swagger UI: `/docs`
- ReDoc: `/redoc`

## Database Migrations

This project uses Alembic for database migrations:

```bash
# Create a new migration
alembic revision --autogenerate -m "Description of changes"

# Apply migrations
alembic upgrade head

# Rollback to a previous version
alembic downgrade <revision_id>
```

## Changelog

### v1.0.0 (2025-03-24) - Production Release
- Initial production release
- Complete user management system
- Patient registration and management
- Case tracking and document management
- Master data management modules
- Reporting functionality
- Containerized deployment support

## Known Issues

- High database load during peak hours may cause connection timeouts
- Large file uploads (>50MB) may fail in certain network conditions
- Report generation for very large datasets may be slow

## Future Enhancements

- Implement caching layer for frequently accessed data
- Add real-time notifications using WebSockets
- Enhance reporting with interactive dashboards
- Implement batch processing for large data operations
- Add support for multiple languages
- Integrate with external healthcare systems via APIs

## License

Copyright © 2025 V2STech Solutions. All rights reserved.
