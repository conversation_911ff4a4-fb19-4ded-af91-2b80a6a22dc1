"""address models migrated

Revision ID: a57e2e3aaf05
Revises: 0a686e8fbb91
Create Date: 2024-05-24 17:41:38.739807

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a57e2e3aaf05'
down_revision: Union[str, None] = '0a686e8fbb91'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('state',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_state_id'), 'state', ['id'], unique=False)
    op.create_index(op.f('ix_state_name'), 'state', ['name'], unique=False)
    op.create_table('district',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('state_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['state_id'], ['state.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_district_id'), 'district', ['id'], unique=False)
    op.create_index(op.f('ix_district_name'), 'district', ['name'], unique=False)
    op.create_table('sub_district',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('district_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['district_id'], ['district.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sub_district_district_id'), 'sub_district', ['district_id'], unique=False)
    op.create_index(op.f('ix_sub_district_id'), 'sub_district', ['id'], unique=False)
    op.create_index(op.f('ix_sub_district_name'), 'sub_district', ['name'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_sub_district_name'), table_name='sub_district')
    op.drop_index(op.f('ix_sub_district_id'), table_name='sub_district')
    op.drop_index(op.f('ix_sub_district_district_id'), table_name='sub_district')
    op.drop_table('sub_district')
    op.drop_index(op.f('ix_district_name'), table_name='district')
    op.drop_index(op.f('ix_district_id'), table_name='district')
    op.drop_table('district')
    op.drop_index(op.f('ix_state_name'), table_name='state')
    op.drop_index(op.f('ix_state_id'), table_name='state')
    op.drop_table('state')
    # ### end Alembic commands ###
