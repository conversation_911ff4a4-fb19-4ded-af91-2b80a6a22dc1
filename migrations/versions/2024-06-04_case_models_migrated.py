"""case models migrated

Revision ID: 2bb1c70dd0ba
Revises: 964d15c6f684
Create Date: 2024-06-04 00:25:43.534168

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2bb1c70dd0ba'
down_revision: Union[str, None] = '964d15c6f684'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('status_master',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('key', sa.String(length=50), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_status_master_id'), 'status_master', ['id'], unique=False)
    op.create_table('case',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('govt_case_id', sa.String(length=255), nullable=False),
    sa.Column('patient_id', sa.Integer(), nullable=True),
    sa.Column('package_master_id', sa.Integer(), nullable=True),
    sa.Column('hospital_id', sa.Integer(), nullable=True),
    sa.Column('hospital_file_no', sa.String(length=255), nullable=True),
    sa.Column('claim_no', sa.String(length=255), nullable=True),
    sa.Column('pre_auth_approved_amount', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['hospital_id'], ['hospital_master.id'], ),
    sa.ForeignKeyConstraint(['package_master_id'], ['package_master.id'], ),
    sa.ForeignKeyConstraint(['patient_id'], ['patient.id'], ),
    sa.ForeignKeyConstraint(['status'], ['status_master.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_case_govt_case_id'), 'case', ['govt_case_id'], unique=True)
    op.create_index(op.f('ix_case_hospital_id'), 'case', ['hospital_id'], unique=False)
    op.create_index(op.f('ix_case_id'), 'case', ['id'], unique=False)
    op.create_index(op.f('ix_case_package_master_id'), 'case', ['package_master_id'], unique=False)
    op.create_index(op.f('ix_case_patient_id'), 'case', ['patient_id'], unique=False)
    op.create_index(op.f('ix_case_status'), 'case', ['status'], unique=False)
    op.create_table('case_patient_relative_mapping',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('case_id', sa.Integer(), nullable=False),
    sa.Column('relative_name', sa.String(length=255), nullable=False),
    sa.Column('relative_contact', sa.String(length=255), nullable=False),
    sa.ForeignKeyConstraint(['case_id'], ['case.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_case_patient_relative_mapping_case_id'), 'case_patient_relative_mapping', ['case_id'], unique=False)
    op.create_table('case_step',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('case_id', sa.Integer(), nullable=True),
    sa.Column('case_step_date', sa.DateTime(), nullable=True),
    sa.Column('case_status_date', sa.DateTime(), nullable=True),
    sa.Column('package_step_master_id', sa.Integer(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['case_id'], ['case.id'], ),
    sa.ForeignKeyConstraint(['case_id'], ['case.id'], ),
    sa.ForeignKeyConstraint(['package_step_master_id'], ['package_step_master.id'], ),
    sa.ForeignKeyConstraint(['package_step_master_id'], ['package_step_master.id'], ),
    sa.ForeignKeyConstraint(['status'], ['status_master.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_case_step_case_id'), 'case_step', ['case_id'], unique=False)
    op.create_index(op.f('ix_case_step_id'), 'case_step', ['id'], unique=False)
    op.create_index(op.f('ix_case_step_package_step_master_id'), 'case_step', ['package_step_master_id'], unique=False)
    op.create_index(op.f('ix_case_step_status'), 'case_step', ['status'], unique=False)
    op.create_table('case_step_document',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('case_step_id', sa.Integer(), nullable=True),
    sa.Column('guid', sa.String(length=36), nullable=True),
    sa.Column('document_master_id', sa.Integer(), nullable=True),
    sa.Column('is_required', sa.Boolean(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['case_step_id'], ['case_step.id'], ),
    sa.ForeignKeyConstraint(['case_step_id'], ['case_step.id'], ),
    sa.ForeignKeyConstraint(['document_master_id'], ['document_master.id'], ),
    sa.ForeignKeyConstraint(['document_master_id'], ['document_master.id'], ),
    sa.ForeignKeyConstraint(['status'], ['status_master.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_case_step_document_case_step_id'), 'case_step_document', ['case_step_id'], unique=False)
    op.create_index(op.f('ix_case_step_document_document_master_id'), 'case_step_document', ['document_master_id'], unique=False)
    op.create_index(op.f('ix_case_step_document_guid'), 'case_step_document', ['guid'], unique=True)
    op.create_index(op.f('ix_case_step_document_id'), 'case_step_document', ['id'], unique=False)
    op.create_index(op.f('ix_case_step_document_status'), 'case_step_document', ['status'], unique=False)
    op.create_table('case_audit_trail',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('case_id', sa.Integer(), nullable=False),
    sa.Column('case_step_id', sa.Integer(), nullable=True),
    sa.Column('case_step_document_id', sa.Integer(), nullable=True),
    sa.Column('action', sa.String(length=25), nullable=False),
    sa.Column('data', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['case_id'], ['case.id'], ),
    sa.ForeignKeyConstraint(['case_step_document_id'], ['case_step_document.id'], ),
    sa.ForeignKeyConstraint(['case_step_id'], ['case_step.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_case_audit_trail_case_id'), 'case_audit_trail', ['case_id'], unique=False)
    op.create_index(op.f('ix_case_audit_trail_id'), 'case_audit_trail', ['id'], unique=False)
    op.drop_index('ix_users_email', table_name='users')
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.create_index('ix_users_email', 'users', ['email'], unique=False)
    op.drop_index(op.f('ix_case_audit_trail_id'), table_name='case_audit_trail')
    op.drop_index(op.f('ix_case_audit_trail_case_id'), table_name='case_audit_trail')
    op.drop_table('case_audit_trail')
    op.drop_index(op.f('ix_case_step_document_status'), table_name='case_step_document')
    op.drop_index(op.f('ix_case_step_document_id'), table_name='case_step_document')
    op.drop_index(op.f('ix_case_step_document_guid'), table_name='case_step_document')
    op.drop_index(op.f('ix_case_step_document_document_master_id'), table_name='case_step_document')
    op.drop_index(op.f('ix_case_step_document_case_step_id'), table_name='case_step_document')
    op.drop_table('case_step_document')
    op.drop_index(op.f('ix_case_step_status'), table_name='case_step')
    op.drop_index(op.f('ix_case_step_package_step_master_id'), table_name='case_step')
    op.drop_index(op.f('ix_case_step_id'), table_name='case_step')
    op.drop_index(op.f('ix_case_step_case_id'), table_name='case_step')
    op.drop_table('case_step')
    op.drop_index(op.f('ix_case_patient_relative_mapping_case_id'), table_name='case_patient_relative_mapping')
    op.drop_table('case_patient_relative_mapping')
    op.drop_index(op.f('ix_case_status'), table_name='case')
    op.drop_index(op.f('ix_case_patient_id'), table_name='case')
    op.drop_index(op.f('ix_case_package_master_id'), table_name='case')
    op.drop_index(op.f('ix_case_id'), table_name='case')
    op.drop_index(op.f('ix_case_hospital_id'), table_name='case')
    op.drop_index(op.f('ix_case_govt_case_id'), table_name='case')
    op.drop_table('case')
    op.drop_index(op.f('ix_status_master_id'), table_name='status_master')
    op.drop_table('status_master')
    # ### end Alembic commands ###
