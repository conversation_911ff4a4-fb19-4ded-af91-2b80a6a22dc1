"""master data models migrated

Revision ID: 46125968afe1
Revises: 
Create Date: 2024-05-24 17:38:44.361033

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '46125968afe1'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('document_master',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=200), nullable=False),
    sa.Column('is_required', sa.<PERSON>(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_document_master_name'), 'document_master', ['name'], unique=False)
    op.create_table('hospital_master',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=300), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_hospital_master_name'), 'hospital_master', ['name'], unique=False)
    op.create_table('scheme_type_master',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('type', sa.String(length=255), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_scheme_type_master_id'), 'scheme_type_master', ['id'], unique=False)
    op.create_index(op.f('ix_scheme_type_master_type'), 'scheme_type_master', ['type'], unique=True)
    op.create_table('step_master',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('type', sa.Enum('PRE_INVESTIGATION', 'PRE_AUTHORIZATION', 'TREATMENT', 'DISCHARGE', 'CLAIM', name='steptype'), nullable=False),
    sa.Column('order', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_step_master_id'), 'step_master', ['id'], unique=False)
    op.create_index(op.f('ix_step_master_order'), 'step_master', ['order'], unique=True)
    op.create_index(op.f('ix_step_master_type'), 'step_master', ['type'], unique=True)
    op.create_table('category_master',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('code', sa.String(length=20), nullable=False),
    sa.Column('name', sa.String(length=300), nullable=False),
    sa.Column('scheme_type_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['scheme_type_id'], ['scheme_type_master.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code', 'name', 'scheme_type_id', name='uq_category')
    )
    op.create_index(op.f('ix_category_master_code'), 'category_master', ['code'], unique=False)
    op.create_index(op.f('ix_category_master_name'), 'category_master', ['name'], unique=False)
    op.create_table('scheme_document_master',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('document_id', sa.Integer(), nullable=False),
    sa.Column('scheme_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['document_id'], ['document_master.id'], ),
    sa.ForeignKeyConstraint(['scheme_id'], ['scheme_type_master.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('sub_category_master',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('code', sa.String(length=20), nullable=False),
    sa.Column('name', sa.String(length=300), nullable=False),
    sa.Column('category_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['category_id'], ['category_master.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sub_category_master_code'), 'sub_category_master', ['code'], unique=False)
    op.create_index(op.f('ix_sub_category_master_name'), 'sub_category_master', ['name'], unique=False)
    op.create_table('package_master',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('procedure_code', sa.String(length=20), nullable=False),
    sa.Column('procedure_name', sa.String(length=500), nullable=False),
    sa.Column('package_amount', sa.Integer(), nullable=False),
    sa.Column('category_id', sa.Integer(), nullable=True),
    sa.Column('sub_category_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['category_id'], ['category_master.id'], ),
    sa.ForeignKeyConstraint(['sub_category_id'], ['sub_category_master.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('procedure_code', 'procedure_name', 'package_amount', 'category_id', 'sub_category_id', name='uq_package')
    )
    op.create_table('package_step_master',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('package_id', sa.Integer(), nullable=True),
    sa.Column('step_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['package_id'], ['package_master.id'], ),
    sa.ForeignKeyConstraint(['step_id'], ['step_master.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('package_step_document_master',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('package_step_id', sa.Integer(), nullable=False),
    sa.Column('document_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['document_id'], ['document_master.id'], ),
    sa.ForeignKeyConstraint(['package_step_id'], ['package_step_master.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('package_step_id', 'document_id', name='uq_package_step_document')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('package_step_document_master')
    op.drop_table('package_step_master')
    op.drop_table('package_master')
    op.drop_index(op.f('ix_sub_category_master_name'), table_name='sub_category_master')
    op.drop_index(op.f('ix_sub_category_master_code'), table_name='sub_category_master')
    op.drop_table('sub_category_master')
    op.drop_table('scheme_document_master')
    op.drop_index(op.f('ix_category_master_name'), table_name='category_master')
    op.drop_index(op.f('ix_category_master_code'), table_name='category_master')
    op.drop_table('category_master')
    op.drop_index(op.f('ix_step_master_type'), table_name='step_master')
    op.drop_index(op.f('ix_step_master_order'), table_name='step_master')
    op.drop_index(op.f('ix_step_master_id'), table_name='step_master')
    op.drop_table('step_master')
    op.drop_index(op.f('ix_scheme_type_master_type'), table_name='scheme_type_master')
    op.drop_index(op.f('ix_scheme_type_master_id'), table_name='scheme_type_master')
    op.drop_table('scheme_type_master')
    op.drop_index(op.f('ix_hospital_master_name'), table_name='hospital_master')
    op.drop_table('hospital_master')
    op.drop_index(op.f('ix_document_master_name'), table_name='document_master')
    op.drop_table('document_master')
    # ### end Alembic commands ###
