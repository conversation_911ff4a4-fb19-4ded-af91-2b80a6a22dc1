"""scheme_hospital models migrated

Revision ID: 2754d6a10936
Revises: 2bb1c70dd0ba
Create Date: 2024-06-05 14:06:57.537806

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2754d6a10936'
down_revision: Union[str, None] = '2bb1c70dd0ba'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('scheme_hospital_master',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('hospital_id', sa.Integer(), nullable=False),
    sa.Column('scheme_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['hospital_id'], ['hospital_master.id'], ),
    sa.ForeignKeyConstraint(['scheme_id'], ['scheme_type_master.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('scheme_hospital_master')
    # ### end Alembic commands ###
