"""patient models migrated

Revision ID: 964d15c6f684
Revises: a57e2e3aaf05
Create Date: 2024-05-24 17:46:45.060453

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '964d15c6f684'
down_revision: Union[str, None] = 'a57e2e3aaf05'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('patient',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('first_name', sa.String(length=25), nullable=True),
    sa.Column('middle_name', sa.String(length=25), nullable=True),
    sa.Column('last_name', sa.String(length=25), nullable=True),
    sa.Column('gender', sa.String(length=6), nullable=True),
    sa.Column('date_of_birth', sa.DateTime(), nullable=True),
    sa.Column('phone_number', sa.String(length=10), nullable=True),
    sa.Column('alternate_phone_number', sa.String(length=10), nullable=True),
    sa.Column('relative_name', sa.String(length=25), nullable=True),
    sa.Column('relative_phone_number', sa.String(length=10), nullable=True),
    sa.Column('ration_card_number', sa.String(length=25), nullable=True),
    sa.Column('aadhar_number', sa.String(length=25), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('timestamp_id', sa.Integer(), nullable=True),
    sa.Column('date_of_enrollement', sa.DateTime(), nullable=True),
    sa.Column('created_by', sa.String(length=25), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('updated_by', sa.String(length=25), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_patient_aadhar_number'), 'patient', ['aadhar_number'], unique=False)
    op.create_index(op.f('ix_patient_alternate_phone_number'), 'patient', ['alternate_phone_number'], unique=False)
    op.create_index(op.f('ix_patient_created_by'), 'patient', ['created_by'], unique=False)
    op.create_index(op.f('ix_patient_date_of_birth'), 'patient', ['date_of_birth'], unique=False)
    op.create_index(op.f('ix_patient_first_name'), 'patient', ['first_name'], unique=False)
    op.create_index(op.f('ix_patient_gender'), 'patient', ['gender'], unique=False)
    op.create_index(op.f('ix_patient_id'), 'patient', ['id'], unique=False)
    op.create_index(op.f('ix_patient_last_name'), 'patient', ['last_name'], unique=False)
    op.create_index(op.f('ix_patient_middle_name'), 'patient', ['middle_name'], unique=False)
    op.create_index(op.f('ix_patient_phone_number'), 'patient', ['phone_number'], unique=False)
    op.create_index(op.f('ix_patient_ration_card_number'), 'patient', ['ration_card_number'], unique=False)
    op.create_index(op.f('ix_patient_relative_name'), 'patient', ['relative_name'], unique=False)
    op.create_index(op.f('ix_patient_relative_phone_number'), 'patient', ['relative_phone_number'], unique=False)
    op.create_index(op.f('ix_patient_timestamp_id'), 'patient', ['timestamp_id'], unique=False)
    op.create_index(op.f('ix_patient_updated_by'), 'patient', ['updated_by'], unique=False)
    op.create_table('address',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('patient_id', sa.Integer(), nullable=True),
    sa.Column('address_line1', sa.String(length=200), nullable=True),
    sa.Column('address_line2', sa.String(length=100), nullable=True),
    sa.Column('address_line3', sa.String(length=100), nullable=True),
    sa.Column('city', sa.String(length=25), nullable=True),
    sa.Column('district', sa.String(length=15), nullable=True),
    sa.Column('state', sa.String(length=15), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('created_by', sa.String(length=25), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('updated_by', sa.String(length=25), nullable=True),
    sa.ForeignKeyConstraint(['patient_id'], ['patient.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_address_address_line1'), 'address', ['address_line1'], unique=False)
    op.create_index(op.f('ix_address_address_line2'), 'address', ['address_line2'], unique=False)
    op.create_index(op.f('ix_address_address_line3'), 'address', ['address_line3'], unique=False)
    op.create_index(op.f('ix_address_city'), 'address', ['city'], unique=False)
    op.create_index(op.f('ix_address_created_by'), 'address', ['created_by'], unique=False)
    op.create_index(op.f('ix_address_district'), 'address', ['district'], unique=False)
    op.create_index(op.f('ix_address_id'), 'address', ['id'], unique=False)
    op.create_index(op.f('ix_address_state'), 'address', ['state'], unique=False)
    op.create_index(op.f('ix_address_updated_by'), 'address', ['updated_by'], unique=False)
    op.create_table('patient_audit_trail',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('patient_id', sa.Integer(), nullable=True),
    sa.Column('action', sa.String(length=25), nullable=True),
    sa.Column('data', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('created_by', sa.String(length=25), nullable=True),
    sa.ForeignKeyConstraint(['patient_id'], ['patient.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_patient_audit_trail_action'), 'patient_audit_trail', ['action'], unique=False)
    op.create_index(op.f('ix_patient_audit_trail_created_by'), 'patient_audit_trail', ['created_by'], unique=False)
    op.create_index(op.f('ix_patient_audit_trail_id'), 'patient_audit_trail', ['id'], unique=False)
    op.create_table('patient_document_master',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('document_master_id', sa.Integer(), nullable=True),
    sa.Column('is_required', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['document_master_id'], ['document_master.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_patient_document_master_document_master_id'), 'patient_document_master', ['document_master_id'], unique=False)
    op.create_index(op.f('ix_patient_document_master_id'), 'patient_document_master', ['id'], unique=False)
    op.create_table('patient_document',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('document_no', sa.String(length=25), nullable=True),
    sa.Column('patient_id', sa.Integer(), nullable=True),
    sa.Column('guid', sa.Integer(), nullable=True),
    sa.Column('patient_document_master_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['patient_document_master_id'], ['patient_document_master.id'], ),
    sa.ForeignKeyConstraint(['patient_id'], ['patient.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_patient_document_document_no'), 'patient_document', ['document_no'], unique=False)
    op.create_index(op.f('ix_patient_document_guid'), 'patient_document', ['guid'], unique=True)
    op.create_index(op.f('ix_patient_document_id'), 'patient_document', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_patient_document_id'), table_name='patient_document')
    op.drop_index(op.f('ix_patient_document_guid'), table_name='patient_document')
    op.drop_index(op.f('ix_patient_document_document_no'), table_name='patient_document')
    op.drop_table('patient_document')
    op.drop_index(op.f('ix_patient_document_master_id'), table_name='patient_document_master')
    op.drop_index(op.f('ix_patient_document_master_document_master_id'), table_name='patient_document_master')
    op.drop_table('patient_document_master')
    op.drop_index(op.f('ix_patient_audit_trail_id'), table_name='patient_audit_trail')
    op.drop_index(op.f('ix_patient_audit_trail_created_by'), table_name='patient_audit_trail')
    op.drop_index(op.f('ix_patient_audit_trail_action'), table_name='patient_audit_trail')
    op.drop_table('patient_audit_trail')
    op.drop_index(op.f('ix_address_updated_by'), table_name='address')
    op.drop_index(op.f('ix_address_state'), table_name='address')
    op.drop_index(op.f('ix_address_id'), table_name='address')
    op.drop_index(op.f('ix_address_district'), table_name='address')
    op.drop_index(op.f('ix_address_created_by'), table_name='address')
    op.drop_index(op.f('ix_address_city'), table_name='address')
    op.drop_index(op.f('ix_address_address_line3'), table_name='address')
    op.drop_index(op.f('ix_address_address_line2'), table_name='address')
    op.drop_index(op.f('ix_address_address_line1'), table_name='address')
    op.drop_table('address')
    op.drop_index(op.f('ix_patient_updated_by'), table_name='patient')
    op.drop_index(op.f('ix_patient_timestamp_id'), table_name='patient')
    op.drop_index(op.f('ix_patient_relative_phone_number'), table_name='patient')
    op.drop_index(op.f('ix_patient_relative_name'), table_name='patient')
    op.drop_index(op.f('ix_patient_ration_card_number'), table_name='patient')
    op.drop_index(op.f('ix_patient_phone_number'), table_name='patient')
    op.drop_index(op.f('ix_patient_middle_name'), table_name='patient')
    op.drop_index(op.f('ix_patient_last_name'), table_name='patient')
    op.drop_index(op.f('ix_patient_id'), table_name='patient')
    op.drop_index(op.f('ix_patient_gender'), table_name='patient')
    op.drop_index(op.f('ix_patient_first_name'), table_name='patient')
    op.drop_index(op.f('ix_patient_date_of_birth'), table_name='patient')
    op.drop_index(op.f('ix_patient_created_by'), table_name='patient')
    op.drop_index(op.f('ix_patient_alternate_phone_number'), table_name='patient')
    op.drop_index(op.f('ix_patient_aadhar_number'), table_name='patient')
    op.drop_table('patient')
    # ### end Alembic commands ###
