"""Updated patient createdby and updatedby relationship; added unique constraint to aadhar no.

Revision ID: 4db61f9e81b5
Revises: 2754d6a10936
Create Date: 2024-06-06 23:18:56.578393

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '4db61f9e81b5'
down_revision: Union[str, None] = '2754d6a10936'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('patient', 'created_by',
               existing_type=mysql.VARCHAR(length=25),
               type_=sa.Integer(),
               existing_nullable=True)
    op.alter_column('patient', 'updated_by',
               existing_type=mysql.VARCHAR(length=25),
               type_=sa.Integer(),
               existing_nullable=True)
    op.drop_index('ix_patient_created_by', table_name='patient')
    op.drop_index('ix_patient_updated_by', table_name='patient')
    op.drop_index('ix_patient_aadhar_number', table_name='patient')
    op.create_index(op.f('ix_patient_aadhar_number'), 'patient', ['aadhar_number'], unique=True)
    op.create_foreign_key(None, 'patient', 'users', ['created_by'], ['id'])
    op.create_foreign_key(None, 'patient', 'users', ['updated_by'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'patient', type_='foreignkey')
    op.drop_constraint(None, 'patient', type_='foreignkey')
    op.drop_index(op.f('ix_patient_aadhar_number'), table_name='patient')
    op.create_index('ix_patient_aadhar_number', 'patient', ['aadhar_number'], unique=False)
    op.create_index('ix_patient_updated_by', 'patient', ['updated_by'], unique=False)
    op.create_index('ix_patient_created_by', 'patient', ['created_by'], unique=False)
    op.alter_column('patient', 'updated_by',
               existing_type=sa.Integer(),
               type_=mysql.VARCHAR(length=25),
               existing_nullable=True)
    op.alter_column('patient', 'created_by',
               existing_type=sa.Integer(),
               type_=mysql.VARCHAR(length=25),
               existing_nullable=True)
    # ### end Alembic commands ###
