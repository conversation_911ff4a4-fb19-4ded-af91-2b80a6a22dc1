"""user and authorisation models migrated

Revision ID: 0a686e8fbb91
Revises: 46125968afe1
Create Date: 2024-05-24 17:40:48.117558

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0a686e8fbb91'
down_revision: Union[str, None] = '46125968afe1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('groups',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index('idx_name', 'groups', ['name'], unique=True)
    op.create_index(op.f('ix_groups_id'), 'groups', ['id'], unique=False)
    op.create_table('permissions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_permissions_id'), 'permissions', ['id'], unique=False)
    op.create_table('roles',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_roles_id'), 'roles', ['id'], unique=False)
    op.create_table('users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('first_name', sa.String(length=25), nullable=True),
    sa.Column('last_name', sa.String(length=25), nullable=True),
    sa.Column('email', sa.String(length=255), nullable=True),
    sa.Column('password', sa.String(length=250), nullable=True),
    sa.Column('mobile_number', sa.String(length=25), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=False)
    op.create_index(op.f('ix_users_first_name'), 'users', ['first_name'], unique=False)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_last_name'), 'users', ['last_name'], unique=False)
    op.create_index(op.f('ix_users_mobile_number'), 'users', ['mobile_number'], unique=False)
    op.create_index(op.f('ix_users_password'), 'users', ['password'], unique=False)
    op.create_table('endpoints',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('url', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('permission_id', sa.Integer(), nullable=True),
    sa.Column('method', sa.Enum('GET', 'POST', 'PUT', 'PATCH', 'DELETE', name='httpmethod'), nullable=False),
    sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index('idx_name_permission_id', 'endpoints', ['name', 'permission_id'], unique=False)
    op.create_index(op.f('ix_endpoints_id'), 'endpoints', ['id'], unique=False)
    op.create_index(op.f('ix_endpoints_permission_id'), 'endpoints', ['permission_id'], unique=False)
    op.create_table('group_permission',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('group_id', sa.Integer(), nullable=True),
    sa.Column('permission_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['group_id'], ['groups.id'], ),
    sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_group_permission', 'group_permission', ['group_id', 'permission_id'], unique=True)
    op.create_table('group_role',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('group_id', sa.Integer(), nullable=True),
    sa.Column('role_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['group_id'], ['groups.id'], ),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_group_role', 'group_role', ['group_id', 'role_id'], unique=True)
    op.create_index(op.f('ix_group_role_group_id'), 'group_role', ['group_id'], unique=False)
    op.create_index(op.f('ix_group_role_id'), 'group_role', ['id'], unique=False)
    op.create_index(op.f('ix_group_role_role_id'), 'group_role', ['role_id'], unique=False)
    op.create_table('otp',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('mobile_number', sa.String(length=25), nullable=True),
    sa.Column('otp', sa.String(length=6), nullable=True),
    sa.Column('timestamp_id', sa.Integer(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('expire_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_otp_id'), 'otp', ['id'], unique=False)
    op.create_index(op.f('ix_otp_mobile_number'), 'otp', ['mobile_number'], unique=False)
    op.create_index(op.f('ix_otp_otp'), 'otp', ['otp'], unique=False)
    op.create_index(op.f('ix_otp_timestamp_id'), 'otp', ['timestamp_id'], unique=False)
    op.create_index(op.f('ix_otp_user_id'), 'otp', ['user_id'], unique=False)
    op.create_table('user_hospital',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('hospital_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['hospital_id'], ['hospital_master.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_hospital_hospital_id'), 'user_hospital', ['hospital_id'], unique=False)
    op.create_index(op.f('ix_user_hospital_id'), 'user_hospital', ['id'], unique=False)
    op.create_index(op.f('ix_user_hospital_user_id'), 'user_hospital', ['user_id'], unique=False)
    op.create_table('user_role',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('role_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_user_role', 'user_role', ['user_id', 'role_id'], unique=True)
    op.create_index(op.f('ix_user_role_id'), 'user_role', ['id'], unique=False)
    op.create_index(op.f('ix_user_role_role_id'), 'user_role', ['role_id'], unique=False)
    op.create_index(op.f('ix_user_role_user_id'), 'user_role', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_role_user_id'), table_name='user_role')
    op.drop_index(op.f('ix_user_role_role_id'), table_name='user_role')
    op.drop_index(op.f('ix_user_role_id'), table_name='user_role')
    op.drop_index('idx_user_role', table_name='user_role')
    op.drop_table('user_role')
    op.drop_index(op.f('ix_user_hospital_user_id'), table_name='user_hospital')
    op.drop_index(op.f('ix_user_hospital_id'), table_name='user_hospital')
    op.drop_index(op.f('ix_user_hospital_hospital_id'), table_name='user_hospital')
    op.drop_table('user_hospital')
    op.drop_index(op.f('ix_otp_user_id'), table_name='otp')
    op.drop_index(op.f('ix_otp_timestamp_id'), table_name='otp')
    op.drop_index(op.f('ix_otp_otp'), table_name='otp')
    op.drop_index(op.f('ix_otp_mobile_number'), table_name='otp')
    op.drop_index(op.f('ix_otp_id'), table_name='otp')
    op.drop_table('otp')
    op.drop_index(op.f('ix_group_role_role_id'), table_name='group_role')
    op.drop_index(op.f('ix_group_role_id'), table_name='group_role')
    op.drop_index(op.f('ix_group_role_group_id'), table_name='group_role')
    op.drop_index('idx_group_role', table_name='group_role')
    op.drop_table('group_role')
    op.drop_index('idx_group_permission', table_name='group_permission')
    op.drop_table('group_permission')
    op.drop_index(op.f('ix_endpoints_permission_id'), table_name='endpoints')
    op.drop_index(op.f('ix_endpoints_id'), table_name='endpoints')
    op.drop_index('idx_name_permission_id', table_name='endpoints')
    op.drop_table('endpoints')
    op.drop_index(op.f('ix_users_password'), table_name='users')
    op.drop_index(op.f('ix_users_mobile_number'), table_name='users')
    op.drop_index(op.f('ix_users_last_name'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_first_name'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_roles_id'), table_name='roles')
    op.drop_table('roles')
    op.drop_index(op.f('ix_permissions_id'), table_name='permissions')
    op.drop_table('permissions')
    op.drop_index(op.f('ix_groups_id'), table_name='groups')
    op.drop_index('idx_name', table_name='groups')
    op.drop_table('groups')
    # ### end Alembic commands ###
