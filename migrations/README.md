# Migrations README

This README provides instructions on how to perform database migrations using the Alembic library and configure the model detection settings.

## Prerequisites

Before you can perform migrations, make sure you have the following prerequisites installed:

- Python (version 3.6 or higher)
- Alembic library (install using `pip install alembic`)
- SQLAlchemy library (install using `pip install sqlalchemy`)

## Configuration

1. **Create a new directory called `migrations`** in your project's root directory.

2. **Inside the `migrations` directory, create a new file called `alembic.ini`** and add the following content:

    ```ini
    [alembic]
    script_location = alembic
    sqlalchemy.url = <database_connection_string>
    ```

    Replace `<database_connection_string>` with the connection string for your database.

3. **Create a new directory called `alembic`** inside the `migrations` directory.

4. **Inside the `alembic` directory, create a new file called `env.py`** and add the following content:

    ```python
    from sqlalchemy import engine_from_config
    from sqlalchemy import pool

    from alembic import context

    # Import your models here
    from your_app.models import *

    # Configure the database connection
    config = context.config
    config.set_main_option('sqlalchemy.url', '<database_connection_string>')

    # Detect models for autogenerate feature
    target_metadata = Base.metadata

    def run_migrations_offline():
         url = config.get_main_option("sqlalchemy.url")
         context.configure(
              url=url,
              target_metadata=target_metadata,
              literal_binds=True,
              dialect_opts={"paramstyle": "named"},
              compare_type=True,
         )

         with context.begin_transaction():
              context.run_migrations()

    def run_migrations_online():
         connectable = engine_from_config(
              config.get_section(config.config_ini_section),
              prefix="sqlalchemy.",
              poolclass=pool.NullPool,
         )

         with connectable.connect() as connection:
              context.configure(
                    connection=connection,
                    target_metadata=target_metadata,
                    compare_type=True,
              )

              with context.begin_transaction():
                    context.run_migrations()

    if context.is_offline_mode():
         run_migrations_offline()
    else:
         run_migrations_online()
    ```

    Replace `<database_connection_string>` with the connection string for your database. Make sure to import your model files inside this file to enable model detection.

## Performing Migrations

To perform migrations, follow these steps:

1. **Open a terminal or command prompt and navigate to the root directory** of your project.

2. **Run the following command to generate an initial migration script**:

    ```shell
    alembic revision --autogenerate -m "Initial migration"
    ```

    This will create a new migration script in the `alembic/versions` directory.

3. **Review the generated migration script and make any necessary modifications**.

4. **Run the following command to apply the migration**:

    ```shell
    alembic upgrade head
    ```

    This will apply the migration to your database.

5. **Repeat steps 2-4 whenever you need to make changes to your database schema**.

That's it! You now know how to perform migrations using the Alembic library, configure model detection settings, and import your model files within the `env.py` file for proper model detection. Happy coding!
